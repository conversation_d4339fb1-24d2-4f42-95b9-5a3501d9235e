<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create permissions
        $permissions = [
            // Product permissions
            'view products',
            'create products',
            'edit products',
            'delete products',
            'import products',
            'export products',
            
            // Inventory permissions
            'view inventory',
            'adjust inventory',
            'transfer inventory',
            
            // Sales permissions
            'view sales',
            'create sales',
            'refund sales',
            'view all sales',
            
            // Customer permissions
            'view customers',
            'create customers',
            'edit customers',
            'delete customers',
            
            // Supplier permissions
            'view suppliers',
            'create suppliers',
            'edit suppliers',
            'delete suppliers',
            
            // Purchase order permissions
            'view purchase orders',
            'create purchase orders',
            'edit purchase orders',
            'delete purchase orders',
            'receive purchase orders',
            
            // Report permissions
            'view reports',
            'export reports',
            
            // User management permissions
            'view users',
            'create users',
            'edit users',
            'delete users',
            
            // Store management permissions
            'view stores',
            'create stores',
            'edit stores',
            'delete stores',
            
            // Settings permissions
            'view settings',
            'edit settings',
            
            // Integration permissions
            'view integrations',
            'edit integrations',
            'sync integrations',
        ];

        foreach ($permissions as $permission) {
            Permission::create(['name' => $permission]);
        }

        // Create roles and assign permissions
        $adminRole = Role::create(['name' => 'admin']);
        $adminRole->givePermissionTo(Permission::all());

        $managerRole = Role::create(['name' => 'manager']);
        $managerRole->givePermissionTo([
            'view products', 'create products', 'edit products', 'import products', 'export products',
            'view inventory', 'adjust inventory', 'transfer inventory',
            'view sales', 'create sales', 'refund sales', 'view all sales',
            'view customers', 'create customers', 'edit customers',
            'view suppliers', 'create suppliers', 'edit suppliers',
            'view purchase orders', 'create purchase orders', 'edit purchase orders', 'receive purchase orders',
            'view reports', 'export reports',
            'view users', 'create users', 'edit users',
            'view stores',
            'view settings',
            'view integrations', 'sync integrations',
        ]);

        $cashierRole = Role::create(['name' => 'cashier']);
        $cashierRole->givePermissionTo([
            'view products',
            'view inventory',
            'view sales', 'create sales',
            'view customers', 'create customers', 'edit customers',
        ]);
    }
}
