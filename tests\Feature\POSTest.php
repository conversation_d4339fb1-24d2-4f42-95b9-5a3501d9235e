<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Store;
use App\Models\Product;
use App\Models\Customer;
use App\Models\Sale;
use App\Models\Category;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class POSTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected User $user;
    protected Store $store;
    protected Product $product;
    protected Customer $customer;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->store = Store::factory()->create();
        $this->product = Product::factory()->create([
            'category_id' => Category::factory()->create()->id,
            'price' => 10.00,
            'cost_price' => 5.00,
        ]);
        $this->customer = Customer::factory()->create();

        // Assign user to store
        $this->store->users()->attach($this->user->id, ['is_primary' => true]);

        // Add product to store with stock
        $this->store->products()->attach($this->product->id, [
            'stock_quantity' => 100,
            'min_stock_level' => 10,
            'max_stock_level' => 200,
        ]);

        // Create roles if using Spatie Permission
        if (class_exists(\Spatie\Permission\Models\Role::class)) {
            $cashierRole = \Spatie\Permission\Models\Role::create(['name' => 'cashier']);
            $this->user->assignRole($cashierRole);
        }
    }

    public function test_user_can_access_pos_interface()
    {
        $response = $this->actingAs($this->user)
            ->get(route('pos.index'));

        $response->assertStatus(200);
        $response->assertViewIs('pos.index');
    }

    public function test_user_can_search_products_in_pos()
    {
        $response = $this->actingAs($this->user)
            ->get(route('pos.search-products', ['q' => $this->product->name]));

        $response->assertStatus(200);
        $response->assertJsonFragment([
            'id' => $this->product->id,
            'name' => $this->product->name,
            'price' => $this->product->price,
        ]);
    }

    public function test_user_can_search_customers_in_pos()
    {
        $response = $this->actingAs($this->user)
            ->get(route('pos.search-customers', ['q' => $this->customer->name]));

        $response->assertStatus(200);
        $response->assertJsonFragment([
            'id' => $this->customer->id,
            'name' => $this->customer->name,
            'email' => $this->customer->email,
        ]);
    }

    public function test_user_can_create_sale_via_api()
    {
        $saleData = [
            'store_id' => $this->store->id,
            'customer_id' => $this->customer->id,
            'items' => [
                [
                    'product_id' => $this->product->id,
                    'quantity' => 2,
                    'price' => $this->product->price,
                    'tax_rate' => 10,
                ]
            ],
            'discount_amount' => 0,
            'payment_method' => 'cash',
            'payments' => [
                [
                    'method' => 'cash',
                    'amount' => 22.00, // 2 * 10.00 + 10% tax
                ]
            ],
            'notes' => 'Test sale',
        ];

        $response = $this->actingAs($this->user)
            ->postJson(route('api.sales.store'), $saleData);

        $response->assertStatus(201);
        $response->assertJsonStructure([
            'message',
            'data' => [
                'id',
                'sale_number',
                'total_amount',
                'payment_status',
                'status',
            ],
            'change',
        ]);

        // Verify sale was created
        $this->assertDatabaseHas('sales', [
            'store_id' => $this->store->id,
            'customer_id' => $this->customer->id,
            'cashier_id' => $this->user->id,
            'payment_status' => 'paid',
            'status' => 'completed',
        ]);

        // Verify sale items were created
        $sale = Sale::latest()->first();
        $this->assertDatabaseHas('sale_items', [
            'sale_id' => $sale->id,
            'product_id' => $this->product->id,
            'quantity' => 2,
            'price' => $this->product->price,
        ]);

        // Verify inventory was updated
        $this->store->products()->updateExistingPivot($this->product->id, []);
        $updatedStock = $this->store->getProductStock($this->product);
        $this->assertEquals(98, $updatedStock); // 100 - 2
    }

    public function test_sale_fails_with_insufficient_stock()
    {
        // Set low stock
        $this->store->products()->updateExistingPivot($this->product->id, [
            'stock_quantity' => 1,
        ]);

        $saleData = [
            'store_id' => $this->store->id,
            'items' => [
                [
                    'product_id' => $this->product->id,
                    'quantity' => 5, // More than available
                    'price' => $this->product->price,
                ]
            ],
            'payment_method' => 'cash',
            'payments' => [
                [
                    'method' => 'cash',
                    'amount' => 50.00,
                ]
            ],
        ];

        $response = $this->actingAs($this->user)
            ->postJson(route('api.sales.store'), $saleData);

        $response->assertStatus(400);
        $response->assertJsonFragment([
            'message' => "Insufficient stock for {$this->product->name}.",
        ]);
    }

    public function test_sale_fails_with_insufficient_payment()
    {
        $saleData = [
            'store_id' => $this->store->id,
            'items' => [
                [
                    'product_id' => $this->product->id,
                    'quantity' => 2,
                    'price' => $this->product->price,
                ]
            ],
            'payment_method' => 'cash',
            'payments' => [
                [
                    'method' => 'cash',
                    'amount' => 15.00, // Less than total (20.00)
                ]
            ],
        ];

        $response = $this->actingAs($this->user)
            ->postJson(route('api.sales.store'), $saleData);

        $response->assertStatus(400);
        $response->assertJsonFragment([
            'message' => 'Insufficient payment amount.',
        ]);
    }

    public function test_customer_loyalty_points_are_updated()
    {
        $initialPoints = $this->customer->loyalty_points;

        $saleData = [
            'store_id' => $this->store->id,
            'customer_id' => $this->customer->id,
            'items' => [
                [
                    'product_id' => $this->product->id,
                    'quantity' => 1,
                    'price' => 100.00, // $100 should give 10 points
                ]
            ],
            'payment_method' => 'cash',
            'payments' => [
                [
                    'method' => 'cash',
                    'amount' => 100.00,
                ]
            ],
        ];

        $response = $this->actingAs($this->user)
            ->postJson(route('api.sales.store'), $saleData);

        $response->assertStatus(201);

        // Check loyalty points were added (1 point per $10 spent)
        $this->customer->refresh();
        $this->assertEquals($initialPoints + 10, $this->customer->loyalty_points);
    }

    public function test_user_can_process_refund()
    {
        // Create a sale first
        $sale = Sale::factory()->create([
            'store_id' => $this->store->id,
            'customer_id' => $this->customer->id,
            'cashier_id' => $this->user->id,
            'total_amount' => 20.00,
            'payment_status' => 'paid',
            'status' => 'completed',
        ]);

        $saleItem = $sale->items()->create([
            'product_id' => $this->product->id,
            'quantity' => 2,
            'price' => 10.00,
            'cost_price' => 5.00,
            'total' => 20.00,
        ]);

        $refundData = [
            'items' => [
                [
                    'sale_item_id' => $saleItem->id,
                    'quantity' => 1,
                ]
            ],
            'reason' => 'Customer request',
            'notes' => 'Product defective',
        ];

        $response = $this->actingAs($this->user)
            ->postJson(route('api.sales.refund', $sale), $refundData);

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'message',
            'data' => [
                'refund_amount',
                'sale_status',
            ],
        ]);

        // Verify refund was recorded
        $this->assertDatabaseHas('sale_refunds', [
            'sale_id' => $sale->id,
            'sale_item_id' => $saleItem->id,
            'quantity' => 1,
            'reason' => 'Customer request',
        ]);

        // Verify stock was returned
        $updatedStock = $this->store->getProductStock($this->product);
        $this->assertEquals(101, $updatedStock); // 100 + 1 returned
    }

    public function test_barcode_scanning_works()
    {
        $this->product->update(['barcode' => '1234567890123']);

        $response = $this->actingAs($this->user)
            ->get(route('pos.scan-barcode', ['barcode' => '1234567890123']));

        $response->assertStatus(200);
        $response->assertJsonFragment([
            'id' => $this->product->id,
            'name' => $this->product->name,
            'barcode' => '1234567890123',
        ]);
    }

    public function test_pos_validates_store_access()
    {
        $otherStore = Store::factory()->create();
        
        $saleData = [
            'store_id' => $otherStore->id, // User doesn't have access to this store
            'items' => [
                [
                    'product_id' => $this->product->id,
                    'quantity' => 1,
                    'price' => $this->product->price,
                ]
            ],
            'payment_method' => 'cash',
            'payments' => [
                [
                    'method' => 'cash',
                    'amount' => 10.00,
                ]
            ],
        ];

        $response = $this->actingAs($this->user)
            ->postJson(route('api.sales.store'), $saleData);

        $response->assertStatus(403);
        $response->assertJsonFragment([
            'message' => 'You do not have access to this store.',
        ]);
    }
}
