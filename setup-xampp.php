<?php

/**
 * SyncPOS XAMPP Setup Script
 * This script helps set up SyncPOS on XAMPP for local development
 */

echo "========================================\n";
echo "SyncPOS XAMPP Setup Script\n";
echo "========================================\n\n";

// Check if we're running from the correct directory
if (!file_exists('composer.json')) {
    echo "❌ Error: composer.json not found. Please run this script from the SyncPOS root directory.\n";
    exit(1);
}

// Check PHP version
$phpVersion = PHP_VERSION;
echo "📋 Checking PHP version: {$phpVersion}\n";
if (version_compare($phpVersion, '8.2.0', '<')) {
    echo "❌ Error: PHP 8.2 or higher is required. Current version: {$phpVersion}\n";
    exit(1);
}
echo "✅ PHP version is compatible\n\n";

// Check required PHP extensions
$requiredExtensions = [
    'curl', 'fileinfo', 'gd', 'mbstring', 'openssl', 'pdo_mysql', 'zip', 'intl'
];

echo "📋 Checking PHP extensions:\n";
$missingExtensions = [];
foreach ($requiredExtensions as $extension) {
    if (extension_loaded($extension)) {
        echo "✅ {$extension}\n";
    } else {
        echo "❌ {$extension} (missing)\n";
        $missingExtensions[] = $extension;
    }
}

if (!empty($missingExtensions)) {
    echo "\n❌ Missing required PHP extensions. Please enable them in php.ini:\n";
    foreach ($missingExtensions as $ext) {
        echo "   extension={$ext}\n";
    }
    echo "\nThen restart Apache and run this script again.\n";
    exit(1);
}

echo "\n✅ All required PHP extensions are loaded\n\n";

// Check if Composer is available
echo "📋 Checking Composer...\n";
$composerCheck = shell_exec('composer --version 2>&1');
if (strpos($composerCheck, 'Composer') === false) {
    echo "❌ Composer not found. Please install Composer from https://getcomposer.org/\n";
    exit(1);
}
echo "✅ Composer is available\n\n";

// Check if Node.js is available
echo "📋 Checking Node.js...\n";
$nodeCheck = shell_exec('node --version 2>&1');
if (empty($nodeCheck) || strpos($nodeCheck, 'v') !== 0) {
    echo "❌ Node.js not found. Please install Node.js from https://nodejs.org/\n";
    exit(1);
}
echo "✅ Node.js is available: " . trim($nodeCheck) . "\n\n";

// Create .env file if it doesn't exist
echo "📋 Setting up environment file...\n";
if (!file_exists('.env')) {
    if (file_exists('.env.xampp')) {
        copy('.env.xampp', '.env');
        echo "✅ Created .env from .env.xampp\n";
    } elseif (file_exists('.env.example')) {
        copy('.env.example', '.env');
        echo "✅ Created .env from .env.example\n";
    } else {
        echo "❌ No .env.example or .env.xampp file found\n";
        exit(1);
    }
} else {
    echo "✅ .env file already exists\n";
}

// Create storage directories
echo "\n📋 Creating storage directories...\n";
$storageDirs = [
    'storage/app/public',
    'storage/framework/cache',
    'storage/framework/sessions',
    'storage/framework/views',
    'storage/logs',
    'bootstrap/cache'
];

foreach ($storageDirs as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
        echo "✅ Created {$dir}\n";
    } else {
        echo "✅ {$dir} already exists\n";
    }
}

// Check database connection
echo "\n📋 Checking database connection...\n";
$envContent = file_get_contents('.env');
preg_match('/DB_HOST=(.*)/', $envContent, $hostMatch);
preg_match('/DB_DATABASE=(.*)/', $envContent, $dbMatch);
preg_match('/DB_USERNAME=(.*)/', $envContent, $userMatch);
preg_match('/DB_PASSWORD=(.*)/', $envContent, $passMatch);

$host = trim($hostMatch[1] ?? '127.0.0.1');
$database = trim($dbMatch[1] ?? 'syncpos_landlord');
$username = trim($userMatch[1] ?? 'root');
$password = trim($passMatch[1] ?? '');

try {
    $pdo = new PDO("mysql:host={$host}", $username, $password);
    echo "✅ Database connection successful\n";
    
    // Check if database exists
    $stmt = $pdo->query("SHOW DATABASES LIKE '{$database}'");
    if ($stmt->rowCount() == 0) {
        echo "📋 Creating database '{$database}'...\n";
        $pdo->exec("CREATE DATABASE `{$database}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        echo "✅ Database '{$database}' created\n";
    } else {
        echo "✅ Database '{$database}' already exists\n";
    }
} catch (PDOException $e) {
    echo "❌ Database connection failed: " . $e->getMessage() . "\n";
    echo "Please make sure MySQL is running in XAMPP and check your database credentials in .env\n";
    exit(1);
}

echo "\n========================================\n";
echo "✅ Setup completed successfully!\n";
echo "========================================\n\n";

echo "Next steps:\n";
echo "1. Run: composer install\n";
echo "2. Run: npm install\n";
echo "3. Run: php artisan key:generate\n";
echo "4. Run: php artisan migrate\n";
echo "5. Run: npm run build\n";
echo "6. Access your application at: http://localhost/syncpos/public\n\n";

echo "Or run the automated installation script:\n";
echo "Windows: install-xampp.bat\n";
echo "Manual: Follow the commands above\n\n";

echo "📖 For detailed instructions, see XAMPP-SETUP.md\n";
