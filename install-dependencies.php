<?php
/**
 * SyncPOS Dependency Installer
 * This script helps install required dependencies
 */

echo "<h1>📦 SyncPOS Dependency Installer</h1>";
echo "<style>body { font-family: Arial, sans-serif; margin: 20px; } .success { color: green; } .error { color: red; } .warning { color: orange; } .info { color: blue; }</style>";

// Check if we're in the right directory
if (!file_exists('composer.json')) {
    echo "<p class='error'>❌ Error: composer.json not found. Please run this script from the SyncPOS root directory.</p>";
    exit;
}

echo "<h2>🔍 Checking Dependencies</h2>";

// Check if vendor directory exists
if (file_exists('vendor/autoload.php')) {
    echo "<p class='success'>✅ Composer dependencies are already installed!</p>";
    echo "<p class='info'>You can proceed with the installation: <a href='public/install'>Click here to install</a></p>";
} else {
    echo "<p class='error'>❌ Composer dependencies not found</p>";
    
    // Check if Composer is available
    exec('composer --version 2>&1', $output, $return_code);
    
    if ($return_code === 0) {
        echo "<p class='success'>✅ Composer is available</p>";
        echo "<p class='info'>Installing dependencies...</p>";
        
        // Try to install dependencies
        echo "<div style='background: #f0f0f0; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
        echo "<h3>Running: composer install</h3>";
        
        $command = 'composer install --no-dev --optimize-autoloader 2>&1';
        $output = [];
        exec($command, $output, $return_code);
        
        echo "<pre style='background: #000; color: #0f0; padding: 10px; overflow-x: auto;'>";
        echo htmlspecialchars(implode("\n", $output));
        echo "</pre>";
        echo "</div>";
        
        if ($return_code === 0 && file_exists('vendor/autoload.php')) {
            echo "<p class='success'>🎉 Dependencies installed successfully!</p>";
            echo "<p class='info'>You can now proceed with the installation: <a href='public/install'>Click here to install</a></p>";
        } else {
            echo "<p class='error'>❌ Failed to install dependencies automatically</p>";
            showManualInstructions();
        }
    } else {
        echo "<p class='error'>❌ Composer is not installed or not in PATH</p>";
        showComposerInstallInstructions();
    }
}

function showComposerInstallInstructions() {
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
    echo "<h3>📥 Install Composer First</h3>";
    echo "<p><strong>Option 1: Download Composer for Windows</strong></p>";
    echo "<ol>";
    echo "<li>Visit: <a href='https://getcomposer.org/download/' target='_blank'>https://getcomposer.org/download/</a></li>";
    echo "<li>Download and run <strong>Composer-Setup.exe</strong></li>";
    echo "<li>Follow the installation wizard</li>";
    echo "<li>Restart your command prompt</li>";
    echo "<li>Come back and refresh this page</li>";
    echo "</ol>";
    
    echo "<p><strong>Option 2: Manual Installation</strong></p>";
    echo "<ol>";
    echo "<li>Download: <a href='https://getcomposer.org/composer.phar' target='_blank'>composer.phar</a></li>";
    echo "<li>Place it in your SyncPOS directory</li>";
    echo "<li>Run: <code>php composer.phar install</code></li>";
    echo "</ol>";
    echo "</div>";
}

function showManualInstructions() {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
    echo "<h3>🔧 Manual Installation Required</h3>";
    echo "<p><strong>Please run these commands in Command Prompt:</strong></p>";
    echo "<ol>";
    echo "<li>Open Command Prompt (Windows + R, type 'cmd', press Enter)</li>";
    echo "<li>Navigate to SyncPOS directory:</li>";
    echo "<code style='background: #000; color: #fff; padding: 5px; display: block; margin: 5px 0;'>cd C:\\xampp\\htdocs\\syncpos</code>";
    echo "<li>Install dependencies:</li>";
    echo "<code style='background: #000; color: #fff; padding: 5px; display: block; margin: 5px 0;'>composer install</code>";
    echo "<li>If that fails, try:</li>";
    echo "<code style='background: #000; color: #fff; padding: 5px; display: block; margin: 5px 0;'>composer install --ignore-platform-reqs</code>";
    echo "</ol>";
    echo "</div>";
}

// Check PHP version
echo "<h2>🔍 System Check</h2>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th style='padding: 8px; background: #f0f0f0;'>Check</th><th style='padding: 8px; background: #f0f0f0;'>Status</th><th style='padding: 8px; background: #f0f0f0;'>Value</th></tr>";

// PHP Version
$phpVersion = PHP_VERSION;
$phpOk = version_compare($phpVersion, '8.2.0', '>=');
echo "<tr><td style='padding: 8px;'>PHP Version</td><td style='padding: 8px;'>" . ($phpOk ? "✅ OK" : "❌ FAIL") . "</td><td style='padding: 8px;'>$phpVersion</td></tr>";

// Composer
exec('composer --version 2>&1', $composerOutput, $composerReturn);
$composerOk = $composerReturn === 0;
$composerVersion = $composerOk ? $composerOutput[0] : 'Not installed';
echo "<tr><td style='padding: 8px;'>Composer</td><td style='padding: 8px;'>" . ($composerOk ? "✅ OK" : "❌ FAIL") . "</td><td style='padding: 8px;'>$composerVersion</td></tr>";

// Vendor directory
$vendorOk = file_exists('vendor/autoload.php');
echo "<tr><td style='padding: 8px;'>Dependencies</td><td style='padding: 8px;'>" . ($vendorOk ? "✅ OK" : "❌ MISSING") . "</td><td style='padding: 8px;'>" . ($vendorOk ? "Installed" : "Not installed") . "</td></tr>";

// Storage permissions
$storageOk = is_writable('storage');
echo "<tr><td style='padding: 8px;'>Storage Writable</td><td style='padding: 8px;'>" . ($storageOk ? "✅ OK" : "❌ FAIL") . "</td><td style='padding: 8px;'>" . ($storageOk ? "Yes" : "No") . "</td></tr>";

echo "</table>";

if ($vendorOk) {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
    echo "<h3>🎉 Ready to Install!</h3>";
    echo "<p>All dependencies are installed. You can now proceed with the SyncPOS installation.</p>";
    echo "<p><a href='public/install' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🚀 Start Installation</a></p>";
    echo "</div>";
}

echo "<hr>";
echo "<p style='text-align: center; color: #666;'>";
echo "<strong>SyncPOS Dependency Installer</strong><br>";
echo "If you need help, please check the documentation or contact support.";
echo "</p>";
?>
