<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\Customer;
use App\Models\Sale;
use App\Models\SaleItem;
use App\Models\Store;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class PosController extends Controller
{
    public function index(Request $request)
    {
        $store = $request->user()->primaryStore() ?? Store::active()->first();
        
        if (!$store) {
            return redirect()->route('dashboard')
                ->with('error', 'No store assigned. Please contact your administrator.');
        }

        $products = Product::with(['category', 'brand'])
            ->whereHas('stores', function ($q) use ($store) {
                $q->where('store_id', $store->id)
                  ->where('stock_quantity', '>', 0);
            })
            ->active()
            ->limit(20)
            ->get();

        $customers = Customer::active()->limit(10)->get();
        
        return view('pos.index', compact('store', 'products', 'customers'));
    }

    public function addToCart(Request $request)
    {
        $validated = $request->validate([
            'product_id' => 'required|exists:products,id',
            'quantity' => 'required|integer|min:1',
            'store_id' => 'required|exists:stores,id',
        ]);

        $product = Product::findOrFail($validated['product_id']);
        $store = Store::findOrFail($validated['store_id']);

        // Check if product is available in store
        if (!$product->isInStock($store, $validated['quantity'])) {
            return response()->json([
                'success' => false,
                'message' => 'Insufficient stock available.',
            ], 400);
        }

        // Get or create cart session
        $cart = session()->get('pos_cart', []);
        $productKey = $product->id;

        if (isset($cart[$productKey])) {
            $cart[$productKey]['quantity'] += $validated['quantity'];
        } else {
            $cart[$productKey] = [
                'product_id' => $product->id,
                'name' => $product->name,
                'sku' => $product->sku,
                'price' => $product->price,
                'cost_price' => $product->cost_price,
                'tax_rate' => $product->tax_rate,
                'quantity' => $validated['quantity'],
                'image' => $product->image,
            ];
        }

        // Check total quantity doesn't exceed stock
        $totalQuantity = $cart[$productKey]['quantity'];
        if (!$product->isInStock($store, $totalQuantity)) {
            return response()->json([
                'success' => false,
                'message' => 'Total quantity exceeds available stock.',
            ], 400);
        }

        session()->put('pos_cart', $cart);

        return response()->json([
            'success' => true,
            'message' => 'Product added to cart.',
            'cart' => $cart,
            'cart_count' => count($cart),
            'cart_total' => $this->calculateCartTotal($cart),
        ]);
    }

    public function removeFromCart(Request $request)
    {
        $validated = $request->validate([
            'product_id' => 'required|exists:products,id',
        ]);

        $cart = session()->get('pos_cart', []);
        $productKey = $validated['product_id'];

        if (isset($cart[$productKey])) {
            unset($cart[$productKey]);
            session()->put('pos_cart', $cart);
        }

        return response()->json([
            'success' => true,
            'message' => 'Product removed from cart.',
            'cart' => $cart,
            'cart_count' => count($cart),
            'cart_total' => $this->calculateCartTotal($cart),
        ]);
    }

    public function checkout(Request $request)
    {
        $validated = $request->validate([
            'store_id' => 'required|exists:stores,id',
            'customer_id' => 'nullable|exists:customers,id',
            'payment_method' => 'required|in:cash,card,split',
            'discount_amount' => 'nullable|numeric|min:0',
            'notes' => 'nullable|string',
            'payments' => 'required|array',
            'payments.*.method' => 'required|in:cash,card',
            'payments.*.amount' => 'required|numeric|min:0',
        ]);

        $cart = session()->get('pos_cart', []);
        
        if (empty($cart)) {
            return response()->json([
                'success' => false,
                'message' => 'Cart is empty.',
            ], 400);
        }

        $store = Store::findOrFail($validated['store_id']);

        DB::beginTransaction();
        
        try {
            // Calculate totals
            $subtotal = $this->calculateCartSubtotal($cart);
            $taxAmount = $this->calculateCartTax($cart);
            $discountAmount = $validated['discount_amount'] ?? 0;
            $totalAmount = $subtotal + $taxAmount - $discountAmount;

            // Validate payment amounts
            $totalPayments = collect($validated['payments'])->sum('amount');
            if ($totalPayments < $totalAmount) {
                throw new \Exception('Payment amount is insufficient.');
            }

            // Create sale
            $sale = Sale::create([
                'sale_number' => Sale::generateSaleNumber(),
                'store_id' => $store->id,
                'customer_id' => $validated['customer_id'],
                'cashier_id' => auth()->id(),
                'subtotal' => $subtotal,
                'tax_amount' => $taxAmount,
                'discount_amount' => $discountAmount,
                'total_amount' => $totalAmount,
                'payment_method' => $validated['payment_method'],
                'payment_status' => 'paid',
                'status' => 'completed',
                'notes' => $validated['notes'],
                'receipt_data' => [
                    'payments' => $validated['payments'],
                    'change' => $totalPayments - $totalAmount,
                ],
            ]);

            // Create sale items and update inventory
            foreach ($cart as $item) {
                $product = Product::findOrFail($item['product_id']);
                
                // Check stock again
                if (!$product->isInStock($store, $item['quantity'])) {
                    throw new \Exception("Insufficient stock for {$product->name}.");
                }

                // Create sale item
                SaleItem::create([
                    'sale_id' => $sale->id,
                    'product_id' => $product->id,
                    'quantity' => $item['quantity'],
                    'price' => $item['price'],
                    'cost_price' => $item['cost_price'],
                    'tax_rate' => $item['tax_rate'],
                    'total' => $item['quantity'] * $item['price'],
                ]);

                // Update inventory
                $currentStock = $store->getProductStock($product);
                $newStock = $currentStock - $item['quantity'];
                
                $store->products()->updateExistingPivot($product->id, [
                    'stock_quantity' => $newStock,
                ]);
            }

            // Update customer loyalty points
            if ($validated['customer_id']) {
                $customer = Customer::find($validated['customer_id']);
                $points = floor($totalAmount / 10); // 1 point per $10 spent
                $customer->addLoyaltyPoints($points, "Sale #{$sale->sale_number}");
                $customer->updateTotalSpent();
            }

            // Clear cart
            session()->forget('pos_cart');

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Sale completed successfully.',
                'sale_id' => $sale->id,
                'sale_number' => $sale->sale_number,
                'total_amount' => $totalAmount,
                'change' => $totalPayments - $totalAmount,
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    public function receipt(Sale $sale)
    {
        $sale->load(['store', 'customer', 'cashier', 'items.product']);
        
        return view('pos.receipt', compact('sale'));
    }

    private function calculateCartTotal(array $cart): float
    {
        return $this->calculateCartSubtotal($cart) + $this->calculateCartTax($cart);
    }

    private function calculateCartSubtotal(array $cart): float
    {
        return collect($cart)->sum(function ($item) {
            return $item['quantity'] * $item['price'];
        });
    }

    private function calculateCartTax(array $cart): float
    {
        return collect($cart)->sum(function ($item) {
            $subtotal = $item['quantity'] * $item['price'];
            return $subtotal * ($item['tax_rate'] / 100);
        });
    }
}
