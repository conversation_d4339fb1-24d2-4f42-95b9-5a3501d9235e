<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Stancl\Tenancy\Events;
use Stancl\Tenancy\Jobs;
use Stancl\Tenancy\Listeners;
use Stancl\Tenancy\Middleware;

class TenancyServiceProvider extends ServiceProvider
{
    public function register()
    {
        //
    }

    public function boot()
    {
        $this->bootEvents();
    }

    protected function bootEvents()
    {
        // Tenant events
        Events\TenantCreated::class => [
            Jobs\CreateDatabase::class,
            Jobs\MigrateDatabase::class,
            Jobs\SeedDatabase::class,
        ];

        Events\TenantDeleted::class => [
            Jobs\DeleteDatabase::class,
        ];

        Events\TenantUpdated::class => [
            // Custom listeners for tenant updates
        ];

        // Domain events
        Events\DomainCreated::class => [
            // Custom listeners for domain creation
        ];

        Events\DomainDeleted::class => [
            // Custom listeners for domain deletion
        ];

        // Sync events
        Events\SyncedResourceSaved::class => [
            Listeners\InvalidateTenantCache::class,
        ];

        Events\SyncedResourceDeleted::class => [
            Listeners\InvalidateTenantCache::class,
        ];

        // Database events
        Events\DatabaseCreated::class => [
            Jobs\MigrateDatabase::class,
            Jobs\SeedDatabase::class,
        ];

        Events\DatabaseMigrated::class => [
            Jobs\SeedDatabase::class,
        ];

        Events\DatabaseDeleted::class => [
            // Custom listeners for database deletion
        ];

        // Tenancy events
        Events\TenancyInitialized::class => [
            Listeners\BootstrapTenancy::class,
        ];

        Events\TenancyEnded::class => [
            Listeners\RevertToCentralContext::class,
        ];

        // Register event listeners
        foreach ($this->events() as $event => $listeners) {
            foreach (array_unique($listeners) as $listener) {
                if (class_exists($listener)) {
                    \Event::listen($event, $listener);
                }
            }
        }
    }

    protected function events(): array
    {
        return [
            Events\TenantCreated::class => [
                Jobs\CreateDatabase::class,
                Jobs\MigrateDatabase::class,
                Jobs\SeedDatabase::class,
            ],
            Events\TenantDeleted::class => [
                Jobs\DeleteDatabase::class,
            ],
            Events\DatabaseCreated::class => [
                Jobs\MigrateDatabase::class,
                Jobs\SeedDatabase::class,
            ],
            Events\DatabaseMigrated::class => [
                Jobs\SeedDatabase::class,
            ],
            Events\TenancyInitialized::class => [
                Listeners\BootstrapTenancy::class,
            ],
            Events\TenancyEnded::class => [
                Listeners\RevertToCentralContext::class,
            ],
        ];
    }
}
