<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SaleRefund extends Model
{
    use HasFactory;

    protected $fillable = [
        'sale_id',
        'sale_item_id',
        'quantity',
        'amount',
        'reason',
        'notes',
        'processed_by',
    ];

    protected $casts = [
        'quantity' => 'integer',
        'amount' => 'decimal:2',
    ];

    /**
     * Get the sale that owns the refund.
     */
    public function sale()
    {
        return $this->belongsTo(Sale::class);
    }

    /**
     * Get the sale item that was refunded.
     */
    public function saleItem()
    {
        return $this->belongsTo(SaleItem::class);
    }

    /**
     * Get the user who processed the refund.
     */
    public function processedBy()
    {
        return $this->belongsTo(User::class, 'processed_by');
    }

    /**
     * Get the product that was refunded.
     */
    public function product()
    {
        return $this->hasOneThrough(Product::class, SaleItem::class, 'id', 'id', 'sale_item_id', 'product_id');
    }
}
