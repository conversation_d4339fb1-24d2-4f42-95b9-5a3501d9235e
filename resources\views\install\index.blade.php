<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SyncPOS Installation</title>
    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>
<body class="bg-gray-100">
    <div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
            <div>
                <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                    🚀 SyncPOS Installation
                </h2>
                <p class="mt-2 text-center text-sm text-gray-600">
                    Welcome! Let's get your POS system up and running.
                </p>
            </div>

            @if (session('success'))
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative" role="alert">
                    <span class="block sm:inline">{{ session('success') }}</span>
                </div>
            @endif

            @if (session('error'))
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                    <span class="block sm:inline">{{ session('error') }}</span>
                </div>
            @endif

            @if ($errors->any())
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                    <ul class="list-disc list-inside">
                        @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            <!-- Installation Steps -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    @php
                        $currentStep = request('step', 1);
                    @endphp

                    <!-- Step Indicator -->
                    <div class="mb-8">
                        <div class="flex items-center">
                            @for ($i = 1; $i <= 3; $i++)
                                <div class="flex items-center {{ $i < 3 ? 'flex-1' : '' }}">
                                    <div class="flex items-center justify-center w-8 h-8 rounded-full {{ $i <= $currentStep ? 'bg-indigo-600 text-white' : 'bg-gray-300 text-gray-600' }}">
                                        {{ $i }}
                                    </div>
                                    @if ($i < 3)
                                        <div class="flex-1 h-1 mx-2 {{ $i < $currentStep ? 'bg-indigo-600' : 'bg-gray-300' }}"></div>
                                    @endif
                                </div>
                            @endfor
                        </div>
                        <div class="flex justify-between mt-2 text-xs text-gray-600">
                            <span>Requirements</span>
                            <span>Database</span>
                            <span>Application</span>
                        </div>
                    </div>

                    @if ($currentStep == 1)
                        @include('install.steps.requirements')
                    @elseif ($currentStep == 2)
                        @include('install.steps.database')
                    @elseif ($currentStep == 3)
                        @include('install.steps.application')
                    @endif
                </div>
            </div>

            <div class="text-center">
                <p class="text-xs text-gray-500">
                    SyncPOS v1.0 - Modern Point of Sale System
                </p>
            </div>
        </div>
    </div>

    <script>
        // Test database connection
        function testDatabaseConnection() {
            const form = document.getElementById('database-form');
            const formData = new FormData(form);
            const button = document.getElementById('test-connection');
            const originalText = button.textContent;
            
            button.textContent = 'Testing...';
            button.disabled = true;
            
            fetch('{{ route("install.process") }}?test=1', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Database connection successful!');
                } else {
                    alert('Database connection failed: ' + data.message);
                }
            })
            .catch(error => {
                alert('Error testing connection: ' + error.message);
            })
            .finally(() => {
                button.textContent = originalText;
                button.disabled = false;
            });
        }
    </script>
</body>
</html>
