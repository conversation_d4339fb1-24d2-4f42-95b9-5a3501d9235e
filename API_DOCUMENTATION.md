# 📡 SyncPOS API Documentation

SyncPOS provides a comprehensive REST API for integration with external systems, mobile applications, and third-party services.

## 🔐 Authentication

SyncPOS uses Laravel Sanctum for API authentication. All API requests must include a valid Bearer token.

### Getting an API Token

```http
POST /api/auth/login
Content-Type: application/json

{
    "email": "<EMAIL>",
    "password": "password"
}
```

**Response:**
```json
{
    "message": "Login successful",
    "data": {
        "user": {
            "id": 1,
            "name": "<PERSON>",
            "email": "<EMAIL>"
        },
        "token": "1|abc123def456...",
        "expires_at": "2024-12-31T23:59:59.000000Z"
    }
}
```

### Using the Token

Include the token in the Authorization header for all subsequent requests:

```http
Authorization: Bearer 1|abc123def456...
```

### Logout

```http
POST /api/auth/logout
Authorization: Bearer {token}
```

## 📦 Products API

### List Products

```http
GET /api/products
Authorization: Bearer {token}
```

**Query Parameters:**
- `page` (integer): Page number for pagination
- `per_page` (integer): Items per page (max 100)
- `search` (string): Search by name, SKU, or barcode
- `category_id` (integer): Filter by category
- `brand_id` (integer): Filter by brand
- `is_active` (boolean): Filter by active status
- `sort_by` (string): Sort field (name, price, created_at)
- `sort_order` (string): Sort direction (asc, desc)

**Response:**
```json
{
    "data": [
        {
            "id": 1,
            "name": "iPhone 15 Pro",
            "sku": "IPH15PRO",
            "barcode": "1234567890123",
            "price": 999.99,
            "cost_price": 750.00,
            "category": {
                "id": 1,
                "name": "Electronics"
            },
            "brand": {
                "id": 1,
                "name": "Apple"
            },
            "is_active": true,
            "stock_quantity": 50,
            "created_at": "2024-01-01T00:00:00.000000Z"
        }
    ],
    "meta": {
        "current_page": 1,
        "per_page": 15,
        "total": 100,
        "last_page": 7
    }
}
```

### Get Product

```http
GET /api/products/{id}
Authorization: Bearer {token}
```

### Create Product

```http
POST /api/products
Authorization: Bearer {token}
Content-Type: application/json

{
    "name": "New Product",
    "sku": "NEW001",
    "barcode": "1234567890123",
    "description": "Product description",
    "price": 99.99,
    "cost_price": 50.00,
    "category_id": 1,
    "brand_id": 1,
    "weight": 1.5,
    "is_active": true,
    "track_quantity": true,
    "stores": [
        {
            "store_id": 1,
            "stock_quantity": 100,
            "min_stock_level": 10,
            "max_stock_level": 200
        }
    ]
}
```

### Update Product

```http
PUT /api/products/{id}
Authorization: Bearer {token}
Content-Type: application/json

{
    "name": "Updated Product Name",
    "price": 109.99
}
```

### Delete Product

```http
DELETE /api/products/{id}
Authorization: Bearer {token}
```

## 🛒 Sales API

### List Sales

```http
GET /api/sales
Authorization: Bearer {token}
```

**Query Parameters:**
- `page`, `per_page`: Pagination
- `start_date`, `end_date`: Date range filter
- `store_id`: Filter by store
- `customer_id`: Filter by customer
- `status`: Filter by status (completed, pending, cancelled, refunded)
- `payment_status`: Filter by payment status

### Get Sale

```http
GET /api/sales/{id}
Authorization: Bearer {token}
```

### Create Sale

```http
POST /api/sales
Authorization: Bearer {token}
Content-Type: application/json

{
    "store_id": 1,
    "customer_id": 1,
    "items": [
        {
            "product_id": 1,
            "quantity": 2,
            "price": 99.99,
            "tax_rate": 10
        }
    ],
    "discount_amount": 10.00,
    "payment_method": "cash",
    "payments": [
        {
            "method": "cash",
            "amount": 209.98
        }
    ],
    "notes": "Customer requested gift wrap"
}
```

**Response:**
```json
{
    "message": "Sale created successfully",
    "data": {
        "id": 123,
        "sale_number": "SALE-000123",
        "total_amount": 209.98,
        "payment_status": "paid",
        "status": "completed",
        "created_at": "2024-01-01T12:00:00.000000Z"
    },
    "change": 0.00
}
```

### Process Refund

```http
POST /api/sales/{id}/refund
Authorization: Bearer {token}
Content-Type: application/json

{
    "items": [
        {
            "sale_item_id": 1,
            "quantity": 1
        }
    ],
    "reason": "Defective product",
    "notes": "Customer reported screen issues"
}
```

## 👥 Customers API

### List Customers

```http
GET /api/customers
Authorization: Bearer {token}
```

### Get Customer

```http
GET /api/customers/{id}
Authorization: Bearer {token}
```

### Create Customer

```http
POST /api/customers
Authorization: Bearer {token}
Content-Type: application/json

{
    "name": "John Smith",
    "email": "<EMAIL>",
    "phone": "******-0123",
    "address": "123 Main St",
    "city": "New York",
    "state": "NY",
    "country": "USA",
    "postal_code": "10001",
    "customer_group_id": 1
}
```

### Update Customer

```http
PUT /api/customers/{id}
Authorization: Bearer {token}
Content-Type: application/json

{
    "name": "John Smith Jr.",
    "phone": "******-0124"
}
```

## 📊 Inventory API

### Get Inventory

```http
GET /api/inventory
Authorization: Bearer {token}
```

**Query Parameters:**
- `store_id`: Filter by store
- `product_id`: Filter by product
- `low_stock`: Show only low stock items (boolean)

### Adjust Inventory

```http
POST /api/inventory/adjust
Authorization: Bearer {token}
Content-Type: application/json

{
    "product_id": 1,
    "store_id": 1,
    "adjustment_type": "increase",
    "quantity": 50,
    "reason": "Stock received",
    "notes": "Weekly delivery from supplier"
}
```

### Transfer Stock

```http
POST /api/inventory/transfer
Authorization: Bearer {token}
Content-Type: application/json

{
    "from_store_id": 1,
    "to_store_id": 2,
    "items": [
        {
            "product_id": 1,
            "quantity": 10
        }
    ],
    "notes": "Rebalancing stock levels"
}
```

## 📈 Reports API

### Sales Report

```http
GET /api/reports/sales
Authorization: Bearer {token}
```

**Query Parameters:**
- `start_date`, `end_date`: Date range
- `store_id`: Filter by store
- `group_by`: Group results (day, week, month, year)

### Inventory Report

```http
GET /api/reports/inventory
Authorization: Bearer {token}
```

### Top Products Report

```http
GET /api/reports/top-products
Authorization: Bearer {token}
```

## 🔍 Search API

### Search Products (POS)

```http
GET /api/pos/search-products
Authorization: Bearer {token}
```

**Query Parameters:**
- `q`: Search query (name, SKU, barcode)
- `store_id`: Filter by store availability

### Search Customers (POS)

```http
GET /api/pos/search-customers
Authorization: Bearer {token}
```

**Query Parameters:**
- `q`: Search query (name, email, phone)

### Scan Barcode

```http
GET /api/pos/scan-barcode/{barcode}
Authorization: Bearer {token}
```

## 🔗 Integration API

### WooCommerce

```http
POST /api/integrations/woocommerce/test-connection
Authorization: Bearer {token}
Content-Type: application/json

{
    "store_id": 1,
    "url": "https://mystore.com",
    "consumer_key": "ck_...",
    "consumer_secret": "cs_..."
}
```

```http
POST /api/integrations/woocommerce/import-products
Authorization: Bearer {token}
Content-Type: application/json

{
    "store_id": 1
}
```

### Shopify

```http
POST /api/integrations/shopify/test-connection
Authorization: Bearer {token}
Content-Type: application/json

{
    "store_id": 1,
    "store_url": "mystore.myshopify.com",
    "access_token": "shpat_..."
}
```

## ⚠️ Error Handling

The API uses standard HTTP status codes and returns errors in a consistent format:

```json
{
    "message": "The given data was invalid.",
    "errors": {
        "email": [
            "The email field is required."
        ],
        "password": [
            "The password field is required."
        ]
    }
}
```

### Common Status Codes

- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `422` - Validation Error
- `500` - Internal Server Error

### Custom Error Codes

- `INSUFFICIENT_STOCK` - Not enough inventory
- `PAYMENT_FAILED` - Payment processing failed
- `STORE_ACCESS_DENIED` - User doesn't have access to store
- `DUPLICATE_ENTRY` - Duplicate SKU/barcode
- `RESOURCE_LOCKED` - Resource is being used

## 🔄 Rate Limiting

API requests are rate limited to prevent abuse:

- **Authenticated requests**: 1000 requests per hour
- **Unauthenticated requests**: 100 requests per hour

Rate limit headers are included in responses:

```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1640995200
```

## 📝 Webhooks

SyncPOS can send webhooks for important events:

### Available Events

- `sale.created` - New sale completed
- `sale.refunded` - Sale refunded
- `inventory.low_stock` - Product below minimum stock
- `customer.created` - New customer registered

### Webhook Payload Example

```json
{
    "event": "sale.created",
    "data": {
        "id": 123,
        "sale_number": "SALE-000123",
        "total_amount": 99.99,
        "store_id": 1,
        "customer_id": 1,
        "created_at": "2024-01-01T12:00:00.000000Z"
    },
    "timestamp": "2024-01-01T12:00:00.000000Z"
}
```

## 🧪 Testing

Use the following test credentials for API testing:

```
Email: <EMAIL>
Password: demo123
Store ID: 1
```

### Postman Collection

Download our Postman collection for easy API testing:
[SyncPOS API Collection](https://api.syncpos.com/postman-collection.json)

---

For additional API support, please contact our development <NAME_EMAIL>
