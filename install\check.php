<?php
// Simple diagnostic script to check what's causing the error
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>SyncPOS Diagnostic Check</h1>";
echo "<style>body{font-family:Arial;margin:20px;} .pass{color:green;} .fail{color:red;} .info{background:#f0f8ff;padding:10px;margin:10px 0;border-left:4px solid #0066cc;}</style>";

echo "<div class='info'><strong>This diagnostic will help identify the issue causing the Internal Server Error.</strong></div>";

// Check PHP version
echo "<h2>PHP Information</h2>";
echo "<p>PHP Version: <strong>" . PHP_VERSION . "</strong></p>";
echo "<p>Server Software: <strong>" . $_SERVER['SERVER_SOFTWARE'] . "</strong></p>";
echo "<p>Document Root: <strong>" . $_SERVER['DOCUMENT_ROOT'] . "</strong></p>";
echo "<p>Script Path: <strong>" . __FILE__ . "</strong></p>";

// Check if we can access parent directory
echo "<h2>File System Check</h2>";
$parentDir = dirname(__DIR__);
echo "<p>Parent Directory: <strong>" . $parentDir . "</strong></p>";
echo "<p>Parent Directory Exists: " . (is_dir($parentDir) ? "<span class='pass'>YES</span>" : "<span class='fail'>NO</span>") . "</p>";
echo "<p>Parent Directory Readable: " . (is_readable($parentDir) ? "<span class='pass'>YES</span>" : "<span class='fail'>NO</span>") . "</p>";

// Check for essential files
echo "<h2>Essential Files Check</h2>";
$files = [
    '../composer.json' => 'Composer configuration',
    '../.env' => 'Environment file',
    '../.env.example' => 'Environment example',
    '../public/index.php' => 'Main application file',
    '../vendor/autoload.php' => 'Composer autoloader',
    '../bootstrap/app.php' => 'Laravel bootstrap',
    '../storage' => 'Storage directory',
    '../bootstrap/cache' => 'Bootstrap cache directory'
];

foreach ($files as $file => $description) {
    $exists = file_exists($file);
    $readable = $exists && is_readable($file);
    echo "<p>{$description}: ";
    if ($exists) {
        echo "<span class='pass'>EXISTS</span>";
        if (!$readable) {
            echo " <span class='fail'>(NOT READABLE)</span>";
        }
    } else {
        echo "<span class='fail'>MISSING</span>";
    }
    echo "</p>";
}

// Check PHP extensions
echo "<h2>PHP Extensions Check</h2>";
$extensions = [
    'pdo' => 'PDO',
    'pdo_mysql' => 'PDO MySQL',
    'openssl' => 'OpenSSL',
    'mbstring' => 'Mbstring',
    'tokenizer' => 'Tokenizer',
    'xml' => 'XML',
    'ctype' => 'Ctype',
    'json' => 'JSON',
    'bcmath' => 'BCMath',
    'fileinfo' => 'Fileinfo'
];

foreach ($extensions as $ext => $name) {
    $loaded = extension_loaded($ext);
    echo "<p>{$name}: " . ($loaded ? "<span class='pass'>LOADED</span>" : "<span class='fail'>NOT LOADED</span>") . "</p>";
}

// Check directory permissions
echo "<h2>Directory Permissions</h2>";
$directories = [
    '../storage' => 'Storage',
    '../bootstrap/cache' => 'Bootstrap Cache',
    '../public' => 'Public'
];

foreach ($directories as $dir => $name) {
    if (is_dir($dir)) {
        $writable = is_writable($dir);
        echo "<p>{$name}: " . ($writable ? "<span class='pass'>WRITABLE</span>" : "<span class='fail'>NOT WRITABLE</span>") . "</p>";
    } else {
        echo "<p>{$name}: <span class='fail'>DIRECTORY MISSING</span></p>";
    }
}

// Test basic PHP functionality
echo "<h2>PHP Functionality Test</h2>";
try {
    $testArray = ['test' => 'value'];
    $testJson = json_encode($testArray);
    echo "<p>JSON encoding: <span class='pass'>WORKING</span></p>";
} catch (Exception $e) {
    echo "<p>JSON encoding: <span class='fail'>ERROR - " . $e->getMessage() . "</span></p>";
}

// Check if we can include files
echo "<h2>File Include Test</h2>";
if (file_exists('../vendor/autoload.php')) {
    try {
        require_once '../vendor/autoload.php';
        echo "<p>Composer autoloader: <span class='pass'>LOADED SUCCESSFULLY</span></p>";
    } catch (Exception $e) {
        echo "<p>Composer autoloader: <span class='fail'>ERROR - " . $e->getMessage() . "</span></p>";
    } catch (Error $e) {
        echo "<p>Composer autoloader: <span class='fail'>FATAL ERROR - " . $e->getMessage() . "</span></p>";
    }
} else {
    echo "<p>Composer autoloader: <span class='fail'>FILE NOT FOUND</span></p>";
}

// Check .htaccess
echo "<h2>.htaccess Check</h2>";
if (file_exists('../.htaccess')) {
    echo "<p>Root .htaccess: <span class='pass'>EXISTS</span></p>";
} else {
    echo "<p>Root .htaccess: <span class='fail'>MISSING</span></p>";
}

if (file_exists('../public/.htaccess')) {
    echo "<p>Public .htaccess: <span class='pass'>EXISTS</span></p>";
} else {
    echo "<p>Public .htaccess: <span class='fail'>MISSING</span></p>";
}

// Check Apache modules
echo "<h2>Apache Modules (if available)</h2>";
if (function_exists('apache_get_modules')) {
    $modules = apache_get_modules();
    $requiredModules = ['mod_rewrite', 'mod_headers'];
    foreach ($requiredModules as $module) {
        $loaded = in_array($module, $modules);
        echo "<p>{$module}: " . ($loaded ? "<span class='pass'>LOADED</span>" : "<span class='fail'>NOT LOADED</span>") . "</p>";
    }
} else {
    echo "<p>Apache module information not available (this is normal for some setups)</p>";
}

echo "<h2>Recommendations</h2>";
echo "<div class='info'>";
echo "<p><strong>If you see any FAIL or ERROR messages above:</strong></p>";
echo "<ol>";
echo "<li>Fix any missing PHP extensions by enabling them in php.ini</li>";
echo "<li>Ensure all files were uploaded correctly</li>";
echo "<li>Check directory permissions (storage and bootstrap/cache need to be writable)</li>";
echo "<li>If Composer autoloader is missing, run 'composer install' in the project root</li>";
echo "<li>Make sure mod_rewrite is enabled in Apache</li>";
echo "</ol>";
echo "</div>";

echo "<h2>Next Steps</h2>";
echo "<p><a href='simple-install.php' style='background:#0066cc;color:white;padding:10px 20px;text-decoration:none;border-radius:5px;'>Try Simple Installer</a></p>";
echo "<p><a href='index.php' style='background:#28a745;color:white;padding:10px 20px;text-decoration:none;border-radius:5px;margin-left:10px;'>Try Full Installer</a></p>";
?>
