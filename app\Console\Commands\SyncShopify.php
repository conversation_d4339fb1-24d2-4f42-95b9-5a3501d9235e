<?php

namespace App\Console\Commands;

use App\Services\ShopifyService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class SyncShopify extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:shopify 
                            {--direction=both : Sync direction (from, to, both)}
                            {--type=products : What to sync (products, inventory, both)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync data with Shopify store';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $direction = $this->option('direction');
        $type = $this->option('type');

        $this->info('Starting Shopify sync...');

        $shopifyService = new ShopifyService();

        // Test connection first
        if (!$shopifyService->testConnection()) {
            $this->error('Failed to connect to Shopify store. Please check your credentials.');
            return 1;
        }

        $this->info('Connected to Shopify store successfully.');

        try {
            // Sync products from Shopify
            if (in_array($direction, ['from', 'both']) && in_array($type, ['products', 'both'])) {
                $this->info('Syncing products from Shopify...');
                $results = $shopifyService->syncProductsFromShopify();
                $this->displayResults('Products from Shopify', $results);
            }

            // Sync products to Shopify
            if (in_array($direction, ['to', 'both']) && in_array($type, ['products', 'both'])) {
                $this->info('Syncing products to Shopify...');
                $results = $shopifyService->syncProductsToShopify();
                $this->displayResults('Products to Shopify', $results);
            }

            // Sync inventory to Shopify
            if (in_array($direction, ['to', 'both']) && in_array($type, ['inventory', 'both'])) {
                $this->info('Syncing inventory to Shopify...');
                $results = $shopifyService->syncInventoryToShopify();
                $this->displayResults('Inventory to Shopify', $results);
            }

            $this->info('Shopify sync completed successfully.');
            return 0;

        } catch (\Exception $e) {
            $this->error('Sync failed: ' . $e->getMessage());
            Log::error('Shopify sync command failed: ' . $e->getMessage());
            return 1;
        }
    }

    /**
     * Display sync results
     */
    protected function displayResults(string $title, array $results): void
    {
        $this->info("=== {$title} Results ===");
        
        if (isset($results['created'])) {
            $this->line("Created: {$results['created']}");
        }
        
        if (isset($results['updated'])) {
            $this->line("Updated: {$results['updated']}");
        }

        if (!empty($results['errors'])) {
            $this->warn("Errors: " . count($results['errors']));
            foreach ($results['errors'] as $error) {
                $this->error("  - {$error}");
            }
        }

        $this->line('');
    }
}
