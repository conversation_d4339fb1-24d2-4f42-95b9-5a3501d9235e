<?php
/**
 * Reset SyncPOS Installation
 * This script removes the installation flag so you can reinstall
 */

echo "<h1>🔄 Reset SyncPOS Installation</h1>";
echo "<style>body { font-family: Arial, sans-serif; margin: 20px; } .success { color: green; } .error { color: red; }</style>";

// Check if we're in the right directory
if (!file_exists('storage')) {
    echo "<p class='error'>Error: storage directory not found. Please run this script from the SyncPOS root directory.</p>";
    exit;
}

$installFile = 'storage/app/installed';

if (file_exists($installFile)) {
    if (unlink($installFile)) {
        echo "<p class='success'>✅ Installation flag removed successfully!</p>";
    } else {
        echo "<p class='error'>❌ Failed to remove installation flag.</p>";
    }
} else {
    echo "<p class='success'>✅ No installation flag found. System is ready for installation.</p>";
}

// Clear caches
$commands = [
    'php artisan cache:clear',
    'php artisan config:clear',
    'php artisan route:clear',
    'php artisan view:clear'
];

echo "<h3>Clearing caches...</h3>";
foreach ($commands as $command) {
    exec($command . ' 2>&1', $output, $return_code);
    if ($return_code === 0) {
        echo "<p class='success'>✅ " . htmlspecialchars($command) . "</p>";
    } else {
        echo "<p class='error'>❌ Failed: " . htmlspecialchars($command) . "</p>";
    }
}

echo "<hr>";
echo "<h2>🚀 Next Steps:</h2>";
echo "<ol>";
echo "<li><strong>Test Laravel:</strong> <a href='http://localhost/syncpos/public/test' target='_blank'>http://localhost/syncpos/public/test</a></li>";
echo "<li><strong>Direct Setup (bypasses all checks):</strong> <a href='http://localhost/syncpos/public/setup' target='_blank'>http://localhost/syncpos/public/setup</a></li>";
echo "<li><strong>Original Install:</strong> <a href='http://localhost/syncpos/public/install' target='_blank'>http://localhost/syncpos/public/install</a></li>";
echo "<li><strong>Laravel Server:</strong> Run <code>php artisan serve</code> then visit <a href='http://localhost:8000/setup' target='_blank'>http://localhost:8000/setup</a></li>";
echo "</ol>";

echo "<h3>🔧 Alternative: Manual Database Setup</h3>";
echo "<p>If the web installer still doesn't work, you can set up manually:</p>";
echo "<ol>";
echo "<li>Create database: <code>CREATE DATABASE syncpos;</code></li>";
echo "<li>Run: <code>php artisan migrate</code></li>";
echo "<li>Run: <code>php artisan db:seed</code></li>";
echo "<li>Create admin user: <code>php artisan make:admin</code></li>";
echo "</ol>";
?>
