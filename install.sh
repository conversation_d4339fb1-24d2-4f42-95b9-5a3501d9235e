#!/bin/bash

# SyncPOS Installation Script
# This script will help you install SyncPOS step by step

echo "🚀 SyncPOS Installation Script"
echo "================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
if [ "$EUID" -eq 0 ]; then
    print_warning "Running as root. This is not recommended for development."
fi

# Step 1: Check PHP version
print_status "Checking PHP version..."
PHP_VERSION=$(php -v | head -n 1 | cut -d " " -f 2 | cut -d "." -f 1,2)
if [ "$(printf '%s\n' "8.2" "$PHP_VERSION" | sort -V | head -n1)" = "8.2" ]; then
    print_status "PHP version $PHP_VERSION is compatible ✅"
else
    print_error "PHP 8.2 or higher is required. Current version: $PHP_VERSION"
    exit 1
fi

# Step 2: Check if Composer is installed
print_status "Checking Composer..."
if command -v composer &> /dev/null; then
    print_status "Composer is installed ✅"
else
    print_error "Composer is not installed. Please install it from https://getcomposer.org"
    exit 1
fi

# Step 3: Check if we're in the right directory
if [ ! -f "composer.json" ]; then
    print_error "composer.json not found. Please run this script from the SyncPOS root directory."
    exit 1
fi

# Step 4: Install PHP dependencies
print_status "Installing PHP dependencies..."
if composer install --no-dev --optimize-autoloader; then
    print_status "PHP dependencies installed ✅"
else
    print_error "Failed to install PHP dependencies"
    exit 1
fi

# Step 5: Check if Node.js is installed
print_status "Checking Node.js..."
if command -v npm &> /dev/null; then
    print_status "Node.js/NPM is installed ✅"
    
    # Install Node dependencies
    print_status "Installing Node.js dependencies..."
    if npm install; then
        print_status "Node.js dependencies installed ✅"
        
        # Build assets
        print_status "Building frontend assets..."
        if npm run build; then
            print_status "Frontend assets built ✅"
        else
            print_warning "Failed to build frontend assets. You can do this later with 'npm run build'"
        fi
    else
        print_warning "Failed to install Node.js dependencies. You can do this later with 'npm install'"
    fi
else
    print_warning "Node.js/NPM not found. Frontend assets won't be built."
    print_warning "Install Node.js from https://nodejs.org and run 'npm install && npm run build'"
fi

# Step 6: Create .env file
print_status "Setting up environment file..."
if [ ! -f ".env" ]; then
    if [ -f ".env.example" ]; then
        cp .env.example .env
        print_status ".env file created from .env.example ✅"
    else
        print_error ".env.example file not found"
        exit 1
    fi
else
    print_warning ".env file already exists. Skipping..."
fi

# Step 7: Generate application key
print_status "Generating application key..."
if php artisan key:generate --force; then
    print_status "Application key generated ✅"
else
    print_error "Failed to generate application key"
    exit 1
fi

# Step 8: Set permissions
print_status "Setting file permissions..."
chmod -R 755 storage
chmod -R 755 bootstrap/cache
print_status "Permissions set ✅"

# Step 9: Check web server configuration
print_status "Checking web server setup..."
DOCUMENT_ROOT=$(pwd)
print_status "Current directory: $DOCUMENT_ROOT"
print_warning "Make sure your web server points to: $DOCUMENT_ROOT/public"

# Step 10: Database check
print_status "Database setup..."
print_warning "Please ensure you have:"
print_warning "1. Created a MySQL database"
print_warning "2. Created a database user with proper permissions"
print_warning "3. Updated the .env file with database credentials"

# Final instructions
echo ""
echo "🎉 Installation Complete!"
echo "========================="
echo ""
echo "Next steps:"
echo "1. Update your .env file with database credentials"
echo "2. Configure your web server to point to the 'public' directory"
echo "3. Visit http://yourdomain.com/install to complete the setup"
echo ""
echo "For development, you can use:"
echo "  php artisan serve"
echo ""
echo "Troubleshooting:"
echo "- Check requirements: http://yourdomain.com/check-requirements.php"
echo "- View logs: tail -f storage/logs/laravel.log"
echo "- Check permissions: ls -la storage/ bootstrap/cache/"
echo ""
print_status "Happy selling with SyncPOS! 🛒"
