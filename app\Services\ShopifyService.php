<?php

namespace App\Services;

use App\Models\Product;
use App\Models\Store;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class ShopifyService
{
    protected $shopDomain;
    protected $accessToken;
    protected $apiVersion;

    public function __construct()
    {
        $this->shopDomain = tenant('settings.shopify_shop_domain') ?? env('SHOPIFY_SHOP_DOMAIN');
        $this->accessToken = tenant('settings.shopify_access_token') ?? env('SHOPIFY_ACCESS_TOKEN');
        $this->apiVersion = '2023-10';
    }

    /**
     * Test connection to Shopify store
     */
    public function testConnection(): bool
    {
        try {
            $response = $this->makeRequest('GET', 'shop');
            return $response->successful();
        } catch (\Exception $e) {
            Log::error('Shopify connection test failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Sync products from Shopify to local database
     */
    public function syncProductsFromShopify(): array
    {
        $results = [
            'created' => 0,
            'updated' => 0,
            'errors' => []
        ];

        try {
            $limit = 250;
            $sinceId = 0;

            do {
                $response = $this->makeRequest('GET', 'products', [
                    'limit' => $limit,
                    'since_id' => $sinceId,
                    'status' => 'active'
                ]);

                if (!$response->successful()) {
                    throw new \Exception('Failed to fetch products from Shopify');
                }

                $data = $response->json();
                $products = $data['products'] ?? [];

                foreach ($products as $shopifyProduct) {
                    try {
                        $this->createOrUpdateProductFromShopify($shopifyProduct, $results);
                        $sinceId = $shopifyProduct['id'];
                    } catch (\Exception $e) {
                        $results['errors'][] = "Product {$shopifyProduct['title']}: " . $e->getMessage();
                    }
                }

            } while (count($products) === $limit);

        } catch (\Exception $e) {
            $results['errors'][] = $e->getMessage();
            Log::error('Shopify sync error: ' . $e->getMessage());
        }

        return $results;
    }

    /**
     * Sync products from local database to Shopify
     */
    public function syncProductsToShopify(): array
    {
        $results = [
            'created' => 0,
            'updated' => 0,
            'errors' => []
        ];

        $products = Product::where('sync_status', 'pending')
            ->orWhereNull('shopify_id')
            ->get();

        foreach ($products as $product) {
            try {
                if ($product->shopify_id) {
                    $this->updateProductInShopify($product, $results);
                } else {
                    $this->createProductInShopify($product, $results);
                }
            } catch (\Exception $e) {
                $results['errors'][] = "Product {$product->name}: " . $e->getMessage();
                $product->update(['sync_status' => 'failed']);
            }
        }

        return $results;
    }

    /**
     * Sync inventory levels to Shopify
     */
    public function syncInventoryToShopify(Store $store = null): array
    {
        $results = [
            'updated' => 0,
            'errors' => []
        ];

        $query = Product::whereNotNull('shopify_id');
        
        if ($store) {
            $query->whereHas('stores', function ($q) use ($store) {
                $q->where('store_id', $store->id);
            });
        }

        $products = $query->get();

        foreach ($products as $product) {
            try {
                $totalStock = $store ? 
                    $product->getStockForStore($store) : 
                    $product->total_stock;

                // Get inventory item ID first
                $productResponse = $this->makeRequest('GET', "products/{$product->shopify_id}");
                
                if (!$productResponse->successful()) {
                    throw new \Exception('Failed to fetch product from Shopify');
                }

                $shopifyProduct = $productResponse->json()['product'];
                $variant = $shopifyProduct['variants'][0] ?? null;

                if (!$variant || !$variant['inventory_item_id']) {
                    throw new \Exception('No inventory item found for product');
                }

                // Get inventory levels
                $inventoryResponse = $this->makeRequest('GET', 'inventory_levels', [
                    'inventory_item_ids' => $variant['inventory_item_id']
                ]);

                if (!$inventoryResponse->successful()) {
                    throw new \Exception('Failed to fetch inventory levels');
                }

                $inventoryLevels = $inventoryResponse->json()['inventory_levels'];
                
                if (empty($inventoryLevels)) {
                    throw new \Exception('No inventory levels found');
                }

                $locationId = $inventoryLevels[0]['location_id'];

                // Update inventory level
                $updateResponse = $this->makeRequest('POST', 'inventory_levels/set', [
                    'location_id' => $locationId,
                    'inventory_item_id' => $variant['inventory_item_id'],
                    'available' => $totalStock
                ]);

                if ($updateResponse->successful()) {
                    $results['updated']++;
                } else {
                    throw new \Exception('Failed to update inventory in Shopify');
                }

            } catch (\Exception $e) {
                $results['errors'][] = "Product {$product->name}: " . $e->getMessage();
            }
        }

        return $results;
    }

    /**
     * Create or update product from Shopify data
     */
    protected function createOrUpdateProductFromShopify(array $shopifyProduct, array &$results): void
    {
        $product = Product::where('shopify_id', $shopifyProduct['id'])->first();

        // Get the first variant for pricing and SKU
        $variant = $shopifyProduct['variants'][0] ?? null;

        $productData = [
            'name' => $shopifyProduct['title'],
            'description' => strip_tags($shopifyProduct['body_html']),
            'sku' => $variant['sku'] ?? 'SHOPIFY-' . $shopifyProduct['id'],
            'price' => $variant['price'] ?? 0,
            'shopify_id' => $shopifyProduct['id'],
            'last_synced_at' => now(),
            'sync_status' => 'synced',
        ];

        // Handle images
        if (!empty($shopifyProduct['images'])) {
            $productData['image'] = $shopifyProduct['images'][0]['src'];
            $productData['images'] = array_column($shopifyProduct['images'], 'src');
        }

        if ($product) {
            $product->update($productData);
            $results['updated']++;
        } else {
            Product::create($productData);
            $results['created']++;
        }
    }

    /**
     * Create product in Shopify
     */
    protected function createProductInShopify(Product $product, array &$results): void
    {
        $shopifyProductData = [
            'product' => [
                'title' => $product->name,
                'body_html' => $product->description,
                'vendor' => $product->brand->name ?? '',
                'product_type' => $product->category->name ?? '',
                'status' => $product->is_active ? 'active' : 'draft',
                'variants' => [
                    [
                        'sku' => $product->sku,
                        'price' => (string) $product->price,
                        'inventory_management' => 'shopify',
                        'inventory_quantity' => $product->total_stock,
                        'inventory_policy' => 'deny'
                    ]
                ]
            ]
        ];

        // Add images
        if ($product->image) {
            $shopifyProductData['product']['images'] = [
                ['src' => $product->image]
            ];
        }

        $response = $this->makeRequest('POST', 'products', $shopifyProductData);

        if ($response->successful()) {
            $shopifyProduct = $response->json()['product'];
            $product->update([
                'shopify_id' => $shopifyProduct['id'],
                'last_synced_at' => now(),
                'sync_status' => 'synced',
            ]);
            $results['created']++;
        } else {
            throw new \Exception('Failed to create product in Shopify: ' . $response->body());
        }
    }

    /**
     * Update product in Shopify
     */
    protected function updateProductInShopify(Product $product, array &$results): void
    {
        $shopifyProductData = [
            'product' => [
                'id' => $product->shopify_id,
                'title' => $product->name,
                'body_html' => $product->description,
                'vendor' => $product->brand->name ?? '',
                'product_type' => $product->category->name ?? '',
                'status' => $product->is_active ? 'active' : 'draft',
            ]
        ];

        $response = $this->makeRequest('PUT', "products/{$product->shopify_id}", $shopifyProductData);

        if ($response->successful()) {
            $product->update([
                'last_synced_at' => now(),
                'sync_status' => 'synced',
            ]);
            $results['updated']++;
        } else {
            throw new \Exception('Failed to update product in Shopify: ' . $response->body());
        }
    }

    /**
     * Make HTTP request to Shopify API
     */
    protected function makeRequest(string $method, string $endpoint, array $data = [])
    {
        $url = "https://{$this->shopDomain}.myshopify.com/admin/api/{$this->apiVersion}/{$endpoint}.json";

        $request = Http::withHeaders([
            'X-Shopify-Access-Token' => $this->accessToken,
            'Content-Type' => 'application/json',
        ])->timeout(30);

        switch (strtoupper($method)) {
            case 'GET':
                return $request->get($url, $data);
            case 'POST':
                return $request->post($url, $data);
            case 'PUT':
                return $request->put($url, $data);
            case 'DELETE':
                return $request->delete($url);
            default:
                throw new \InvalidArgumentException("Unsupported HTTP method: {$method}");
        }
    }
}
