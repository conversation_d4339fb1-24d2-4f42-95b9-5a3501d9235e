<?php
// Simple installer that doesn't rely on <PERSON><PERSON> or Composer
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

$step = $_GET['step'] ?? ($_POST['step'] ?? 1);
$error = $_SESSION['error'] ?? null;
$success = $_SESSION['success'] ?? null;
unset($_SESSION['error'], $_SESSION['success']);

// Process form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($step == 2) {
        // Database configuration
        $dbHost = $_POST['db_host'] ?? 'localhost';
        $dbName = $_POST['db_name'] ?? '';
        $dbUser = $_POST['db_user'] ?? '';
        $dbPass = $_POST['db_pass'] ?? '';
        $dbPort = $_POST['db_port'] ?? '3306';

        try {
            // Test connection
            $dsn = "mysql:host={$dbHost};port={$dbPort};charset=utf8mb4";
            $pdo = new PDO($dsn, $dbUser, $dbPass, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            ]);

            // Create database if it doesn't exist
            $stmt = $pdo->query("SHOW DATABASES LIKE '{$dbName}'");
            if ($stmt->rowCount() == 0) {
                $pdo->exec("CREATE DATABASE `{$dbName}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            }

            // Store config
            $_SESSION['db_config'] = compact('dbHost', 'dbName', 'dbUser', 'dbPass', 'dbPort');
            $_SESSION['success'] = 'Database connection successful!';
            $step = 3;
        } catch (PDOException $e) {
            $_SESSION['error'] = 'Database error: ' . $e->getMessage();
        }
    } elseif ($step == 3) {
        // Installation
        $appName = $_POST['app_name'] ?? 'SyncPOS';
        $appUrl = rtrim($_POST['app_url'] ?? '', '/');
        $adminEmail = $_POST['admin_email'] ?? '';
        $adminPassword = $_POST['admin_password'] ?? '';

        if (!isset($_SESSION['db_config'])) {
            $_SESSION['error'] = 'Database configuration missing. Please start over.';
            $step = 2;
        } else {
            try {
                $dbConfig = $_SESSION['db_config'];
                
                // Create .env file
                $appKey = 'base64:' . base64_encode(random_bytes(32));
                $envContent = createSimpleEnv($appName, $appUrl, $appKey, $dbConfig);
                
                if (!file_put_contents('../.env', $envContent)) {
                    throw new Exception('Could not create .env file');
                }

                // Create basic database structure
                createBasicDatabase($dbConfig);
                
                // Create admin user
                createAdminUser($dbConfig, $adminEmail, $adminPassword);
                
                // Create directories
                createDirectories();
                
                // Create lock file
                file_put_contents('../storage/installed', date('Y-m-d H:i:s'));
                
                $_SESSION['success'] = 'Installation completed successfully!';
                unset($_SESSION['db_config']);
                $step = 4;
                
            } catch (Exception $e) {
                $_SESSION['error'] = 'Installation failed: ' . $e->getMessage();
            }
        }
    }
}

function createSimpleEnv($appName, $appUrl, $appKey, $dbConfig) {
    return "APP_NAME=\"{$appName}\"
APP_ENV=production
APP_KEY={$appKey}
APP_DEBUG=false
APP_URL={$appUrl}

DB_CONNECTION=mysql
DB_HOST={$dbConfig['dbHost']}
DB_PORT={$dbConfig['dbPort']}
DB_DATABASE={$dbConfig['dbName']}
DB_USERNAME={$dbConfig['dbUser']}
DB_PASSWORD={$dbConfig['dbPass']}

SESSION_DRIVER=file
CACHE_DRIVER=file
QUEUE_CONNECTION=sync
MAIL_MAILER=log
";
}

function createBasicDatabase($dbConfig) {
    $dsn = "mysql:host={$dbConfig['dbHost']};port={$dbConfig['dbPort']};dbname={$dbConfig['dbName']};charset=utf8mb4";
    $pdo = new PDO($dsn, $dbConfig['dbUser'], $dbConfig['dbPass'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
    ]);

    $sql = "
    CREATE TABLE IF NOT EXISTS `users` (
        `id` bigint unsigned NOT NULL AUTO_INCREMENT,
        `name` varchar(255) NOT NULL,
        `email` varchar(255) NOT NULL,
        `email_verified_at` timestamp NULL DEFAULT NULL,
        `password` varchar(255) NOT NULL,
        `is_active` tinyint(1) NOT NULL DEFAULT '1',
        `created_at` timestamp NULL DEFAULT NULL,
        `updated_at` timestamp NULL DEFAULT NULL,
        PRIMARY KEY (`id`),
        UNIQUE KEY `users_email_unique` (`email`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

    CREATE TABLE IF NOT EXISTS `stores` (
        `id` bigint unsigned NOT NULL AUTO_INCREMENT,
        `name` varchar(255) NOT NULL,
        `code` varchar(50) NOT NULL,
        `address` text,
        `phone` varchar(20),
        `email` varchar(255),
        `is_active` tinyint(1) NOT NULL DEFAULT '1',
        `created_at` timestamp NULL DEFAULT NULL,
        `updated_at` timestamp NULL DEFAULT NULL,
        PRIMARY KEY (`id`),
        UNIQUE KEY `stores_code_unique` (`code`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

    CREATE TABLE IF NOT EXISTS `products` (
        `id` bigint unsigned NOT NULL AUTO_INCREMENT,
        `name` varchar(255) NOT NULL,
        `sku` varchar(100) NOT NULL,
        `price` decimal(10,2) NOT NULL DEFAULT '0.00',
        `cost_price` decimal(10,2) DEFAULT NULL,
        `is_active` tinyint(1) NOT NULL DEFAULT '1',
        `created_at` timestamp NULL DEFAULT NULL,
        `updated_at` timestamp NULL DEFAULT NULL,
        PRIMARY KEY (`id`),
        UNIQUE KEY `products_sku_unique` (`sku`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";

    $pdo->exec($sql);
}

function createAdminUser($dbConfig, $email, $password) {
    $dsn = "mysql:host={$dbConfig['dbHost']};port={$dbConfig['dbPort']};dbname={$dbConfig['dbName']};charset=utf8mb4";
    $pdo = new PDO($dsn, $dbConfig['dbUser'], $dbConfig['dbPass'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
    ]);

    $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
    $now = date('Y-m-d H:i:s');

    $stmt = $pdo->prepare("INSERT INTO users (name, email, password, email_verified_at, is_active, created_at, updated_at) VALUES (?, ?, ?, ?, 1, ?, ?)");
    $stmt->execute(['Administrator', $email, $hashedPassword, $now, $now, $now]);
}

function createDirectories() {
    $dirs = [
        '../storage/app',
        '../storage/app/public',
        '../storage/framework',
        '../storage/framework/cache',
        '../storage/framework/sessions',
        '../storage/framework/views',
        '../storage/logs',
        '../bootstrap/cache'
    ];

    foreach ($dirs as $dir) {
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SyncPOS Simple Installer</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: Arial, sans-serif; background: #f5f5f5; padding: 20px; }
        .container { max-width: 600px; margin: 0 auto; background: white; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { padding: 30px; }
        .form-group { margin-bottom: 20px; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; }
        .form-group input, .form-group select { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
        .btn { background: #667eea; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; text-decoration: none; display: inline-block; }
        .btn:hover { background: #5a67d8; }
        .alert { padding: 15px; margin-bottom: 20px; border-radius: 5px; }
        .alert-error { background: #fee; color: #c33; border: 1px solid #fcc; }
        .alert-success { background: #efe; color: #363; border: 1px solid #cfc; }
        .step { text-align: center; margin-bottom: 20px; }
        .step span { display: inline-block; width: 30px; height: 30px; line-height: 30px; border-radius: 50%; margin: 0 5px; }
        .step .active { background: #667eea; color: white; }
        .step .completed { background: #28a745; color: white; }
        .step .pending { background: #ddd; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 SyncPOS Simple Installer</h1>
            <p>Quick & Easy Installation</p>
        </div>
        
        <div class="content">
            <?php if ($error): ?>
                <div class="alert alert-error"><?= htmlspecialchars($error) ?></div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success"><?= htmlspecialchars($success) ?></div>
            <?php endif; ?>

            <div class="step">
                <span class="<?= $step >= 1 ? ($step > 1 ? 'completed' : 'active') : 'pending' ?>">1</span>
                <span class="<?= $step >= 2 ? ($step > 2 ? 'completed' : 'active') : 'pending' ?>">2</span>
                <span class="<?= $step >= 3 ? ($step > 3 ? 'completed' : 'active') : 'pending' ?>">3</span>
                <span class="<?= $step >= 4 ? 'active' : 'pending' ?>">4</span>
            </div>

            <?php if ($step == 1): ?>
                <h2>Welcome to SyncPOS</h2>
                <p>This simple installer will set up SyncPOS with basic functionality.</p>
                <p><strong>Requirements:</strong></p>
                <ul style="margin: 15px 0; padding-left: 30px;">
                    <li>PHP 8.1+ ✓</li>
                    <li>MySQL Database ✓</li>
                    <li>Web Server ✓</li>
                </ul>
                <p style="text-align: center; margin-top: 30px;">
                    <a href="?step=2" class="btn">Start Installation</a>
                </p>

            <?php elseif ($step == 2): ?>
                <h2>Database Configuration</h2>
                <form method="POST">
                    <input type="hidden" name="step" value="2">
                    
                    <div class="form-group">
                        <label>Database Host</label>
                        <input type="text" name="db_host" value="localhost" required>
                    </div>
                    
                    <div class="form-group">
                        <label>Database Name</label>
                        <input type="text" name="db_name" placeholder="syncpos_db" required>
                    </div>
                    
                    <div class="form-group">
                        <label>Database Username</label>
                        <input type="text" name="db_user" value="root" required>
                    </div>
                    
                    <div class="form-group">
                        <label>Database Password</label>
                        <input type="password" name="db_pass" placeholder="Leave empty for localhost">
                    </div>
                    
                    <div class="form-group">
                        <label>Database Port</label>
                        <input type="text" name="db_port" value="3306">
                    </div>
                    
                    <button type="submit" class="btn">Test & Continue</button>
                </form>

            <?php elseif ($step == 3): ?>
                <h2>Application Setup</h2>
                <form method="POST">
                    <input type="hidden" name="step" value="3">
                    
                    <div class="form-group">
                        <label>Application Name</label>
                        <input type="text" name="app_name" value="SyncPOS" required>
                    </div>
                    
                    <div class="form-group">
                        <label>Application URL</label>
                        <input type="url" name="app_url" value="<?= (isset($_SERVER['HTTPS']) ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'] . str_replace('/install/simple-install.php', '', $_SERVER['REQUEST_URI']) ?>" required>
                    </div>
                    
                    <div class="form-group">
                        <label>Admin Email</label>
                        <input type="email" name="admin_email" required>
                    </div>
                    
                    <div class="form-group">
                        <label>Admin Password</label>
                        <input type="password" name="admin_password" minlength="6" required>
                    </div>
                    
                    <button type="submit" class="btn">Install SyncPOS</button>
                </form>

            <?php elseif ($step == 4): ?>
                <h2>🎉 Installation Complete!</h2>
                <div class="alert alert-success">
                    <strong>Success!</strong> SyncPOS has been installed successfully.
                </div>
                
                <p><strong>Important:</strong> Delete the <code>/install</code> folder for security.</p>
                
                <p style="text-align: center; margin-top: 30px;">
                    <a href="../public" class="btn">Access SyncPOS</a>
                </p>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>
