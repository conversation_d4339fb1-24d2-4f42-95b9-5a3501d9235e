<?php
/**
 * Comprehensive Composer Diagnostic Tool
 */

echo "<h1>🔍 Composer Diagnostic Tool</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .info { color: blue; }
    .terminal { background: #000; color: #0f0; padding: 10px; border-radius: 5px; margin: 10px 0; font-family: monospace; white-space: pre-wrap; }
    .button { background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 5px; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
</style>";

echo "<h2>🔍 Step 1: Checking Composer Installation</h2>";

// Check 1: Command line composer
echo "<h3>Method 1: Global Composer Check</h3>";
exec('composer --version 2>&1', $globalOutput, $globalReturn);
echo "<div class='terminal'>Command: composer --version\n";
echo "Return Code: $globalReturn\n";
echo "Output:\n" . htmlspecialchars(implode("\n", $globalOutput)) . "</div>";

if ($globalReturn === 0) {
    echo "<p class='success'>✅ Global Composer is working!</p>";
    $composerWorking = true;
} else {
    echo "<p class='error'>❌ Global Composer not found or not working</p>";
    $composerWorking = false;
}

// Check 2: Alternative composer commands
echo "<h3>Method 2: Alternative Composer Paths</h3>";
$composerPaths = [
    'composer.bat',
    'composer.phar',
    'php composer.phar',
    'C:\\ProgramData\\ComposerSetup\\bin\\composer.bat',
    'C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin\\composer.bat'
];

foreach ($composerPaths as $path) {
    exec("$path --version 2>&1", $output, $return);
    echo "<div class='terminal'>Command: $path --version\n";
    echo "Return Code: $return\n";
    if ($return === 0) {
        echo "<span class='success'>✅ WORKING</span>\n";
        echo "Output: " . htmlspecialchars(implode("\n", $output)) . "</div>";
        if (!$composerWorking) {
            $composerWorking = true;
            $workingComposer = $path;
        }
    } else {
        echo "<span class='error'>❌ NOT WORKING</span>\n";
        echo "Output: " . htmlspecialchars(implode("\n", $output)) . "</div>";
    }
    $output = [];
}

// Check 3: Environment variables
echo "<h3>Method 3: Environment Variables</h3>";
$pathVar = getenv('PATH');
echo "<div class='terminal'>PATH Variable:\n" . htmlspecialchars($pathVar) . "</div>";

$composerInPath = false;
if (stripos($pathVar, 'composer') !== false) {
    echo "<p class='success'>✅ Composer found in PATH variable</p>";
    $composerInPath = true;
} else {
    echo "<p class='warning'>⚠️ Composer not found in PATH variable</p>";
}

// Check 4: Common installation locations
echo "<h3>Method 4: Checking Common Installation Locations</h3>";
$commonPaths = [
    'C:\\ProgramData\\ComposerSetup\\bin\\composer.bat',
    'C:\\composer\\composer.bat',
    'C:\\tools\\composer\\composer.bat',
    getcwd() . '\\composer.phar',
];

echo "<table>";
echo "<tr><th>Location</th><th>Exists</th><th>Executable</th></tr>";

foreach ($commonPaths as $path) {
    $exists = file_exists($path);
    $executable = $exists && is_executable($path);
    
    echo "<tr>";
    echo "<td>" . htmlspecialchars($path) . "</td>";
    echo "<td>" . ($exists ? "<span class='success'>✅ Yes</span>" : "<span class='error'>❌ No</span>") . "</td>";
    echo "<td>" . ($executable ? "<span class='success'>✅ Yes</span>" : "<span class='error'>❌ No</span>") . "</td>";
    echo "</tr>";
}
echo "</table>";

// Step 2: Try to install dependencies anyway
echo "<hr>";
echo "<h2>🚀 Step 2: Attempt Dependency Installation</h2>";

if (file_exists('vendor/autoload.php')) {
    echo "<p class='success'>✅ Dependencies already installed!</p>";
    echo "<p><a href='public/install' class='button'>🚀 Start SyncPOS Installation</a></p>";
} else {
    echo "<p class='info'>Dependencies not found. Let's try to install them...</p>";
    
    if (isset($_GET['install'])) {
        echo "<h3>Installing Dependencies...</h3>";
        
        // Try different composer commands
        $installCommands = [
            'composer install --no-dev --optimize-autoloader',
            'php composer.phar install --no-dev --optimize-autoloader',
            'composer.bat install --no-dev --optimize-autoloader',
        ];
        
        if (isset($workingComposer)) {
            array_unshift($installCommands, "$workingComposer install --no-dev --optimize-autoloader");
        }
        
        foreach ($installCommands as $command) {
            echo "<div class='terminal'>Trying: $command\n";
            
            $descriptorspec = array(
                0 => array("pipe", "r"),
                1 => array("pipe", "w"),
                2 => array("pipe", "w")
            );
            
            $process = proc_open($command, $descriptorspec, $pipes);
            
            if (is_resource($process)) {
                fclose($pipes[0]);
                
                $output = stream_get_contents($pipes[1]);
                $error = stream_get_contents($pipes[2]);
                
                fclose($pipes[1]);
                fclose($pipes[2]);
                
                $return_value = proc_close($process);
                
                echo "Output:\n" . htmlspecialchars($output) . "\n";
                if ($error) {
                    echo "Errors:\n" . htmlspecialchars($error) . "\n";
                }
                echo "Return Code: $return_value</div>";
                
                if ($return_value === 0 && file_exists('vendor/autoload.php')) {
                    echo "<p class='success'>🎉 Dependencies installed successfully!</p>";
                    echo "<p><a href='public/install' class='button'>🚀 Start SyncPOS Installation</a></p>";
                    break;
                }
            } else {
                echo "Failed to execute command</div>";
            }
        }
        
        if (!file_exists('vendor/autoload.php')) {
            echo "<p class='error'>❌ All installation attempts failed</p>";
            showAlternativeSolutions();
        }
        
    } else {
        echo "<p><a href='?install=1' class='button'>📦 Try Installing Dependencies</a></p>";
    }
}

function showAlternativeSolutions() {
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>🔧 Alternative Solutions</h3>";
    
    echo "<h4>Option 1: Manual Composer Download</h4>";
    echo "<ol>";
    echo "<li>Download: <a href='https://getcomposer.org/composer.phar' target='_blank'>composer.phar</a></li>";
    echo "<li>Save it in this directory: <code>" . __DIR__ . "</code></li>";
    echo "<li>Refresh this page and try again</li>";
    echo "</ol>";
    
    echo "<h4>Option 2: Reinstall Composer</h4>";
    echo "<ol>";
    echo "<li>Uninstall current Composer from Control Panel</li>";
    echo "<li>Download fresh installer: <a href='https://getcomposer.org/Composer-Setup.exe' target='_blank'>Composer-Setup.exe</a></li>";
    echo "<li>Run as Administrator</li>";
    echo "<li>Restart your computer</li>";
    echo "<li>Try again</li>";
    echo "</ol>";
    
    echo "<h4>Option 3: Command Prompt Method</h4>";
    echo "<ol>";
    echo "<li>Open Command Prompt as Administrator</li>";
    echo "<li>Navigate to: <code>cd " . __DIR__ . "</code></li>";
    echo "<li>Download composer: <code>curl -sS https://getcomposer.org/installer | php</code></li>";
    echo "<li>Install dependencies: <code>php composer.phar install</code></li>";
    echo "</ol>";
    
    echo "<h4>Option 4: Pre-built Package</h4>";
    echo "<p>If all else fails, I can provide you with a pre-built package that includes all dependencies.</p>";
    
    echo "</div>";
}

// System Information
echo "<hr>";
echo "<h2>🖥️ System Information</h2>";
echo "<table>";
echo "<tr><th>Item</th><th>Value</th></tr>";
echo "<tr><td>PHP Version</td><td>" . PHP_VERSION . "</td></tr>";
echo "<tr><td>Operating System</td><td>" . PHP_OS . "</td></tr>";
echo "<tr><td>Current User</td><td>" . get_current_user() . "</td></tr>";
echo "<tr><td>Current Directory</td><td>" . __DIR__ . "</td></tr>";
echo "<tr><td>PHP Executable</td><td>" . PHP_BINARY . "</td></tr>";
echo "<tr><td>Server Software</td><td>" . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "</td></tr>";
echo "</table>";

echo "<hr>";
echo "<p style='text-align: center; color: #666;'>";
echo "<strong>SyncPOS Composer Diagnostic</strong><br>";
echo "If you need help, copy the output above and contact support.";
echo "</p>";
?>
