<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\Store;
use App\Services\WooCommerceService;
use App\Services\ShopifyService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class IntegrationController extends Controller
{
    protected $wooCommerceService;
    protected $shopifyService;

    public function __construct(WooCommerceService $wooCommerceService, ShopifyService $shopifyService)
    {
        $this->wooCommerceService = $wooCommerceService;
        $this->shopifyService = $shopifyService;
    }

    public function syncWooCommerce(Request $request)
    {
        $validated = $request->validate([
            'store_id' => 'required|exists:stores,id',
            'sync_type' => 'required|in:products,inventory,both',
            'direction' => 'required|in:import,export,bidirectional',
        ]);

        try {
            $store = Store::findOrFail($validated['store_id']);
            
            // Check if user has access to this store
            if (!auth()->user()->hasAccessToStore($store)) {
                return response()->json([
                    'message' => 'You do not have access to this store.',
                ], 403);
            }

            // Check if WooCommerce is configured for this store
            $settings = $store->settings['integrations']['woocommerce'] ?? [];
            if (!($settings['enabled'] ?? false)) {
                return response()->json([
                    'message' => 'WooCommerce integration is not enabled for this store.',
                ], 400);
            }

            $results = [];

            if ($validated['sync_type'] === 'products' || $validated['sync_type'] === 'both') {
                if ($validated['direction'] === 'import' || $validated['direction'] === 'bidirectional') {
                    $results['products_imported'] = $this->wooCommerceService->importProducts($store);
                }
                
                if ($validated['direction'] === 'export' || $validated['direction'] === 'bidirectional') {
                    $results['products_exported'] = $this->wooCommerceService->exportProducts($store);
                }
            }

            if ($validated['sync_type'] === 'inventory' || $validated['sync_type'] === 'both') {
                if ($validated['direction'] === 'import' || $validated['direction'] === 'bidirectional') {
                    $results['inventory_imported'] = $this->wooCommerceService->importInventory($store);
                }
                
                if ($validated['direction'] === 'export' || $validated['direction'] === 'bidirectional') {
                    $results['inventory_exported'] = $this->wooCommerceService->exportInventory($store);
                }
            }

            // Update last sync time
            $settings['last_sync'] = now()->toISOString();
            $storeSettings = $store->settings;
            $storeSettings['integrations']['woocommerce'] = $settings;
            $store->update(['settings' => $storeSettings]);

            return response()->json([
                'message' => 'WooCommerce sync completed successfully.',
                'data' => $results,
            ]);

        } catch (\Exception $e) {
            Log::error('WooCommerce sync failed', [
                'store_id' => $validated['store_id'],
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'message' => 'WooCommerce sync failed.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function syncShopify(Request $request)
    {
        $validated = $request->validate([
            'store_id' => 'required|exists:stores,id',
            'sync_type' => 'required|in:products,inventory,both',
            'direction' => 'required|in:import,export,bidirectional',
        ]);

        try {
            $store = Store::findOrFail($validated['store_id']);
            
            // Check if user has access to this store
            if (!auth()->user()->hasAccessToStore($store)) {
                return response()->json([
                    'message' => 'You do not have access to this store.',
                ], 403);
            }

            // Check if Shopify is configured for this store
            $settings = $store->settings['integrations']['shopify'] ?? [];
            if (!($settings['enabled'] ?? false)) {
                return response()->json([
                    'message' => 'Shopify integration is not enabled for this store.',
                ], 400);
            }

            $results = [];

            if ($validated['sync_type'] === 'products' || $validated['sync_type'] === 'both') {
                if ($validated['direction'] === 'import' || $validated['direction'] === 'bidirectional') {
                    $results['products_imported'] = $this->shopifyService->importProducts($store);
                }
                
                if ($validated['direction'] === 'export' || $validated['direction'] === 'bidirectional') {
                    $results['products_exported'] = $this->shopifyService->exportProducts($store);
                }
            }

            if ($validated['sync_type'] === 'inventory' || $validated['sync_type'] === 'both') {
                if ($validated['direction'] === 'import' || $validated['direction'] === 'bidirectional') {
                    $results['inventory_imported'] = $this->shopifyService->importInventory($store);
                }
                
                if ($validated['direction'] === 'export' || $validated['direction'] === 'bidirectional') {
                    $results['inventory_exported'] = $this->shopifyService->exportInventory($store);
                }
            }

            // Update last sync time
            $settings['last_sync'] = now()->toISOString();
            $storeSettings = $store->settings;
            $storeSettings['integrations']['shopify'] = $settings;
            $store->update(['settings' => $storeSettings]);

            return response()->json([
                'message' => 'Shopify sync completed successfully.',
                'data' => $results,
            ]);

        } catch (\Exception $e) {
            Log::error('Shopify sync failed', [
                'store_id' => $validated['store_id'],
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'message' => 'Shopify sync failed.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function syncStatus(Request $request)
    {
        $storeId = $request->store_id ?? auth()->user()->primaryStore()?->id;
        $store = Store::find($storeId);

        if (!$store) {
            return response()->json([
                'message' => 'Store not found.',
            ], 404);
        }

        // Check if user has access to this store
        if (!auth()->user()->hasAccessToStore($store)) {
            return response()->json([
                'message' => 'You do not have access to this store.',
            ], 403);
        }

        $integrations = $store->settings['integrations'] ?? [];
        
        $status = [
            'woocommerce' => [
                'enabled' => $integrations['woocommerce']['enabled'] ?? false,
                'last_sync' => $integrations['woocommerce']['last_sync'] ?? null,
                'status' => $this->getIntegrationStatus('woocommerce', $integrations['woocommerce'] ?? []),
            ],
            'shopify' => [
                'enabled' => $integrations['shopify']['enabled'] ?? false,
                'last_sync' => $integrations['shopify']['last_sync'] ?? null,
                'status' => $this->getIntegrationStatus('shopify', $integrations['shopify'] ?? []),
            ],
        ];

        // Add sync statistics
        $status['statistics'] = [
            'total_synced_products' => Product::whereNotNull('woocommerce_id')
                ->orWhereNotNull('shopify_id')
                ->count(),
            'woocommerce_products' => Product::whereNotNull('woocommerce_id')->count(),
            'shopify_products' => Product::whereNotNull('shopify_id')->count(),
            'last_sync_errors' => $this->getRecentSyncErrors(),
        ];

        return response()->json([
            'data' => $status,
        ]);
    }

    public function testConnection(Request $request)
    {
        $validated = $request->validate([
            'type' => 'required|in:woocommerce,shopify',
            'store_id' => 'required|exists:stores,id',
        ]);

        $store = Store::findOrFail($validated['store_id']);
        
        // Check if user has access to this store
        if (!auth()->user()->hasAccessToStore($store)) {
            return response()->json([
                'message' => 'You do not have access to this store.',
            ], 403);
        }

        try {
            if ($validated['type'] === 'woocommerce') {
                $result = $this->wooCommerceService->testConnection($store);
            } else {
                $result = $this->shopifyService->testConnection($store);
            }

            return response()->json([
                'message' => ucfirst($validated['type']) . ' connection test successful.',
                'data' => $result,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => ucfirst($validated['type']) . ' connection test failed.',
                'error' => $e->getMessage(),
            ], 400);
        }
    }

    public function syncLogs(Request $request)
    {
        $storeId = $request->store_id ?? auth()->user()->primaryStore()?->id;
        $type = $request->type; // woocommerce, shopify, or null for all
        
        // This would typically come from a sync_logs table
        // For now, we'll return a mock response
        $logs = collect([
            [
                'id' => 1,
                'type' => 'woocommerce',
                'action' => 'import_products',
                'status' => 'success',
                'message' => 'Imported 25 products successfully',
                'details' => ['imported' => 25, 'updated' => 5, 'errors' => 0],
                'created_at' => now()->subHours(2),
            ],
            [
                'id' => 2,
                'type' => 'shopify',
                'action' => 'export_inventory',
                'status' => 'error',
                'message' => 'Failed to export inventory: API rate limit exceeded',
                'details' => ['error' => 'Rate limit exceeded'],
                'created_at' => now()->subHours(4),
            ],
        ]);

        if ($type) {
            $logs = $logs->where('type', $type);
        }

        return response()->json([
            'data' => $logs->values(),
        ]);
    }

    private function getIntegrationStatus(string $type, array $config): string
    {
        if (!($config['enabled'] ?? false)) {
            return 'disabled';
        }

        $lastSync = $config['last_sync'] ?? null;
        if (!$lastSync) {
            return 'not_synced';
        }

        $lastSyncTime = Carbon::parse($lastSync);
        $syncInterval = $config['sync_interval'] ?? 30; // minutes

        if ($lastSyncTime->addMinutes($syncInterval)->isPast()) {
            return 'sync_overdue';
        }

        return 'active';
    }

    private function getRecentSyncErrors(): array
    {
        // This would typically query a sync_logs table
        // For now, return mock data
        return [
            [
                'type' => 'woocommerce',
                'message' => 'Product SKU conflict',
                'occurred_at' => now()->subHours(1),
            ],
        ];
    }
}
