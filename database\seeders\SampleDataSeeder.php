<?php

namespace Database\Seeders;

use App\Models\Product;
use App\Models\Customer;
use App\Models\Store;
use App\Models\Category;
use App\Models\Brand;
use App\Models\CustomerGroup;
use App\Models\Supplier;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class SampleDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Only seed if we're in development or if explicitly requested
        if (!app()->environment(['local', 'development']) && !config('app.seed_sample_data', false)) {
            return;
        }

        $this->createSampleProducts();
        $this->createSampleCustomers();
        $this->createSampleSuppliers();
    }

    private function createSampleProducts(): void
    {
        $categories = Category::all();
        $brands = Brand::all();
        $stores = Store::all();

        if ($categories->isEmpty() || $brands->isEmpty() || $stores->isEmpty()) {
            return;
        }

        $sampleProducts = [
            // Electronics
            ['name' => 'iPhone 15 Pro', 'category' => 'smartphones', 'brand' => 'apple', 'price' => 999.99, 'cost_price' => 750.00],
            ['name' => 'Samsung Galaxy S24', 'category' => 'smartphones', 'brand' => 'samsung', 'price' => 899.99, 'cost_price' => 680.00],
            ['name' => 'MacBook Air M2', 'category' => 'laptops', 'brand' => 'apple', 'price' => 1199.99, 'cost_price' => 900.00],
            ['name' => 'Dell XPS 13', 'category' => 'laptops', 'brand' => 'dell', 'price' => 1099.99, 'cost_price' => 820.00],
            ['name' => 'iPad Pro 12.9"', 'category' => 'tablets', 'brand' => 'apple', 'price' => 1099.99, 'cost_price' => 825.00],
            ['name' => 'Sony WH-1000XM5', 'category' => 'audio', 'brand' => 'sony', 'price' => 399.99, 'cost_price' => 280.00],
            ['name' => 'Bose QuietComfort 45', 'category' => 'audio', 'brand' => 'bose', 'price' => 329.99, 'cost_price' => 230.00],

            // Clothing
            ['name' => 'Nike Air Max 270', 'category' => 'shoes', 'brand' => 'nike', 'price' => 149.99, 'cost_price' => 75.00],
            ['name' => 'Adidas Ultraboost 22', 'category' => 'shoes', 'brand' => 'adidas', 'price' => 179.99, 'cost_price' => 90.00],
            ['name' => 'Levi\'s 501 Original Jeans', 'category' => 'mens-clothing', 'brand' => 'levis', 'price' => 89.99, 'cost_price' => 45.00],
            ['name' => 'Nike Dri-FIT T-Shirt', 'category' => 'mens-clothing', 'brand' => 'nike', 'price' => 29.99, 'cost_price' => 12.00],
            ['name' => 'H&M Summer Dress', 'category' => 'womens-clothing', 'brand' => 'hm', 'price' => 39.99, 'cost_price' => 15.00],

            // Food & Beverages
            ['name' => 'Coca-Cola 12-Pack', 'category' => 'beverages', 'brand' => 'coca-cola', 'price' => 5.99, 'cost_price' => 3.50],
            ['name' => 'Lay\'s Classic Chips', 'category' => 'snacks', 'brand' => 'pepsico', 'price' => 2.99, 'cost_price' => 1.20],
            ['name' => 'Organic Whole Milk', 'category' => 'dairy', 'brand' => 'generic', 'price' => 4.99, 'cost_price' => 2.80],
            ['name' => 'Frozen Pizza Margherita', 'category' => 'frozen-foods', 'brand' => 'generic', 'price' => 7.99, 'cost_price' => 4.50],

            // Books
            ['name' => 'The Great Gatsby', 'category' => 'fiction', 'brand' => 'penguin-random-house', 'price' => 14.99, 'cost_price' => 7.50],
            ['name' => 'Atomic Habits', 'category' => 'non-fiction', 'brand' => 'penguin-random-house', 'price' => 18.99, 'cost_price' => 9.50],
            ['name' => 'Harry Potter Set', 'category' => 'childrens-books', 'brand' => 'scholastic', 'price' => 59.99, 'cost_price' => 30.00],

            // Home & Garden
            ['name' => 'IKEA BILLY Bookshelf', 'category' => 'furniture', 'brand' => 'ikea', 'price' => 59.99, 'cost_price' => 25.00],
            ['name' => 'KitchenAid Stand Mixer', 'category' => 'appliances', 'brand' => 'kitchenaid', 'price' => 379.99, 'cost_price' => 220.00],
            ['name' => 'Garden Hose 50ft', 'category' => 'garden-tools', 'brand' => 'generic', 'price' => 29.99, 'cost_price' => 12.00],
        ];

        foreach ($sampleProducts as $productData) {
            $category = $categories->where('slug', $productData['category'])->first();
            $brand = $brands->where('slug', $productData['brand'])->first();

            if (!$category || !$brand) {
                continue;
            }

            $product = Product::create([
                'name' => $productData['name'],
                'slug' => Str::slug($productData['name']),
                'sku' => 'SKU-' . strtoupper(Str::random(8)),
                'barcode' => '123456789' . str_pad(rand(100, 999), 3, '0', STR_PAD_LEFT),
                'description' => 'Sample product: ' . $productData['name'],
                'price' => $productData['price'],
                'cost_price' => $productData['cost_price'],
                'category_id' => $category->id,
                'brand_id' => $brand->id,
                'is_active' => true,
                'track_quantity' => true,
                'weight' => rand(100, 5000) / 100, // Random weight between 1-50 kg
            ]);

            // Attach to all stores with random stock
            foreach ($stores as $store) {
                $stock = rand(10, 100);
                $product->stores()->attach($store->id, [
                    'stock_quantity' => $stock,
                    'min_stock_level' => rand(5, 15),
                    'max_stock_level' => $stock + rand(50, 200),
                ]);
            }
        }
    }

    private function createSampleCustomers(): void
    {
        $customerGroups = CustomerGroup::all();

        if ($customerGroups->isEmpty()) {
            return;
        }

        $sampleCustomers = [
            ['name' => 'John Smith', 'email' => '<EMAIL>', 'phone' => '******-0101'],
            ['name' => 'Sarah Johnson', 'email' => '<EMAIL>', 'phone' => '******-0102'],
            ['name' => 'Michael Brown', 'email' => '<EMAIL>', 'phone' => '******-0103'],
            ['name' => 'Emily Davis', 'email' => '<EMAIL>', 'phone' => '******-0104'],
            ['name' => 'David Wilson', 'email' => '<EMAIL>', 'phone' => '******-0105'],
            ['name' => 'Lisa Anderson', 'email' => '<EMAIL>', 'phone' => '******-0106'],
            ['name' => 'Robert Taylor', 'email' => '<EMAIL>', 'phone' => '******-0107'],
            ['name' => 'Jennifer Martinez', 'email' => '<EMAIL>', 'phone' => '******-0108'],
            ['name' => 'William Garcia', 'email' => '<EMAIL>', 'phone' => '******-0109'],
            ['name' => 'Amanda Rodriguez', 'email' => '<EMAIL>', 'phone' => '******-0110'],
        ];

        foreach ($sampleCustomers as $customerData) {
            Customer::create([
                'name' => $customerData['name'],
                'email' => $customerData['email'],
                'phone' => $customerData['phone'],
                'address' => rand(100, 9999) . ' Main Street',
                'city' => ['New York', 'Los Angeles', 'Chicago', 'Houston', 'Phoenix'][rand(0, 4)],
                'state' => ['NY', 'CA', 'IL', 'TX', 'AZ'][rand(0, 4)],
                'country' => 'United States',
                'postal_code' => str_pad(rand(10000, 99999), 5, '0', STR_PAD_LEFT),
                'customer_group_id' => $customerGroups->random()->id,
                'loyalty_points' => rand(0, 1000),
                'total_spent' => rand(0, 5000),
                'is_active' => true,
            ]);
        }
    }

    private function createSampleSuppliers(): void
    {
        $sampleSuppliers = [
            [
                'name' => 'Tech Distributors Inc.',
                'email' => '<EMAIL>',
                'phone' => '******-1001',
                'contact_person' => 'Mark Stevens',
                'address' => '123 Technology Blvd',
                'city' => 'San Francisco',
                'state' => 'CA',
                'country' => 'United States',
                'postal_code' => '94105',
            ],
            [
                'name' => 'Fashion Wholesale Co.',
                'email' => '<EMAIL>',
                'phone' => '******-1002',
                'contact_person' => 'Anna Chen',
                'address' => '456 Fashion Ave',
                'city' => 'New York',
                'state' => 'NY',
                'country' => 'United States',
                'postal_code' => '10001',
            ],
            [
                'name' => 'Food & Beverage Supply',
                'email' => '<EMAIL>',
                'phone' => '******-1003',
                'contact_person' => 'Carlos Rodriguez',
                'address' => '789 Industrial Way',
                'city' => 'Chicago',
                'state' => 'IL',
                'country' => 'United States',
                'postal_code' => '60601',
            ],
            [
                'name' => 'Book Publishers United',
                'email' => '<EMAIL>',
                'phone' => '******-1004',
                'contact_person' => 'Rebecca Thompson',
                'address' => '321 Publishing St',
                'city' => 'Boston',
                'state' => 'MA',
                'country' => 'United States',
                'postal_code' => '02101',
            ],
            [
                'name' => 'Home & Garden Supplies',
                'email' => '<EMAIL>',
                'phone' => '******-1005',
                'contact_person' => 'James Wilson',
                'address' => '654 Garden Center Dr',
                'city' => 'Atlanta',
                'state' => 'GA',
                'country' => 'United States',
                'postal_code' => '30301',
            ],
        ];

        foreach ($sampleSuppliers as $supplierData) {
            Supplier::create(array_merge($supplierData, [
                'is_active' => true,
                'payment_terms' => ['Net 30', 'Net 15', '2/10 Net 30'][rand(0, 2)],
                'tax_number' => 'TAX-' . strtoupper(Str::random(10)),
            ]));
        }
    }
}
