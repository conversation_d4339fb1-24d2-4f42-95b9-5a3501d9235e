<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class CustomerGroup extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'description',
        'discount_percentage',
        'is_active',
        'settings',
    ];

    protected $casts = [
        'discount_percentage' => 'decimal:2',
        'is_active' => 'boolean',
        'settings' => 'array',
    ];

    /**
     * Get the customers in this group.
     */
    public function customers()
    {
        return $this->hasMany(Customer::class);
    }

    /**
     * Scope for active customer groups.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Get the number of customers in this group.
     */
    public function getCustomerCountAttribute(): int
    {
        return $this->customers()->count();
    }

    /**
     * Get the total spent by customers in this group.
     */
    public function getTotalSpentAttribute(): float
    {
        return $this->customers()->sum('total_spent');
    }

    /**
     * Apply group discount to a price.
     */
    public function applyDiscount(float $price): float
    {
        if ($this->discount_percentage <= 0) {
            return $price;
        }

        return $price * (1 - ($this->discount_percentage / 100));
    }
}
