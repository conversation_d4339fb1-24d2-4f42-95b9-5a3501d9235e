<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\Store;
use App\Models\InventoryAdjustment;
use Illuminate\Http\Request;

class InventoryController extends Controller
{
    public function index(Request $request)
    {
        $query = Product::with(['category', 'brand', 'stores']);

        // Filter by store
        $storeId = $request->store_id ?? auth()->user()->primaryStore()?->id;
        $store = Store::find($storeId);

        if ($store) {
            $query->whereHas('stores', function ($q) use ($store) {
                $q->where('store_id', $store->id);
            });
        }

        // Search
        if ($request->filled('search')) {
            $query->search($request->search);
        }

        // Filter by stock status
        if ($request->filled('stock_status')) {
            if ($request->stock_status === 'low' && $store) {
                $query->lowStock($store);
            } elseif ($request->stock_status === 'out') {
                $query->whereHas('stores', function ($q) use ($store) {
                    $q->where('store_id', $store->id)
                      ->where('stock_quantity', 0);
                });
            }
        }

        $products = $query->paginate(20);
        $stores = Store::active()->get();

        return view('inventory.index', compact('products', 'stores', 'store'));
    }

    public function adjust(Request $request)
    {
        $validated = $request->validate([
            'product_id' => 'required|exists:products,id',
            'store_id' => 'required|exists:stores,id',
            'adjustment_type' => 'required|in:increase,decrease',
            'quantity' => 'required|integer|min:1',
            'reason' => 'required|string|max:255',
            'notes' => 'nullable|string',
        ]);

        $product = Product::findOrFail($validated['product_id']);
        $store = Store::findOrFail($validated['store_id']);

        $currentStock = $store->getProductStock($product);
        
        if ($validated['adjustment_type'] === 'increase') {
            $newStock = $currentStock + $validated['quantity'];
        } else {
            $newStock = max(0, $currentStock - $validated['quantity']);
        }

        // Update stock
        $store->products()->updateExistingPivot($product->id, [
            'stock_quantity' => $newStock,
        ]);

        // Log adjustment
        InventoryAdjustment::create([
            'store_id' => $store->id,
            'product_id' => $product->id,
            'adjustment_type' => $validated['adjustment_type'],
            'quantity' => $validated['quantity'],
            'reason' => $validated['reason'],
            'notes' => $validated['notes'],
            'user_id' => auth()->id(),
        ]);

        return redirect()->back()
            ->with('success', 'Inventory adjusted successfully.');
    }

    public function transfer(Request $request)
    {
        $validated = $request->validate([
            'product_id' => 'required|exists:products,id',
            'from_store_id' => 'required|exists:stores,id',
            'to_store_id' => 'required|exists:stores,id|different:from_store_id',
            'quantity' => 'required|integer|min:1',
            'notes' => 'nullable|string',
        ]);

        $product = Product::findOrFail($validated['product_id']);
        $fromStore = Store::findOrFail($validated['from_store_id']);
        $toStore = Store::findOrFail($validated['to_store_id']);

        $fromStock = $fromStore->getProductStock($product);
        
        if ($fromStock < $validated['quantity']) {
            return redirect()->back()
                ->with('error', 'Insufficient stock in source store.');
        }

        $toStock = $toStore->getProductStock($product);

        // Update stock quantities
        $fromStore->products()->updateExistingPivot($product->id, [
            'stock_quantity' => $fromStock - $validated['quantity'],
        ]);

        // Ensure product exists in destination store
        if (!$toStore->products()->where('product_id', $product->id)->exists()) {
            $toStore->products()->attach($product->id, [
                'stock_quantity' => $validated['quantity'],
                'min_stock_level' => 0,
                'max_stock_level' => 0,
            ]);
        } else {
            $toStore->products()->updateExistingPivot($product->id, [
                'stock_quantity' => $toStock + $validated['quantity'],
            ]);
        }

        // Log adjustments for both stores
        InventoryAdjustment::create([
            'store_id' => $fromStore->id,
            'product_id' => $product->id,
            'adjustment_type' => 'decrease',
            'quantity' => $validated['quantity'],
            'reason' => "Transfer to {$toStore->name}",
            'notes' => $validated['notes'],
            'user_id' => auth()->id(),
        ]);

        InventoryAdjustment::create([
            'store_id' => $toStore->id,
            'product_id' => $product->id,
            'adjustment_type' => 'increase',
            'quantity' => $validated['quantity'],
            'reason' => "Transfer from {$fromStore->name}",
            'notes' => $validated['notes'],
            'user_id' => auth()->id(),
        ]);

        return redirect()->back()
            ->with('success', 'Stock transferred successfully.');
    }

    public function lowStock(Request $request)
    {
        $storeId = $request->store_id ?? auth()->user()->primaryStore()?->id;
        $store = Store::find($storeId);

        if (!$store) {
            return redirect()->route('inventory.index')
                ->with('error', 'Please select a store.');
        }

        $lowStockProducts = $store->getLowStockProducts();
        $stores = Store::active()->get();

        return view('inventory.low-stock', compact('lowStockProducts', 'stores', 'store'));
    }
}
