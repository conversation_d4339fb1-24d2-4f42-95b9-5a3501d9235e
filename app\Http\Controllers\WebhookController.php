<?php

namespace App\Http\Controllers;

use App\Models\Tenant;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use <PERSON><PERSON>\Cashier\Http\Controllers\WebhookController as CashierController;

class WebhookController extends CashierController
{
    /**
     * Handle Stripe webhook.
     */
    public function stripe(Request $request)
    {
        return $this->handleWebhook($request);
    }

    /**
     * Handle customer subscription updated.
     */
    protected function handleCustomerSubscriptionUpdated(array $payload)
    {
        $subscription = $payload['data']['object'];
        $customerId = $subscription['customer'];

        $tenant = Tenant::where('stripe_id', $customerId)->first();

        if ($tenant) {
            // Update tenant subscription plan based on price ID
            $priceId = $subscription['items']['data'][0]['price']['id'];
            $plan = $this->getPlanFromPriceId($priceId);
            
            if ($plan) {
                $tenant->update(['subscription_plan' => $plan]);
            }

            Log::info('Tenant subscription updated', [
                'tenant_id' => $tenant->id,
                'plan' => $plan,
                'status' => $subscription['status'],
            ]);
        }

        return parent::handleCustomerSubscriptionUpdated($payload);
    }

    /**
     * Handle customer subscription deleted.
     */
    protected function handleCustomerSubscriptionDeleted(array $payload)
    {
        $subscription = $payload['data']['object'];
        $customerId = $subscription['customer'];

        $tenant = Tenant::where('stripe_id', $customerId)->first();

        if ($tenant) {
            // Downgrade to basic plan or suspend account
            $tenant->update(['subscription_plan' => 'basic']);

            Log::info('Tenant subscription cancelled', [
                'tenant_id' => $tenant->id,
            ]);
        }

        return parent::handleCustomerSubscriptionDeleted($payload);
    }

    /**
     * Handle invoice payment failed.
     */
    protected function handleInvoicePaymentFailed(array $payload)
    {
        $invoice = $payload['data']['object'];
        $customerId = $invoice['customer'];

        $tenant = Tenant::where('stripe_id', $customerId)->first();

        if ($tenant) {
            // Send notification about failed payment
            Log::warning('Payment failed for tenant', [
                'tenant_id' => $tenant->id,
                'invoice_id' => $invoice['id'],
            ]);

            // You can add email notification here
        }

        return parent::handleInvoicePaymentFailed($payload);
    }

    /**
     * Handle WooCommerce webhook.
     */
    public function woocommerce(Request $request)
    {
        // Verify webhook signature
        $signature = $request->header('X-WC-Webhook-Signature');
        $payload = $request->getContent();
        
        // Process WooCommerce webhook
        Log::info('WooCommerce webhook received', [
            'topic' => $request->header('X-WC-Webhook-Topic'),
            'source' => $request->header('X-WC-Webhook-Source'),
        ]);

        return response()->json(['status' => 'success']);
    }

    /**
     * Handle Shopify webhook.
     */
    public function shopify(Request $request)
    {
        // Verify webhook signature
        $signature = $request->header('X-Shopify-Hmac-Sha256');
        $payload = $request->getContent();
        
        // Process Shopify webhook
        Log::info('Shopify webhook received', [
            'topic' => $request->header('X-Shopify-Topic'),
            'shop_domain' => $request->header('X-Shopify-Shop-Domain'),
        ]);

        return response()->json(['status' => 'success']);
    }

    /**
     * Get plan name from Stripe price ID.
     */
    private function getPlanFromPriceId(string $priceId): ?string
    {
        $priceMapping = [
            env('STRIPE_BASIC_PRICE_ID') => 'basic',
            env('STRIPE_PROFESSIONAL_PRICE_ID') => 'professional',
            env('STRIPE_ENTERPRISE_PRICE_ID') => 'enterprise',
        ];

        return $priceMapping[$priceId] ?? null;
    }
}
