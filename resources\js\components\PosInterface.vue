<template>
  <div class="pos-interface">
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 h-screen">
      <!-- Product Selection Area -->
      <div class="lg:col-span-2 bg-white rounded-lg shadow-sm p-6">
        <div class="mb-6">
          <h2 class="text-2xl font-bold text-gray-900 mb-4">Products</h2>
          
          <!-- Search Bar -->
          <div class="mb-4">
            <input
              v-model="searchQuery"
              @input="searchProducts"
              type="text"
              placeholder="Search products by name, SKU, or barcode..."
              class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          <!-- Category Filter -->
          <div class="mb-4">
            <select
              v-model="selectedCategory"
              @change="filterProducts"
              class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Categories</option>
              <option v-for="category in categories" :key="category.id" :value="category.id">
                {{ category.name }}
              </option>
            </select>
          </div>
        </div>

        <!-- Product Grid -->
        <div class="grid grid-cols-2 md:grid-cols-3 xl:grid-cols-4 gap-4 max-h-96 overflow-y-auto">
          <div
            v-for="product in filteredProducts"
            :key="product.id"
            @click="addToCart(product)"
            class="product-card bg-gray-50 rounded-lg p-4 cursor-pointer hover:bg-gray-100 transition-colors"
          >
            <div class="aspect-square bg-gray-200 rounded-lg mb-3 flex items-center justify-center">
              <img
                v-if="product.image"
                :src="product.image"
                :alt="product.name"
                class="w-full h-full object-cover rounded-lg"
              />
              <span v-else class="text-gray-400 text-sm">No Image</span>
            </div>
            <h3 class="font-medium text-sm text-gray-900 mb-1 truncate">{{ product.name }}</h3>
            <p class="text-xs text-gray-500 mb-2">{{ product.sku }}</p>
            <div class="flex justify-between items-center">
              <span class="font-bold text-blue-600">${{ product.price }}</span>
              <span class="text-xs text-gray-500">Stock: {{ getProductStock(product) }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Cart and Checkout Area -->
      <div class="bg-white rounded-lg shadow-sm p-6">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-xl font-bold text-gray-900">Cart</h2>
          <button
            @click="clearCart"
            class="text-red-600 hover:text-red-800 text-sm"
          >
            Clear All
          </button>
        </div>

        <!-- Customer Selection -->
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 mb-2">Customer</label>
          <select
            v-model="selectedCustomer"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
          >
            <option value="">Walk-in Customer</option>
            <option v-for="customer in customers" :key="customer.id" :value="customer.id">
              {{ customer.name }} - {{ customer.phone }}
            </option>
          </select>
        </div>

        <!-- Cart Items -->
        <div class="cart-items max-h-64 overflow-y-auto mb-4">
          <div
            v-for="item in cart"
            :key="item.product_id"
            class="flex items-center justify-between py-3 border-b border-gray-200"
          >
            <div class="flex-1">
              <h4 class="font-medium text-sm text-gray-900">{{ item.name }}</h4>
              <p class="text-xs text-gray-500">{{ item.sku }}</p>
              <p class="text-sm font-bold text-blue-600">${{ item.price }}</p>
            </div>
            <div class="flex items-center space-x-2">
              <button
                @click="updateQuantity(item.product_id, item.quantity - 1)"
                class="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center hover:bg-gray-300"
              >
                -
              </button>
              <span class="w-8 text-center">{{ item.quantity }}</span>
              <button
                @click="updateQuantity(item.product_id, item.quantity + 1)"
                class="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center hover:bg-gray-300"
              >
                +
              </button>
              <button
                @click="removeFromCart(item.product_id)"
                class="w-8 h-8 rounded-full bg-red-100 text-red-600 flex items-center justify-center hover:bg-red-200"
              >
                ×
              </button>
            </div>
          </div>
        </div>

        <!-- Cart Summary -->
        <div class="border-t border-gray-200 pt-4 mb-4">
          <div class="flex justify-between text-sm mb-2">
            <span>Subtotal:</span>
            <span>${{ cartSubtotal.toFixed(2) }}</span>
          </div>
          <div class="flex justify-between text-sm mb-2">
            <span>Tax:</span>
            <span>${{ cartTax.toFixed(2) }}</span>
          </div>
          <div class="flex justify-between text-sm mb-2">
            <span>Discount:</span>
            <input
              v-model.number="discountAmount"
              type="number"
              min="0"
              step="0.01"
              class="w-20 px-2 py-1 text-right border border-gray-300 rounded"
            />
          </div>
          <div class="flex justify-between text-lg font-bold border-t border-gray-200 pt-2">
            <span>Total:</span>
            <span>${{ cartTotal.toFixed(2) }}</span>
          </div>
        </div>

        <!-- Checkout Button -->
        <button
          @click="openPaymentModal"
          :disabled="cart.length === 0"
          class="w-full bg-blue-600 text-white py-3 rounded-lg font-medium hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed"
        >
          Checkout ({{ cart.length }} items)
        </button>
      </div>
    </div>

    <!-- Payment Modal -->
    <payment-modal
      v-if="showPaymentModal"
      :total="cartTotal"
      :cart="cart"
      :customer-id="selectedCustomer"
      :store-id="storeId"
      :discount-amount="discountAmount"
      @close="closePaymentModal"
      @payment-complete="handlePaymentComplete"
    />
  </div>
</template>

<script>
export default {
  name: 'PosInterface',
  props: {
    initialProducts: {
      type: Array,
      default: () => []
    },
    initialCustomers: {
      type: Array,
      default: () => []
    },
    storeId: {
      type: Number,
      required: true
    }
  },
  data() {
    return {
      products: this.initialProducts,
      customers: this.initialCustomers,
      categories: [],
      cart: [],
      searchQuery: '',
      selectedCategory: '',
      selectedCustomer: '',
      discountAmount: 0,
      showPaymentModal: false,
      filteredProducts: this.initialProducts
    }
  },
  computed: {
    cartSubtotal() {
      return this.cart.reduce((total, item) => {
        return total + (item.quantity * item.price)
      }, 0)
    },
    cartTax() {
      return this.cart.reduce((total, item) => {
        const subtotal = item.quantity * item.price
        return total + (subtotal * (item.tax_rate / 100))
      }, 0)
    },
    cartTotal() {
      return this.cartSubtotal + this.cartTax - this.discountAmount
    }
  },
  methods: {
    async addToCart(product) {
      try {
        const response = await axios.post('/pos/add-to-cart', {
          product_id: product.id,
          quantity: 1,
          store_id: this.storeId
        })

        if (response.data.success) {
          this.cart = Object.values(response.data.cart)
          this.$toast.success(response.data.message)
        }
      } catch (error) {
        this.$toast.error(error.response?.data?.message || 'Error adding product to cart')
      }
    },
    async removeFromCart(productId) {
      try {
        const response = await axios.post('/pos/remove-from-cart', {
          product_id: productId
        })

        if (response.data.success) {
          this.cart = Object.values(response.data.cart)
          this.$toast.success(response.data.message)
        }
      } catch (error) {
        this.$toast.error('Error removing product from cart')
      }
    },
    updateQuantity(productId, newQuantity) {
      if (newQuantity <= 0) {
        this.removeFromCart(productId)
        return
      }

      const item = this.cart.find(item => item.product_id == productId)
      if (item) {
        const product = this.products.find(p => p.id == productId)
        const stock = this.getProductStock(product)
        
        if (newQuantity > stock) {
          this.$toast.error('Insufficient stock available')
          return
        }

        item.quantity = newQuantity
      }
    },
    clearCart() {
      this.cart = []
      // Clear server-side cart as well
      axios.post('/pos/clear-cart')
    },
    searchProducts() {
      if (this.searchQuery.length >= 2) {
        this.filterProducts()
      } else if (this.searchQuery.length === 0) {
        this.filteredProducts = this.products
      }
    },
    filterProducts() {
      let filtered = this.products

      if (this.searchQuery) {
        const query = this.searchQuery.toLowerCase()
        filtered = filtered.filter(product =>
          product.name.toLowerCase().includes(query) ||
          product.sku.toLowerCase().includes(query) ||
          (product.barcode && product.barcode.toLowerCase().includes(query))
        )
      }

      if (this.selectedCategory) {
        filtered = filtered.filter(product => product.category_id == this.selectedCategory)
      }

      this.filteredProducts = filtered
    },
    getProductStock(product) {
      const storeProduct = product.stores?.find(store => store.id === this.storeId)
      return storeProduct?.pivot?.stock_quantity || 0
    },
    openPaymentModal() {
      if (this.cart.length === 0) {
        this.$toast.error('Cart is empty')
        return
      }
      this.showPaymentModal = true
    },
    closePaymentModal() {
      this.showPaymentModal = false
    },
    handlePaymentComplete(result) {
      this.cart = []
      this.selectedCustomer = ''
      this.discountAmount = 0
      this.showPaymentModal = false
      this.$toast.success(`Sale completed! Sale #${result.sale_number}`)
      
      // Optionally redirect to receipt
      if (confirm('Would you like to view the receipt?')) {
        window.open(`/pos/receipt/${result.sale_id}`, '_blank')
      }
    }
  },
  mounted() {
    // Load categories
    axios.get('/api/categories').then(response => {
      this.categories = response.data.data
    })

    // Load cart from session
    axios.get('/pos/cart').then(response => {
      this.cart = Object.values(response.data.cart || {})
    })
  }
}
</script>

<style scoped>
.pos-interface {
  font-family: 'Inter', sans-serif;
}

.product-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.cart-items {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e0 #f7fafc;
}

.cart-items::-webkit-scrollbar {
  width: 6px;
}

.cart-items::-webkit-scrollbar-track {
  background: #f7fafc;
}

.cart-items::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 3px;
}
</style>
