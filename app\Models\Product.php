<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Product extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'description',
        'sku',
        'barcode',
        'price',
        'cost_price',
        'category_id',
        'brand_id',
        'unit',
        'weight',
        'dimensions',
        'image',
        'images',
        'is_active',
        'is_trackable',
        'tax_rate',
        'woocommerce_id',
        'shopify_id',
        'last_synced_at',
        'sync_status',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'cost_price' => 'decimal:2',
        'weight' => 'decimal:2',
        'tax_rate' => 'decimal:2',
        'is_active' => 'boolean',
        'is_trackable' => 'boolean',
        'dimensions' => 'array',
        'images' => 'array',
        'last_synced_at' => 'datetime',
    ];

    /**
     * Get the category that owns the product.
     */
    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * Get the brand that owns the product.
     */
    public function brand()
    {
        return $this->belongsTo(Brand::class);
    }

    /**
     * Get the stores that stock this product.
     */
    public function stores()
    {
        return $this->belongsToMany(Store::class, 'store_products')
            ->withPivot('stock_quantity', 'min_stock_level', 'max_stock_level')
            ->withTimestamps();
    }

    /**
     * Get the product variants.
     */
    public function variants()
    {
        return $this->hasMany(ProductVariant::class);
    }

    /**
     * Get the sale items for this product.
     */
    public function saleItems()
    {
        return $this->hasMany(SaleItem::class);
    }

    /**
     * Get the purchase order items for this product.
     */
    public function purchaseOrderItems()
    {
        return $this->hasMany(PurchaseOrderItem::class);
    }

    /**
     * Get the inventory adjustments for this product.
     */
    public function inventoryAdjustments()
    {
        return $this->hasMany(InventoryAdjustment::class);
    }

    /**
     * Get the stock transfers for this product.
     */
    public function stockTransfers()
    {
        return $this->hasMany(StockTransferItem::class);
    }

    /**
     * Get total stock across all stores.
     */
    public function getTotalStockAttribute(): int
    {
        return $this->stores()->sum('store_products.stock_quantity');
    }

    /**
     * Get stock for a specific store.
     */
    public function getStockForStore(Store $store): int
    {
        $pivot = $this->stores()->where('store_id', $store->id)->first()?->pivot;
        return $pivot ? $pivot->stock_quantity : 0;
    }

    /**
     * Check if product is in stock for a store.
     */
    public function isInStock(Store $store, int $quantity = 1): bool
    {
        return $this->getStockForStore($store) >= $quantity;
    }

    /**
     * Get the profit margin.
     */
    public function getProfitMarginAttribute(): float
    {
        if ($this->cost_price <= 0) {
            return 0;
        }
        
        return (($this->price - $this->cost_price) / $this->cost_price) * 100;
    }

    /**
     * Get the profit amount.
     */
    public function getProfitAttribute(): float
    {
        return $this->price - $this->cost_price;
    }

    /**
     * Scope for active products.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for trackable products.
     */
    public function scopeTrackable($query)
    {
        return $query->where('is_trackable', true);
    }

    /**
     * Scope for products with low stock.
     */
    public function scopeLowStock($query, Store $store = null)
    {
        if ($store) {
            return $query->whereHas('stores', function ($q) use ($store) {
                $q->where('store_id', $store->id)
                  ->whereColumn('store_products.stock_quantity', '<=', 'store_products.min_stock_level');
            });
        }

        return $query->whereHas('stores', function ($q) {
            $q->whereColumn('store_products.stock_quantity', '<=', 'store_products.min_stock_level');
        });
    }

    /**
     * Search products by name, SKU, or barcode.
     */
    public function scopeSearch($query, string $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('name', 'like', "%{$search}%")
              ->orWhere('sku', 'like', "%{$search}%")
              ->orWhere('barcode', 'like', "%{$search}%");
        });
    }
}
