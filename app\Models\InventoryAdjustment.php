<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class InventoryAdjustment extends Model
{
    use HasFactory;

    protected $fillable = [
        'store_id',
        'product_id',
        'adjustment_type',
        'quantity',
        'reason',
        'notes',
        'user_id',
    ];

    /**
     * Get the store for this adjustment.
     */
    public function store()
    {
        return $this->belongsTo(Store::class);
    }

    /**
     * Get the product for this adjustment.
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Get the user who made this adjustment.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope for increases.
     */
    public function scopeIncreases($query)
    {
        return $query->where('adjustment_type', 'increase');
    }

    /**
     * Scope for decreases.
     */
    public function scopeDecreases($query)
    {
        return $query->where('adjustment_type', 'decrease');
    }
}
