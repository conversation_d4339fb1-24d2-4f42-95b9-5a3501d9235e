<div>
    <h3 class="text-lg font-medium text-gray-900 mb-4">Application Configuration</h3>
    <p class="text-sm text-gray-600 mb-6">
        Configure your application settings and create the administrator account.
    </p>

    <form method="POST" action="{{ route('install.process') }}">
        @csrf
        <input type="hidden" name="step" value="3">

        <div class="space-y-6">
            <!-- Application Settings -->
            <div>
                <h4 class="text-sm font-medium text-gray-700 mb-3">Application Settings</h4>
                <div class="space-y-4">
                    <div>
                        <label for="app_name" class="block text-sm font-medium text-gray-700">Application Name</label>
                        <input type="text" name="app_name" id="app_name" value="{{ old('app_name', 'SyncPOS') }}" required
                            class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                        <p class="mt-1 text-xs text-gray-500">This will appear in the browser title and emails</p>
                    </div>

                    <div>
                        <label for="app_url" class="block text-sm font-medium text-gray-700">Application URL</label>
                        <input type="url" name="app_url" id="app_url" value="{{ old('app_url', request()->getSchemeAndHttpHost()) }}" required
                            class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                        <p class="mt-1 text-xs text-gray-500">The full URL where your application will be accessible</p>
                    </div>

                    <div>
                        <label for="app_env" class="block text-sm font-medium text-gray-700">Environment</label>
                        <select name="app_env" id="app_env" required
                            class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                            <option value="production" {{ old('app_env') === 'production' ? 'selected' : '' }}>Production</option>
                            <option value="local" {{ old('app_env') === 'local' ? 'selected' : '' }}>Local/Development</option>
                        </select>
                        <p class="mt-1 text-xs text-gray-500">Use 'Production' for live sites, 'Local' for development</p>
                    </div>
                </div>
            </div>

            <!-- Administrator Account -->
            <div>
                <h4 class="text-sm font-medium text-gray-700 mb-3">Administrator Account</h4>
                <div class="space-y-4">
                    <div>
                        <label for="admin_name" class="block text-sm font-medium text-gray-700">Full Name</label>
                        <input type="text" name="admin_name" id="admin_name" value="{{ old('admin_name', 'Administrator') }}" required
                            class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    </div>

                    <div>
                        <label for="admin_email" class="block text-sm font-medium text-gray-700">Email Address</label>
                        <input type="email" name="admin_email" id="admin_email" value="{{ old('admin_email') }}" required
                            class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                        <p class="mt-1 text-xs text-gray-500">This will be your login email</p>
                    </div>

                    <div>
                        <label for="admin_password" class="block text-sm font-medium text-gray-700">Password</label>
                        <input type="password" name="admin_password" id="admin_password" required
                            class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                        <p class="mt-1 text-xs text-gray-500">Minimum 8 characters</p>
                    </div>

                    <div>
                        <label for="admin_password_confirmation" class="block text-sm font-medium text-gray-700">Confirm Password</label>
                        <input type="password" name="admin_password_confirmation" id="admin_password_confirmation" required
                            class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    </div>
                </div>
            </div>

            <!-- Default Store -->
            <div>
                <h4 class="text-sm font-medium text-gray-700 mb-3">Default Store</h4>
                <div class="space-y-4">
                    <div>
                        <label for="store_name" class="block text-sm font-medium text-gray-700">Store Name</label>
                        <input type="text" name="store_name" id="store_name" value="{{ old('store_name', 'Main Store') }}" required
                            class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    </div>

                    <div>
                        <label for="store_email" class="block text-sm font-medium text-gray-700">Store Email</label>
                        <input type="email" name="store_email" id="store_email" value="{{ old('store_email') }}" required
                            class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                        <p class="mt-1 text-xs text-gray-500">This will appear on receipts and invoices</p>
                    </div>

                    <div>
                        <label for="store_phone" class="block text-sm font-medium text-gray-700">Store Phone (Optional)</label>
                        <input type="tel" name="store_phone" id="store_phone" value="{{ old('store_phone') }}"
                            class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    </div>
                </div>
            </div>
        </div>

        <div class="mt-8 bg-yellow-50 border border-yellow-200 rounded-md p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm text-yellow-800">
                        <strong>Important:</strong> This will create the database tables, admin user, and initial configuration. 
                        Make sure all information is correct before proceeding.
                    </p>
                </div>
            </div>
        </div>

        <div class="mt-6 flex space-x-3">
            <a href="{{ route('install.index', ['step' => 2]) }}" 
                class="flex-1 flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                ← Back to Database
            </a>
            <button type="submit"
                class="flex-1 flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                Install SyncPOS
            </button>
        </div>
    </form>
</div>

<script>
// Auto-fill store email with admin email
document.getElementById('admin_email').addEventListener('input', function() {
    const storeEmailField = document.getElementById('store_email');
    if (!storeEmailField.value) {
        storeEmailField.value = this.value;
    }
});

// Password strength indicator
document.getElementById('admin_password').addEventListener('input', function() {
    const password = this.value;
    const strength = getPasswordStrength(password);
    
    // Remove existing indicator
    const existingIndicator = document.querySelector('.password-strength');
    if (existingIndicator) {
        existingIndicator.remove();
    }
    
    if (password.length > 0) {
        const indicator = document.createElement('div');
        indicator.className = 'password-strength mt-1 text-xs';
        
        if (strength < 2) {
            indicator.className += ' text-red-600';
            indicator.textContent = 'Weak password';
        } else if (strength < 4) {
            indicator.className += ' text-yellow-600';
            indicator.textContent = 'Medium password';
        } else {
            indicator.className += ' text-green-600';
            indicator.textContent = 'Strong password';
        }
        
        this.parentNode.appendChild(indicator);
    }
});

function getPasswordStrength(password) {
    let strength = 0;
    if (password.length >= 8) strength++;
    if (/[a-z]/.test(password)) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/[0-9]/.test(password)) strength++;
    if (/[^A-Za-z0-9]/.test(password)) strength++;
    return strength;
}
</script>
