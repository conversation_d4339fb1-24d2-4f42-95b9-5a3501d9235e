<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\Sale;
use App\Models\Customer;
use App\Models\Store;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class DashboardController extends Controller
{
    public function index(Request $request)
    {
        $user = $request->user();
        $store = $user->primaryStore() ?? Store::active()->first();
        
        if (!$store) {
            return view('dashboard.no-store');
        }

        // Date range for statistics
        $startDate = now()->startOfMonth();
        $endDate = now()->endOfMonth();
        
        // Sales statistics
        $salesStats = $this->getSalesStats($store, $startDate, $endDate);
        
        // Inventory statistics
        $inventoryStats = $this->getInventoryStats($store);
        
        // Customer statistics
        $customerStats = $this->getCustomerStats($startDate, $endDate);
        
        // Recent sales
        $recentSales = Sale::with(['customer', 'cashier'])
            ->where('store_id', $store->id)
            ->latest()
            ->limit(10)
            ->get();
        
        // Low stock products
        $lowStockProducts = $store->getLowStockProducts()->take(10);
        
        // Top selling products
        $topProducts = $this->getTopSellingProducts($store, $startDate, $endDate);
        
        // Sales chart data (last 30 days)
        $salesChartData = $this->getSalesChartData($store);
        
        return view('dashboard.index', compact(
            'store',
            'salesStats',
            'inventoryStats', 
            'customerStats',
            'recentSales',
            'lowStockProducts',
            'topProducts',
            'salesChartData'
        ));
    }
    
    private function getSalesStats(Store $store, Carbon $startDate, Carbon $endDate): array
    {
        $salesQuery = Sale::where('store_id', $store->id)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->where('payment_status', 'paid');
            
        $todaySales = Sale::where('store_id', $store->id)
            ->whereDate('created_at', today())
            ->where('payment_status', 'paid');
            
        return [
            'total_sales' => $salesQuery->sum('total_amount'),
            'total_transactions' => $salesQuery->count(),
            'average_transaction' => $salesQuery->avg('total_amount') ?? 0,
            'today_sales' => $todaySales->sum('total_amount'),
            'today_transactions' => $todaySales->count(),
        ];
    }
    
    private function getInventoryStats(Store $store): array
    {
        $totalProducts = $store->products()->count();
        $lowStockCount = $store->products()
            ->whereColumn('store_products.stock_quantity', '<=', 'store_products.min_stock_level')
            ->count();
        $outOfStockCount = $store->products()
            ->where('store_products.stock_quantity', 0)
            ->count();
        $totalValue = $store->products()
            ->sum(DB::raw('store_products.stock_quantity * products.cost_price'));
            
        return [
            'total_products' => $totalProducts,
            'low_stock_count' => $lowStockCount,
            'out_of_stock_count' => $outOfStockCount,
            'inventory_value' => $totalValue,
        ];
    }
    
    private function getCustomerStats(Carbon $startDate, Carbon $endDate): array
    {
        return [
            'total_customers' => Customer::count(),
            'new_customers' => Customer::whereBetween('created_at', [$startDate, $endDate])->count(),
            'repeat_customers' => Customer::whereHas('sales', function ($query) use ($startDate, $endDate) {
                $query->whereBetween('created_at', [$startDate, $endDate]);
            }, '>', 1)->count(),
        ];
    }
    
    private function getTopSellingProducts(Store $store, Carbon $startDate, Carbon $endDate)
    {
        return Product::select('products.*', DB::raw('SUM(sale_items.quantity) as total_sold'))
            ->join('sale_items', 'products.id', '=', 'sale_items.product_id')
            ->join('sales', 'sale_items.sale_id', '=', 'sales.id')
            ->where('sales.store_id', $store->id)
            ->whereBetween('sales.created_at', [$startDate, $endDate])
            ->where('sales.payment_status', 'paid')
            ->groupBy('products.id')
            ->orderBy('total_sold', 'desc')
            ->limit(5)
            ->get();
    }
    
    private function getSalesChartData(Store $store): array
    {
        $data = Sale::where('store_id', $store->id)
            ->where('payment_status', 'paid')
            ->where('created_at', '>=', now()->subDays(30))
            ->select(
                DB::raw('DATE(created_at) as date'),
                DB::raw('SUM(total_amount) as total'),
                DB::raw('COUNT(*) as transactions')
            )
            ->groupBy('date')
            ->orderBy('date')
            ->get();
            
        return [
            'labels' => $data->pluck('date')->toArray(),
            'sales' => $data->pluck('total')->toArray(),
            'transactions' => $data->pluck('transactions')->toArray(),
        ];
    }
}
