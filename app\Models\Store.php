<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Store extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'code',
        'address',
        'city',
        'state',
        'country',
        'postal_code',
        'phone',
        'email',
        'is_active',
        'settings',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'settings' => 'array',
    ];

    /**
     * Get the users assigned to this store.
     */
    public function users()
    {
        return $this->belongsToMany(User::class, 'store_users')
            ->withPivot('is_primary', 'permissions')
            ->withTimestamps();
    }

    /**
     * Get the products in this store.
     */
    public function products()
    {
        return $this->belongsToMany(Product::class, 'store_products')
            ->withPivot('stock_quantity', 'min_stock_level', 'max_stock_level')
            ->withTimestamps();
    }

    /**
     * Get the sales made in this store.
     */
    public function sales()
    {
        return $this->hasMany(Sale::class);
    }

    /**
     * Get the inventory adjustments for this store.
     */
    public function inventoryAdjustments()
    {
        return $this->hasMany(InventoryAdjustment::class);
    }

    /**
     * Get the stock transfers from this store.
     */
    public function stockTransfersFrom()
    {
        return $this->hasMany(StockTransfer::class, 'from_store_id');
    }

    /**
     * Get the stock transfers to this store.
     */
    public function stockTransfersTo()
    {
        return $this->hasMany(StockTransfer::class, 'to_store_id');
    }

    /**
     * Get the current stock quantity for a product.
     */
    public function getProductStock(Product $product): int
    {
        $pivot = $this->products()->where('product_id', $product->id)->first()?->pivot;
        return $pivot ? $pivot->stock_quantity : 0;
    }

    /**
     * Update stock quantity for a product.
     */
    public function updateProductStock(Product $product, int $quantity, string $reason = null): void
    {
        $this->products()->updateExistingPivot($product->id, [
            'stock_quantity' => $quantity,
        ]);

        // Log inventory adjustment
        InventoryAdjustment::create([
            'store_id' => $this->id,
            'product_id' => $product->id,
            'adjustment_type' => $quantity >= 0 ? 'increase' : 'decrease',
            'quantity' => abs($quantity),
            'reason' => $reason ?? 'Manual adjustment',
            'user_id' => auth()->id(),
        ]);
    }

    /**
     * Check if store has low stock products.
     */
    public function hasLowStockProducts(): bool
    {
        return $this->products()
            ->whereColumn('store_products.stock_quantity', '<=', 'store_products.min_stock_level')
            ->exists();
    }

    /**
     * Get low stock products.
     */
    public function getLowStockProducts()
    {
        return $this->products()
            ->whereColumn('store_products.stock_quantity', '<=', 'store_products.min_stock_level')
            ->get();
    }
}
