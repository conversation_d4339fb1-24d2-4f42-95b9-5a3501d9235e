<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use <PERSON>tie\Permission\Traits\HasRoles;

class User extends Authenticatable implements MustVerifyEmail
{
    use HasApiTokens, HasFactory, Notifiable, HasRoles;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'phone',
        'avatar',
        'is_active',
        'last_login_at',
        'settings',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'last_login_at' => 'datetime',
            'is_active' => 'boolean',
            'settings' => 'array',
            'password' => 'hashed',
        ];
    }

    /**
     * Get the stores this user manages.
     */
    public function stores()
    {
        return $this->belongsToMany(Store::class, 'store_users');
    }

    /**
     * Get the sales made by this user.
     */
    public function sales()
    {
        return $this->hasMany(Sale::class, 'cashier_id');
    }

    /**
     * Check if user is an admin.
     */
    public function isAdmin(): bool
    {
        return $this->hasRole('admin');
    }

    /**
     * Check if user is a manager.
     */
    public function isManager(): bool
    {
        return $this->hasRole(['admin', 'manager']);
    }

    /**
     * Check if user is a cashier.
     */
    public function isCashier(): bool
    {
        return $this->hasRole('cashier');
    }

    /**
     * Get user's primary store.
     */
    public function primaryStore()
    {
        return $this->stores()->wherePivot('is_primary', true)->first();
    }

    /**
     * Check if user can access store.
     */
    public function canAccessStore(Store $store): bool
    {
        return $this->isAdmin() || $this->stores->contains($store);
    }

    /**
     * Check if user has access to store (alias for canAccessStore).
     */
    public function hasAccessToStore(Store $store): bool
    {
        return $this->canAccessStore($store);
    }

    /**
     * Get user's permissions for a specific store.
     */
    public function getStorePermissions(Store $store): array
    {
        if ($this->isAdmin()) {
            return ['all'];
        }

        $pivot = $this->stores()->wherePivot('store_id', $store->id)->first()?->pivot;
        return $pivot ? json_decode($pivot->permissions, true) : [];
    }

    /**
     * Update last login timestamp.
     */
    public function updateLastLogin(): void
    {
        $this->update(['last_login_at' => now()]);
    }
}
