<?php
session_start();

// Check if already installed
if (file_exists('../.env') && !isset($_GET['force'])) {
    header('Location: ../public/index.php');
    exit;
}

$step = $_GET['step'] ?? 1;
$error = $_SESSION['error'] ?? null;
$success = $_SESSION['success'] ?? null;
unset($_SESSION['error'], $_SESSION['success']);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SyncPOS Installation</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #f8fafc; color: #374151; line-height: 1.6; }
        .container { max-width: 600px; margin: 2rem auto; padding: 0 1rem; }
        .card { background: white; border-radius: 12px; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1); overflow: hidden; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 2rem; text-align: center; }
        .header h1 { font-size: 2rem; margin-bottom: 0.5rem; }
        .header p { opacity: 0.9; }
        .content { padding: 2rem; }
        .step-indicator { display: flex; justify-content: center; margin-bottom: 2rem; }
        .step { width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 0.5rem; font-weight: bold; }
        .step.active { background: #667eea; color: white; }
        .step.completed { background: #10b981; color: white; }
        .step.pending { background: #e5e7eb; color: #6b7280; }
        .form-group { margin-bottom: 1.5rem; }
        .form-group label { display: block; margin-bottom: 0.5rem; font-weight: 600; color: #374151; }
        .form-group input, .form-group select { width: 100%; padding: 0.75rem; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 1rem; transition: border-color 0.2s; }
        .form-group input:focus, .form-group select:focus { outline: none; border-color: #667eea; }
        .btn { display: inline-block; padding: 0.75rem 1.5rem; background: #667eea; color: white; text-decoration: none; border-radius: 8px; border: none; font-size: 1rem; cursor: pointer; transition: background 0.2s; }
        .btn:hover { background: #5a67d8; }
        .btn-secondary { background: #6b7280; }
        .btn-secondary:hover { background: #4b5563; }
        .alert { padding: 1rem; border-radius: 8px; margin-bottom: 1rem; }
        .alert-error { background: #fef2f2; color: #dc2626; border: 1px solid #fecaca; }
        .alert-success { background: #f0fdf4; color: #16a34a; border: 1px solid #bbf7d0; }
        .alert-info { background: #eff6ff; color: #2563eb; border: 1px solid #bfdbfe; }
        .requirements { list-style: none; }
        .requirements li { padding: 0.5rem 0; display: flex; align-items: center; }
        .requirements .check { margin-right: 0.5rem; font-weight: bold; }
        .requirements .pass { color: #16a34a; }
        .requirements .fail { color: #dc2626; }
        .progress-bar { width: 100%; height: 8px; background: #e5e7eb; border-radius: 4px; overflow: hidden; margin-bottom: 2rem; }
        .progress-fill { height: 100%; background: linear-gradient(90deg, #667eea, #764ba2); transition: width 0.3s; }
        .text-center { text-align: center; }
        .mt-2 { margin-top: 1rem; }
        .small { font-size: 0.875rem; color: #6b7280; }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <div class="header">
                <h1>🚀 SyncPOS</h1>
                <p>Point of Sale & Inventory Management System</p>
            </div>
            
            <div class="content">
                <?php if ($error): ?>
                    <div class="alert alert-error"><?= htmlspecialchars($error) ?></div>
                <?php endif; ?>
                
                <?php if ($success): ?>
                    <div class="alert alert-success"><?= htmlspecialchars($success) ?></div>
                <?php endif; ?>

                <div class="step-indicator">
                    <div class="step <?= $step >= 1 ? ($step > 1 ? 'completed' : 'active') : 'pending' ?>">1</div>
                    <div class="step <?= $step >= 2 ? ($step > 2 ? 'completed' : 'active') : 'pending' ?>">2</div>
                    <div class="step <?= $step >= 3 ? ($step > 3 ? 'completed' : 'active') : 'pending' ?>">3</div>
                    <div class="step <?= $step >= 4 ? 'active' : 'pending' ?>">4</div>
                </div>

                <div class="progress-bar">
                    <div class="progress-fill" style="width: <?= ($step / 4) * 100 ?>%"></div>
                </div>

                <?php if ($step == 1): ?>
                    <h2>System Requirements</h2>
                    <p class="small">Checking if your server meets the requirements...</p>
                    
                    <ul class="requirements">
                        <?php
                        $requirements = [
                            'PHP Version >= 8.1' => version_compare(PHP_VERSION, '8.1.0', '>='),
                            'PDO Extension' => extension_loaded('pdo'),
                            'PDO MySQL Extension' => extension_loaded('pdo_mysql'),
                            'OpenSSL Extension' => extension_loaded('openssl'),
                            'Mbstring Extension' => extension_loaded('mbstring'),
                            'Tokenizer Extension' => extension_loaded('tokenizer'),
                            'XML Extension' => extension_loaded('xml'),
                            'Ctype Extension' => extension_loaded('ctype'),
                            'JSON Extension' => extension_loaded('json'),
                            'BCMath Extension' => extension_loaded('bcmath'),
                            'Fileinfo Extension' => extension_loaded('fileinfo'),
                        ];
                        
                        $allPassed = true;
                        foreach ($requirements as $requirement => $passed) {
                            $allPassed = $allPassed && $passed;
                            echo '<li><span class="check ' . ($passed ? 'pass">✓' : 'fail">✗') . '</span>' . $requirement . '</li>';
                        }
                        ?>
                    </ul>

                    <?php if ($allPassed): ?>
                        <div class="alert alert-success">✅ All requirements met! You can proceed with the installation.</div>
                        <div class="text-center mt-2">
                            <a href="?step=2" class="btn">Continue Installation</a>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-error">❌ Some requirements are not met. Please contact your hosting provider to enable the missing extensions.</div>
                    <?php endif; ?>

                <?php elseif ($step == 2): ?>
                    <h2>Database Configuration</h2>
                    <p class="small">Enter your database connection details</p>
                    
                    <form method="POST" action="process.php">
                        <input type="hidden" name="step" value="2">
                        
                        <div class="form-group">
                            <label for="db_host">Database Host</label>
                            <input type="text" id="db_host" name="db_host" value="localhost" required>
                            <small class="small">Usually 'localhost' for most hosting providers</small>
                        </div>
                        
                        <div class="form-group">
                            <label for="db_name">Database Name</label>
                            <input type="text" id="db_name" name="db_name" required>
                            <small class="small">The name of your MySQL database</small>
                        </div>
                        
                        <div class="form-group">
                            <label for="db_user">Database Username</label>
                            <input type="text" id="db_user" name="db_user" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="db_pass">Database Password</label>
                            <input type="password" id="db_pass" name="db_pass">
                        </div>
                        
                        <div class="form-group">
                            <label for="db_port">Database Port</label>
                            <input type="text" id="db_port" name="db_port" value="3306">
                            <small class="small">Usually 3306 for MySQL</small>
                        </div>
                        
                        <div class="text-center">
                            <button type="submit" class="btn">Test Connection & Continue</button>
                        </div>
                    </form>

                <?php elseif ($step == 3): ?>
                    <h2>Application Settings</h2>
                    <p class="small">Configure your application settings</p>
                    
                    <form method="POST" action="process.php">
                        <input type="hidden" name="step" value="3">
                        
                        <div class="form-group">
                            <label for="app_name">Application Name</label>
                            <input type="text" id="app_name" name="app_name" value="SyncPOS" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="app_url">Application URL</label>
                            <input type="url" id="app_url" name="app_url" value="<?= (isset($_SERVER['HTTPS']) ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) ?>" required>
                            <small class="small">The full URL where your application will be accessible</small>
                        </div>
                        
                        <div class="form-group">
                            <label for="app_env">Environment</label>
                            <select id="app_env" name="app_env" required>
                                <option value="production">Production</option>
                                <option value="local">Local Development</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="admin_email">Admin Email</label>
                            <input type="email" id="admin_email" name="admin_email" required>
                            <small class="small">This will be used for the first admin account</small>
                        </div>
                        
                        <div class="form-group">
                            <label for="admin_password">Admin Password</label>
                            <input type="password" id="admin_password" name="admin_password" required minlength="8">
                            <small class="small">Minimum 8 characters</small>
                        </div>
                        
                        <div class="text-center">
                            <button type="submit" class="btn">Install SyncPOS</button>
                        </div>
                    </form>

                <?php elseif ($step == 4): ?>
                    <h2>🎉 Installation Complete!</h2>
                    
                    <div class="alert alert-success">
                        <strong>Congratulations!</strong> SyncPOS has been successfully installed.
                    </div>
                    
                    <div class="alert alert-info">
                        <strong>Important:</strong> For security reasons, please delete the <code>/install</code> folder from your server.
                    </div>
                    
                    <h3>What's Next?</h3>
                    <ul style="margin: 1rem 0; padding-left: 2rem;">
                        <li>Login with your admin credentials</li>
                        <li>Create your first store</li>
                        <li>Add products to your inventory</li>
                        <li>Start making sales!</li>
                    </ul>
                    
                    <div class="text-center">
                        <a href="../public" class="btn">Access Your SyncPOS</a>
                        <a href="../public/admin" class="btn btn-secondary">Admin Panel</a>
                    </div>
                    
                    <div class="mt-2 text-center">
                        <small class="small">
                            Need help? Check our documentation or contact support.
                        </small>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</body>
</html>
