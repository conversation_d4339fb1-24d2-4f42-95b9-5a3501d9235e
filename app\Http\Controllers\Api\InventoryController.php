<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\Store;
use App\Models\InventoryAdjustment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class InventoryController extends Controller
{
    public function index(Request $request)
    {
        $query = Product::with(['category', 'brand', 'stores']);

        // Filter by store
        $storeId = $request->store_id ?? auth()->user()->primaryStore()?->id;
        $store = Store::find($storeId);

        if ($store) {
            $query->whereHas('stores', function ($q) use ($store) {
                $q->where('store_id', $store->id);
            });
        }

        // Search
        if ($request->filled('search')) {
            $query->search($request->search);
        }

        // Filter by category
        if ($request->filled('category_id')) {
            $query->where('category_id', $request->category_id);
        }

        // Filter by stock status
        if ($request->filled('stock_status')) {
            if ($request->stock_status === 'low' && $store) {
                $query->lowStock($store);
            } elseif ($request->stock_status === 'out' && $store) {
                $query->whereHas('stores', function ($q) use ($store) {
                    $q->where('store_id', $store->id)
                      ->where('stock_quantity', 0);
                });
            }
        }

        $products = $query->paginate($request->per_page ?? 20);

        // Add stock information for each product
        if ($store) {
            $products->getCollection()->transform(function ($product) use ($store) {
                $pivot = $product->stores->where('id', $store->id)->first()?->pivot;
                $product->current_stock = $pivot ? $pivot->stock_quantity : 0;
                $product->min_stock_level = $pivot ? $pivot->min_stock_level : 0;
                $product->max_stock_level = $pivot ? $pivot->max_stock_level : 0;
                $product->is_low_stock = $product->current_stock <= $product->min_stock_level;
                unset($product->stores); // Remove to reduce response size
                return $product;
            });
        }

        return response()->json([
            'data' => $products->items(),
            'meta' => [
                'current_page' => $products->currentPage(),
                'last_page' => $products->lastPage(),
                'per_page' => $products->perPage(),
                'total' => $products->total(),
            ],
            'store' => $store ? [
                'id' => $store->id,
                'name' => $store->name,
            ] : null,
        ]);
    }

    public function adjust(Request $request)
    {
        $validated = $request->validate([
            'product_id' => 'required|exists:products,id',
            'store_id' => 'required|exists:stores,id',
            'adjustment_type' => 'required|in:increase,decrease',
            'quantity' => 'required|integer|min:1',
            'reason' => 'required|string|max:255',
            'notes' => 'nullable|string',
        ]);

        try {
            DB::beginTransaction();

            $product = Product::findOrFail($validated['product_id']);
            $store = Store::findOrFail($validated['store_id']);

            // Check if user has access to this store
            if (!auth()->user()->hasAccessToStore($store)) {
                return response()->json([
                    'message' => 'You do not have access to this store.',
                ], 403);
            }

            $currentStock = $store->getProductStock($product);
            
            if ($validated['adjustment_type'] === 'increase') {
                $newStock = $currentStock + $validated['quantity'];
            } else {
                $newStock = max(0, $currentStock - $validated['quantity']);
            }

            // Update stock
            $store->products()->updateExistingPivot($product->id, [
                'stock_quantity' => $newStock,
            ]);

            // Log adjustment
            InventoryAdjustment::create([
                'store_id' => $store->id,
                'product_id' => $product->id,
                'adjustment_type' => $validated['adjustment_type'],
                'quantity' => $validated['quantity'],
                'previous_quantity' => $currentStock,
                'new_quantity' => $newStock,
                'reason' => $validated['reason'],
                'notes' => $validated['notes'],
                'user_id' => auth()->id(),
            ]);

            DB::commit();

            return response()->json([
                'message' => 'Inventory adjusted successfully.',
                'data' => [
                    'product_id' => $product->id,
                    'product_name' => $product->name,
                    'previous_stock' => $currentStock,
                    'new_stock' => $newStock,
                    'adjustment_type' => $validated['adjustment_type'],
                    'quantity' => $validated['quantity'],
                ],
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            
            return response()->json([
                'message' => 'Inventory adjustment failed.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function lowStock(Request $request)
    {
        $storeId = $request->store_id ?? auth()->user()->primaryStore()?->id;
        $store = Store::find($storeId);

        if (!$store) {
            return response()->json([
                'message' => 'Store not found.',
            ], 404);
        }

        // Check if user has access to this store
        if (!auth()->user()->hasAccessToStore($store)) {
            return response()->json([
                'message' => 'You do not have access to this store.',
            ], 403);
        }

        $lowStockProducts = $store->products()
            ->with(['category', 'brand'])
            ->whereColumn('store_products.stock_quantity', '<=', 'store_products.min_stock_level')
            ->get()
            ->map(function ($product) use ($store) {
                $pivot = $product->stores->where('id', $store->id)->first()?->pivot;
                return [
                    'id' => $product->id,
                    'name' => $product->name,
                    'sku' => $product->sku,
                    'category' => $product->category?->name,
                    'brand' => $product->brand?->name,
                    'current_stock' => $pivot ? $pivot->stock_quantity : 0,
                    'min_stock_level' => $pivot ? $pivot->min_stock_level : 0,
                    'max_stock_level' => $pivot ? $pivot->max_stock_level : 0,
                    'price' => $product->price,
                    'cost_price' => $product->cost_price,
                ];
            });

        return response()->json([
            'data' => $lowStockProducts,
            'store' => [
                'id' => $store->id,
                'name' => $store->name,
            ],
            'count' => $lowStockProducts->count(),
        ]);
    }

    public function transfer(Request $request)
    {
        $validated = $request->validate([
            'product_id' => 'required|exists:products,id',
            'from_store_id' => 'required|exists:stores,id',
            'to_store_id' => 'required|exists:stores,id|different:from_store_id',
            'quantity' => 'required|integer|min:1',
            'notes' => 'nullable|string',
        ]);

        try {
            DB::beginTransaction();

            $product = Product::findOrFail($validated['product_id']);
            $fromStore = Store::findOrFail($validated['from_store_id']);
            $toStore = Store::findOrFail($validated['to_store_id']);

            // Check if user has access to both stores
            if (!auth()->user()->hasAccessToStore($fromStore) || !auth()->user()->hasAccessToStore($toStore)) {
                return response()->json([
                    'message' => 'You do not have access to one or both stores.',
                ], 403);
            }

            $fromStock = $fromStore->getProductStock($product);
            
            if ($fromStock < $validated['quantity']) {
                return response()->json([
                    'message' => 'Insufficient stock in source store.',
                ], 400);
            }

            $toStock = $toStore->getProductStock($product);

            // Update stock in both stores
            $fromStore->products()->updateExistingPivot($product->id, [
                'stock_quantity' => $fromStock - $validated['quantity'],
            ]);

            $toStore->products()->updateExistingPivot($product->id, [
                'stock_quantity' => $toStock + $validated['quantity'],
            ]);

            // Log adjustments for both stores
            InventoryAdjustment::create([
                'store_id' => $fromStore->id,
                'product_id' => $product->id,
                'adjustment_type' => 'decrease',
                'quantity' => $validated['quantity'],
                'previous_quantity' => $fromStock,
                'new_quantity' => $fromStock - $validated['quantity'],
                'reason' => "Transfer to {$toStore->name}",
                'notes' => $validated['notes'],
                'user_id' => auth()->id(),
            ]);

            InventoryAdjustment::create([
                'store_id' => $toStore->id,
                'product_id' => $product->id,
                'adjustment_type' => 'increase',
                'quantity' => $validated['quantity'],
                'previous_quantity' => $toStock,
                'new_quantity' => $toStock + $validated['quantity'],
                'reason' => "Transfer from {$fromStore->name}",
                'notes' => $validated['notes'],
                'user_id' => auth()->id(),
            ]);

            DB::commit();

            return response()->json([
                'message' => 'Inventory transfer completed successfully.',
                'data' => [
                    'product_name' => $product->name,
                    'quantity' => $validated['quantity'],
                    'from_store' => $fromStore->name,
                    'to_store' => $toStore->name,
                    'from_stock_remaining' => $fromStock - $validated['quantity'],
                    'to_stock_new' => $toStock + $validated['quantity'],
                ],
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            
            return response()->json([
                'message' => 'Inventory transfer failed.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
