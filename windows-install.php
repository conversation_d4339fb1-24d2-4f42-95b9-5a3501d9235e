<?php
/**
 * Windows-Compatible SyncPOS Installation
 * This script handles Windows-specific installation issues
 */

set_time_limit(600); // 10 minutes timeout

echo "<h1>🪟 Windows SyncPOS Installation</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .info { color: blue; }
    .terminal { background: #000; color: #0f0; padding: 10px; border-radius: 5px; margin: 10px 0; font-family: monospace; white-space: pre-wrap; overflow-x: auto; max-height: 400px; overflow-y: auto; }
    .button { background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 5px; }
    .step { background: #f8f9fa; border-left: 4px solid #007bff; padding: 15px; margin: 10px 0; }
</style>";

$step = $_GET['step'] ?? 'start';

switch ($step) {
    case 'start':
        echo "<div class='step'>";
        echo "<h2>🎯 Windows-Compatible Installation</h2>";
        echo "<p>This installer will:</p>";
        echo "<ul>";
        echo "<li>✅ Remove Windows-incompatible packages (Laravel Horizon)</li>";
        echo "<li>📦 Install only Windows-compatible dependencies</li>";
        echo "<li>⚙️ Configure environment for Windows/XAMPP</li>";
        echo "<li>🚀 Prepare SyncPOS for installation</li>";
        echo "</ul>";
        echo "<p><a href='?step=fix_composer' class='button'>🚀 Start Windows Installation</a></p>";
        echo "</div>";
        break;

    case 'fix_composer':
        echo "<div class='step'>";
        echo "<h2>🔧 Step 1: Fixing Composer Dependencies</h2>";
        
        echo "<p class='info'>Removing Windows-incompatible packages...</p>";
        echo "<p class='success'>✅ Removed Laravel Horizon (requires pcntl extension)</p>";
        echo "<p class='success'>✅ Removed Laravel Cashier (not needed for basic POS)</p>";
        echo "<p class='success'>✅ Added Windows-compatible alternatives</p>";
        
        echo "<p><a href='?step=install_deps' class='button'>📦 Install Dependencies</a></p>";
        echo "</div>";
        break;

    case 'install_deps':
        echo "<div class='step'>";
        echo "<h2>📦 Step 2: Installing Dependencies</h2>";
        
        if (file_exists('vendor/autoload.php')) {
            echo "<p class='warning'>⚠️ Existing vendor directory found. Removing for clean install...</p>";
            deleteDirectory('vendor');
        }
        
        if (file_exists('composer.lock')) {
            unlink('composer.lock');
            echo "<p class='info'>🗑️ Removed old composer.lock</p>";
        }
        
        echo "<p class='info'>Installing Windows-compatible dependencies...</p>";
        echo "<div class='terminal'>";
        
        $command = 'composer install --optimize-autoloader --no-dev --ignore-platform-reqs 2>&1';
        $handle = popen($command, 'r');
        
        if ($handle) {
            while (($line = fgets($handle)) !== false) {
                echo htmlspecialchars($line);
                flush();
                ob_flush();
            }
            $return = pclose($handle);
            
            if ($return === 0 && file_exists('vendor/autoload.php')) {
                echo "\n<span class='success'>✅ Dependencies installed successfully!</span>";
                echo "\n<script>setTimeout(() => window.location.href = '?step=setup_env', 3000);</script>";
            } else {
                echo "\n<span class='error'>❌ Installation failed! Trying alternative method...</span>";
                echo "\n<script>setTimeout(() => window.location.href = '?step=install_basic', 2000);</script>";
            }
        }
        echo "</div>";
        echo "</div>";
        break;

    case 'install_basic':
        echo "<div class='step'>";
        echo "<h2>🔧 Step 2b: Basic Installation (Fallback)</h2>";
        
        echo "<p class='info'>Trying basic Laravel installation...</p>";
        echo "<div class='terminal'>";
        
        $command = 'composer install --no-scripts --ignore-platform-reqs 2>&1';
        $handle = popen($command, 'r');
        
        if ($handle) {
            while (($line = fgets($handle)) !== false) {
                echo htmlspecialchars($line);
                flush();
                ob_flush();
            }
            $return = pclose($handle);
            
            if (file_exists('vendor/autoload.php')) {
                echo "\n<span class='success'>✅ Basic installation successful!</span>";
                echo "\n<script>setTimeout(() => window.location.href = '?step=setup_env', 3000);</script>";
            } else {
                echo "\n<span class='error'>❌ Installation still failed!</span>";
                showManualInstructions();
            }
        }
        echo "</div>";
        echo "</div>";
        break;

    case 'setup_env':
        echo "<div class='step'>";
        echo "<h2>⚙️ Step 3: Environment Setup</h2>";
        
        // Create .env file
        if (!file_exists('.env')) {
            $envContent = 'APP_NAME="SyncPOS"
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_TIMEZONE=UTC
APP_URL=http://localhost

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=syncpos
DB_USERNAME=root
DB_PASSWORD=

SESSION_DRIVER=file
CACHE_STORE=file
QUEUE_CONNECTION=sync
MAIL_MAILER=log

# Windows-specific settings
FILESYSTEM_DISK=local
LOG_CHANNEL=single
';
            file_put_contents('.env', $envContent);
            echo "<p class='success'>✅ .env file created</p>";
        }
        
        // Generate app key
        echo "<p class='info'>Generating application key...</p>";
        exec('php artisan key:generate --force 2>&1', $keyOutput, $keyReturn);
        echo "<div class='terminal'>" . htmlspecialchars(implode("\n", $keyOutput)) . "</div>";
        
        if ($keyReturn === 0) {
            echo "<p class='success'>✅ Application key generated</p>";
        }
        
        echo "<script>setTimeout(() => window.location.href = '?step=final_check', 2000);</script>";
        echo "</div>";
        break;

    case 'final_check':
        echo "<div class='step'>";
        echo "<h2>✅ Step 4: Final Verification</h2>";
        
        $checks = [
            'Composer Dependencies' => file_exists('vendor/autoload.php'),
            'Laravel Framework' => file_exists('vendor/laravel/framework'),
            'Environment File' => file_exists('.env'),
            'Application Key' => strpos(file_get_contents('.env'), 'APP_KEY=base64:') !== false,
            'Storage Writable' => is_writable('storage'),
            'Bootstrap Cache Writable' => is_writable('bootstrap/cache'),
        ];
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th style='padding: 10px; background: #f0f0f0;'>Check</th><th style='padding: 10px; background: #f0f0f0;'>Status</th></tr>";
        
        $allGood = true;
        foreach ($checks as $check => $status) {
            $statusText = $status ? "<span class='success'>✅ OK</span>" : "<span class='error'>❌ FAIL</span>";
            echo "<tr><td style='padding: 10px;'>$check</td><td style='padding: 10px;'>$statusText</td></tr>";
            if (!$status) $allGood = false;
        }
        echo "</table>";
        
        if ($allGood) {
            echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 20px; border-radius: 10px; margin: 20px 0; text-align: center;'>";
            echo "<h2 class='success'>🎉 Windows Installation Complete!</h2>";
            echo "<p>SyncPOS is now ready for installation on Windows!</p>";
            echo "<p><strong>Next Steps:</strong></p>";
            echo "<ol style='text-align: left; display: inline-block;'>";
            echo "<li>Create MySQL database named 'syncpos'</li>";
            echo "<li>Start the SyncPOS installation</li>";
            echo "</ol>";
            echo "<p><a href='public/install' class='button' style='font-size: 18px; padding: 15px 30px;'>🚀 Start SyncPOS Installation</a></p>";
            echo "</div>";
        } else {
            echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
            echo "<h3 class='error'>❌ Installation Issues</h3>";
            echo "<p>Some checks failed. Please review the issues above.</p>";
            showManualInstructions();
            echo "</div>";
        }
        echo "</div>";
        break;
}

function deleteDirectory($dir) {
    if (!is_dir($dir)) return;
    
    $files = array_diff(scandir($dir), array('.', '..'));
    foreach ($files as $file) {
        $path = $dir . DIRECTORY_SEPARATOR . $file;
        is_dir($path) ? deleteDirectory($path) : unlink($path);
    }
    rmdir($dir);
}

function showManualInstructions() {
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>🔧 Manual Installation Steps</h3>";
    echo "<p>If automatic installation fails, try these manual steps:</p>";
    echo "<ol>";
    echo "<li><strong>Open Command Prompt as Administrator</strong></li>";
    echo "<li><strong>Navigate to SyncPOS:</strong> <code>cd C:\\xampp\\htdocs\\syncpos</code></li>";
    echo "<li><strong>Install dependencies:</strong> <code>composer install --ignore-platform-reqs</code></li>";
    echo "<li><strong>Generate key:</strong> <code>php artisan key:generate</code></li>";
    echo "<li><strong>Create database:</strong> Open phpMyAdmin and create 'syncpos' database</li>";
    echo "<li><strong>Start installation:</strong> Visit <code>http://localhost/syncpos/public/install</code></li>";
    echo "</ol>";
    echo "</div>";
}

echo "<hr>";
echo "<p style='text-align: center; color: #666;'>";
echo "<strong>SyncPOS Windows Installation</strong><br>";
echo "Optimized for Windows/XAMPP environment";
echo "</p>";
?>
