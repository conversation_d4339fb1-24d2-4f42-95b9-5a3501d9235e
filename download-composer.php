<?php
/**
 * Download and Setup Composer for SyncPOS
 */

echo "<h1>📥 Download Composer for SyncPOS</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .info { color: blue; }
    .warning { color: orange; font-weight: bold; }
    .terminal { background: #000; color: #0f0; padding: 10px; border-radius: 5px; margin: 10px 0; font-family: monospace; }
    .button { background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 5px; }
</style>";

// Check if composer.phar already exists
if (file_exists('composer.phar')) {
    echo "<p class='success'>✅ composer.phar already exists!</p>";
    
    // Test if it works
    exec('php composer.phar --version 2>&1', $output, $return_code);
    if ($return_code === 0) {
        echo "<p class='success'>✅ Composer is working!</p>";
        echo "<div class='terminal'>" . htmlspecialchars(implode("\n", $output)) . "</div>";
        
        echo "<h2>🚀 Install Dependencies</h2>";
        echo "<p>Now let's install the Laravel dependencies:</p>";
        
        if (isset($_GET['install'])) {
            echo "<h3>Installing dependencies...</h3>";
            echo "<div class='terminal'>";
            
            // Run composer install
            $command = 'php composer.phar install --no-dev --optimize-autoloader 2>&1';
            $handle = popen($command, 'r');
            
            if ($handle) {
                while (($line = fgets($handle)) !== false) {
                    echo htmlspecialchars($line) . "<br>";
                    flush();
                }
                pclose($handle);
            }
            
            echo "</div>";
            
            // Check if installation was successful
            if (file_exists('vendor/autoload.php')) {
                echo "<p class='success'>🎉 Dependencies installed successfully!</p>";
                echo "<p><a href='public/install' class='button'>🚀 Start SyncPOS Installation</a></p>";
            } else {
                echo "<p class='error'>❌ Installation may have failed. Please check the output above.</p>";
            }
        } else {
            echo "<p><a href='?install=1' class='button'>📦 Install Dependencies Now</a></p>";
        }
        
    } else {
        echo "<p class='error'>❌ composer.phar exists but is not working properly</p>";
        echo "<div class='terminal'>" . htmlspecialchars(implode("\n", $output)) . "</div>";
    }
} else {
    echo "<p class='info'>Downloading composer.phar...</p>";
    
    // Download composer.phar
    $composerUrl = 'https://getcomposer.org/composer.phar';
    $composerPhar = file_get_contents($composerUrl);
    
    if ($composerPhar !== false) {
        file_put_contents('composer.phar', $composerPhar);
        echo "<p class='success'>✅ composer.phar downloaded successfully!</p>";
        
        // Test the downloaded composer
        exec('php composer.phar --version 2>&1', $output, $return_code);
        if ($return_code === 0) {
            echo "<p class='success'>✅ Composer is working!</p>";
            echo "<div class='terminal'>" . htmlspecialchars(implode("\n", $output)) . "</div>";
            echo "<p><a href='?' class='button'>🔄 Refresh Page</a></p>";
        } else {
            echo "<p class='error'>❌ Downloaded composer is not working</p>";
            echo "<div class='terminal'>" . htmlspecialchars(implode("\n", $output)) . "</div>";
        }
    } else {
        echo "<p class='error'>❌ Failed to download composer.phar</p>";
        echo "<p>Please try manual installation:</p>";
        showManualInstructions();
    }
}

function showManualInstructions() {
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px;'>";
    echo "<h3>🔧 Manual Installation Options</h3>";
    
    echo "<h4>Option 1: Windows Installer (Easiest)</h4>";
    echo "<ol>";
    echo "<li>Download: <a href='https://getcomposer.org/Composer-Setup.exe' target='_blank'>Composer-Setup.exe</a></li>";
    echo "<li>Run the installer</li>";
    echo "<li>Restart your command prompt</li>";
    echo "<li>Run: <code>composer install</code> in your SyncPOS directory</li>";
    echo "</ol>";
    
    echo "<h4>Option 2: Manual Download</h4>";
    echo "<ol>";
    echo "<li>Download: <a href='https://getcomposer.org/composer.phar' target='_blank'>composer.phar</a></li>";
    echo "<li>Save it in your SyncPOS directory (where this file is)</li>";
    echo "<li>Refresh this page</li>";
    echo "</ol>";
    
    echo "<h4>Option 3: Command Line</h4>";
    echo "<p>Open Command Prompt in your SyncPOS directory and run:</p>";
    echo "<div class='terminal'>";
    echo "curl -sS https://getcomposer.org/installer | php<br>";
    echo "php composer.phar install";
    echo "</div>";
    
    echo "</div>";
}

// Show current status
echo "<hr>";
echo "<h2>📊 Current Status</h2>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th style='padding: 8px; background: #f0f0f0;'>Check</th><th style='padding: 8px; background: #f0f0f0;'>Status</th></tr>";

$checks = [
    'composer.phar exists' => file_exists('composer.phar'),
    'vendor/autoload.php exists' => file_exists('vendor/autoload.php'),
    'PHP version >= 8.2' => version_compare(PHP_VERSION, '8.2.0', '>='),
    'Storage writable' => is_writable('storage'),
];

foreach ($checks as $check => $status) {
    $statusText = $status ? "<span class='success'>✅ OK</span>" : "<span class='error'>❌ FAIL</span>";
    echo "<tr><td style='padding: 8px;'>$check</td><td style='padding: 8px;'>$statusText</td></tr>";
}

echo "</table>";

echo "<hr>";
echo "<p style='text-align: center; color: #666;'>";
echo "<strong>SyncPOS Composer Downloader</strong><br>";
echo "Current directory: " . __DIR__;
echo "</p>";
?>
