<?php

namespace Database\Seeders;

use App\Models\Category;
use Illuminate\Database\Seeder;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => 'Electronics',
                'slug' => 'electronics',
                'description' => 'Electronic devices and accessories',
                'sort_order' => 1,
                'is_active' => true,
                'children' => [
                    ['name' => 'Smartphones', 'slug' => 'smartphones', 'description' => 'Mobile phones and accessories'],
                    ['name' => 'Laptops', 'slug' => 'laptops', 'description' => 'Laptop computers and accessories'],
                    ['name' => 'Tablets', 'slug' => 'tablets', 'description' => 'Tablet computers and accessories'],
                    ['name' => 'Audio', 'slug' => 'audio', 'description' => 'Headphones, speakers, and audio equipment'],
                ]
            ],
            [
                'name' => 'Clothing',
                'slug' => 'clothing',
                'description' => 'Apparel and fashion items',
                'sort_order' => 2,
                'is_active' => true,
                'children' => [
                    ['name' => 'Men\'s Clothing', 'slug' => 'mens-clothing', 'description' => 'Clothing for men'],
                    ['name' => 'Women\'s Clothing', 'slug' => 'womens-clothing', 'description' => 'Clothing for women'],
                    ['name' => 'Kids\' Clothing', 'slug' => 'kids-clothing', 'description' => 'Clothing for children'],
                    ['name' => 'Shoes', 'slug' => 'shoes', 'description' => 'Footwear for all ages'],
                ]
            ],
            [
                'name' => 'Food & Beverages',
                'slug' => 'food-beverages',
                'description' => 'Food items and beverages',
                'sort_order' => 3,
                'is_active' => true,
                'children' => [
                    ['name' => 'Snacks', 'slug' => 'snacks', 'description' => 'Chips, crackers, and other snacks'],
                    ['name' => 'Beverages', 'slug' => 'beverages', 'description' => 'Soft drinks, juices, and water'],
                    ['name' => 'Dairy', 'slug' => 'dairy', 'description' => 'Milk, cheese, and dairy products'],
                    ['name' => 'Frozen Foods', 'slug' => 'frozen-foods', 'description' => 'Frozen meals and ingredients'],
                ]
            ],
            [
                'name' => 'Books',
                'slug' => 'books',
                'description' => 'Books and educational materials',
                'sort_order' => 4,
                'is_active' => true,
                'children' => [
                    ['name' => 'Fiction', 'slug' => 'fiction', 'description' => 'Novels and fictional stories'],
                    ['name' => 'Non-Fiction', 'slug' => 'non-fiction', 'description' => 'Educational and informational books'],
                    ['name' => 'Children\'s Books', 'slug' => 'childrens-books', 'description' => 'Books for children'],
                    ['name' => 'Textbooks', 'slug' => 'textbooks', 'description' => 'Educational textbooks'],
                ]
            ],
            [
                'name' => 'Home & Garden',
                'slug' => 'home-garden',
                'description' => 'Home improvement and garden supplies',
                'sort_order' => 5,
                'is_active' => true,
                'children' => [
                    ['name' => 'Furniture', 'slug' => 'furniture', 'description' => 'Home and office furniture'],
                    ['name' => 'Appliances', 'slug' => 'appliances', 'description' => 'Home appliances'],
                    ['name' => 'Garden Tools', 'slug' => 'garden-tools', 'description' => 'Tools for gardening'],
                    ['name' => 'Decor', 'slug' => 'decor', 'description' => 'Home decoration items'],
                ]
            ],
            [
                'name' => 'Sports & Outdoors',
                'slug' => 'sports-outdoors',
                'description' => 'Sports equipment and outdoor gear',
                'sort_order' => 6,
                'is_active' => true,
                'children' => [
                    ['name' => 'Fitness Equipment', 'slug' => 'fitness-equipment', 'description' => 'Exercise and fitness gear'],
                    ['name' => 'Outdoor Gear', 'slug' => 'outdoor-gear', 'description' => 'Camping and hiking equipment'],
                    ['name' => 'Team Sports', 'slug' => 'team-sports', 'description' => 'Equipment for team sports'],
                    ['name' => 'Water Sports', 'slug' => 'water-sports', 'description' => 'Swimming and water sports gear'],
                ]
            ],
        ];

        foreach ($categories as $categoryData) {
            $children = $categoryData['children'] ?? [];
            unset($categoryData['children']);

            $category = Category::create($categoryData);

            foreach ($children as $childData) {
                Category::create(array_merge($childData, [
                    'parent_id' => $category->id,
                    'is_active' => true,
                    'sort_order' => 0,
                ]));
            }
        }
    }
}
