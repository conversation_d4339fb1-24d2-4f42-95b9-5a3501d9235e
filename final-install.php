<?php
/**
 * SyncPOS Final Installation Script
 * This script prepares SyncPOS for a clean installation
 */

echo "<h1>🚀 SyncPOS Final Installation Setup</h1>";
echo "<style>body { font-family: Arial, sans-serif; margin: 20px; } .success { color: green; } .error { color: red; } .info { color: blue; }</style>";

// Step 1: Remove installation flag
$installFile = 'storage/app/installed';
if (file_exists($installFile)) {
    unlink($installFile);
    echo "<p class='success'>✅ Removed installation flag</p>";
}

// Step 2: Clear all caches
$commands = [
    'php artisan cache:clear',
    'php artisan config:clear',
    'php artisan route:clear',
    'php artisan view:clear'
];

echo "<h3>Clearing caches...</h3>";
foreach ($commands as $command) {
    exec($command . ' 2>&1', $output, $return_code);
    if ($return_code === 0) {
        echo "<p class='success'>✅ " . htmlspecialchars($command) . "</p>";
    } else {
        echo "<p class='error'>❌ Failed: " . htmlspecialchars($command) . "</p>";
    }
}

// Step 3: Set permissions
echo "<h3>Setting permissions...</h3>";
if (is_writable('storage')) {
    echo "<p class='success'>✅ Storage directory is writable</p>";
} else {
    echo "<p class='error'>❌ Storage directory is not writable</p>";
}

if (is_writable('bootstrap/cache')) {
    echo "<p class='success'>✅ Bootstrap cache directory is writable</p>";
} else {
    echo "<p class='error'>❌ Bootstrap cache directory is not writable</p>";
}

echo "<hr>";
echo "<h2>🎯 Installation Ready!</h2>";
echo "<p><strong>Your SyncPOS installation is now ready. Choose one of the following options:</strong></p>";

echo "<div style='background: #f0f9ff; border: 1px solid #0ea5e9; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
echo "<h3>🌐 Option 1: Web Installation (Recommended)</h3>";
echo "<ol>";
echo "<li><strong>Via XAMPP:</strong> <a href='http://localhost/syncpos/public/install' target='_blank'>http://localhost/syncpos/public/install</a></li>";
echo "<li><strong>Via Laravel Server:</strong> Run <code>php artisan serve</code> then visit <a href='http://localhost:8000/install' target='_blank'>http://localhost:8000/install</a></li>";
echo "</ol>";
echo "</div>";

echo "<div style='background: #f0fdf4; border: 1px solid #22c55e; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
echo "<h3>⚡ Option 2: Quick Laravel Server</h3>";
echo "<p>Run this command in your terminal:</p>";
echo "<code style='background: #000; color: #0f0; padding: 10px; display: block; margin: 10px 0;'>cd " . __DIR__ . " && php artisan serve</code>";
echo "<p>Then visit: <a href='http://localhost:8000/install' target='_blank'>http://localhost:8000/install</a></p>";
echo "</div>";

echo "<div style='background: #fefce8; border: 1px solid #eab308; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
echo "<h3>🔧 Option 3: Manual Setup (Advanced)</h3>";
echo "<p>If the web installer doesn't work, you can set up manually:</p>";
echo "<ol>";
echo "<li>Create database: <code>CREATE DATABASE syncpos;</code></li>";
echo "<li>Run: <code>php artisan migrate</code></li>";
echo "<li>Run: <code>php artisan db:seed</code></li>";
echo "<li>Create admin: <code>php artisan make:admin</code></li>";
echo "</ol>";
echo "</div>";

echo "<h3>📋 Pre-Installation Checklist</h3>";
echo "<ul>";
echo "<li>✅ PHP 8.2+ installed</li>";
echo "<li>✅ MySQL/MariaDB running</li>";
echo "<li>✅ Composer dependencies installed</li>";
echo "<li>✅ File permissions set</li>";
echo "<li>✅ Installation flag removed</li>";
echo "<li>✅ Caches cleared</li>";
echo "</ul>";

echo "<h3>🆘 Troubleshooting</h3>";
echo "<ul>";
echo "<li><strong>404 Error:</strong> Make sure you're accessing the <code>public</code> directory</li>";
echo "<li><strong>500 Error:</strong> Check <code>storage/logs/laravel.log</code> for details</li>";
echo "<li><strong>Database Error:</strong> Ensure MySQL is running and database exists</li>";
echo "<li><strong>Permission Error:</strong> Run <code>chmod -R 755 storage bootstrap/cache</code></li>";
echo "</ul>";

echo "<hr>";
echo "<p style='text-align: center; color: #666;'>";
echo "<strong>SyncPOS v1.0</strong> - Ready for Installation<br>";
echo "Commercial Point of Sale System";
echo "</p>";
?>
