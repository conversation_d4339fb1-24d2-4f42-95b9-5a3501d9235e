<?php

namespace Database\Seeders;

use App\Models\CustomerGroup;
use Illuminate\Database\Seeder;

class CustomerGroupSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $customerGroups = [
            [
                'name' => 'Regular Customers',
                'description' => 'Standard customers with no special discounts',
                'discount_percentage' => 0.00,
                'is_active' => true,
                'settings' => [
                    'loyalty_points_multiplier' => 1.0,
                    'minimum_order_amount' => 0,
                    'free_shipping_threshold' => null,
                    'special_offers' => false,
                ]
            ],
            [
                'name' => 'VIP Customers',
                'description' => 'Premium customers with 5% discount on all purchases',
                'discount_percentage' => 5.00,
                'is_active' => true,
                'settings' => [
                    'loyalty_points_multiplier' => 1.5,
                    'minimum_order_amount' => 0,
                    'free_shipping_threshold' => 50,
                    'special_offers' => true,
                    'priority_support' => true,
                ]
            ],
            [
                'name' => 'Wholesale Customers',
                'description' => 'Bulk buyers with 10% discount on orders over $500',
                'discount_percentage' => 10.00,
                'is_active' => true,
                'settings' => [
                    'loyalty_points_multiplier' => 0.5,
                    'minimum_order_amount' => 500,
                    'free_shipping_threshold' => 100,
                    'special_offers' => false,
                    'bulk_pricing' => true,
                ]
            ],
            [
                'name' => 'Employee Discount',
                'description' => 'Staff members with 15% employee discount',
                'discount_percentage' => 15.00,
                'is_active' => true,
                'settings' => [
                    'loyalty_points_multiplier' => 1.0,
                    'minimum_order_amount' => 0,
                    'free_shipping_threshold' => 25,
                    'special_offers' => true,
                    'employee_benefits' => true,
                ]
            ],
            [
                'name' => 'Student Discount',
                'description' => 'Students with valid ID get 8% discount',
                'discount_percentage' => 8.00,
                'is_active' => true,
                'settings' => [
                    'loyalty_points_multiplier' => 1.2,
                    'minimum_order_amount' => 0,
                    'free_shipping_threshold' => null,
                    'special_offers' => true,
                    'verification_required' => true,
                ]
            ],
            [
                'name' => 'Senior Citizens',
                'description' => 'Senior citizens (65+) with 6% discount',
                'discount_percentage' => 6.00,
                'is_active' => true,
                'settings' => [
                    'loyalty_points_multiplier' => 1.0,
                    'minimum_order_amount' => 0,
                    'free_shipping_threshold' => null,
                    'special_offers' => true,
                    'age_verification_required' => true,
                ]
            ],
        ];

        foreach ($customerGroups as $group) {
            CustomerGroup::create($group);
        }
    }
}
