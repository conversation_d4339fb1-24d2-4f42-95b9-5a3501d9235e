<?php

namespace App\Models;

use Stancl\Tenancy\Database\Models\Domain as BaseDomain;

class Domain extends BaseDomain
{
    protected $fillable = [
        'domain',
        'tenant_id',
        'is_primary',
        'certificate',
    ];

    protected $casts = [
        'is_primary' => 'boolean',
    ];

    public static function getCustomColumns(): array
    {
        return [
            'id',
            'domain',
            'tenant_id',
            'is_primary',
            'certificate',
        ];
    }
}
