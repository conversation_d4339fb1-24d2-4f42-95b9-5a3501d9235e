#!/bin/bash

# SyncPOS Deployment Script
# This script automates the deployment process for production environments

set -e

echo "🚀 Starting SyncPOS deployment..."

# Configuration
APP_ENV=${APP_ENV:-production}
BACKUP_DIR="/var/backups/syncpos"
DEPLOY_DIR="/var/www/syncpos"
REPO_URL="https://github.com/your-repo/syncpos.git"
BRANCH=${BRANCH:-main}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   log_error "This script should not be run as root for security reasons"
   exit 1
fi

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    command -v docker >/dev/null 2>&1 || { log_error "Docker is required but not installed. Aborting."; exit 1; }
    command -v docker-compose >/dev/null 2>&1 || { log_error "Docker Compose is required but not installed. Aborting."; exit 1; }
    command -v git >/dev/null 2>&1 || { log_error "Git is required but not installed. Aborting."; exit 1; }
    
    log_info "Prerequisites check passed ✓"
}

# Create backup
create_backup() {
    if [ -d "$DEPLOY_DIR" ]; then
        log_info "Creating backup..."
        
        # Create backup directory
        mkdir -p "$BACKUP_DIR"
        
        # Backup database
        docker-compose exec -T db mysqldump -u root -p$DB_PASSWORD --all-databases > "$BACKUP_DIR/database_$(date +%Y%m%d_%H%M%S).sql"
        
        # Backup application files
        tar -czf "$BACKUP_DIR/app_$(date +%Y%m%d_%H%M%S).tar.gz" -C "$DEPLOY_DIR" .
        
        # Keep only last 5 backups
        ls -t "$BACKUP_DIR"/database_*.sql | tail -n +6 | xargs -r rm
        ls -t "$BACKUP_DIR"/app_*.tar.gz | tail -n +6 | xargs -r rm
        
        log_info "Backup created ✓"
    fi
}

# Deploy application
deploy_application() {
    log_info "Deploying application..."
    
    # Clone or update repository
    if [ ! -d "$DEPLOY_DIR" ]; then
        log_info "Cloning repository..."
        git clone "$REPO_URL" "$DEPLOY_DIR"
        cd "$DEPLOY_DIR"
        git checkout "$BRANCH"
    else
        log_info "Updating repository..."
        cd "$DEPLOY_DIR"
        git fetch origin
        git checkout "$BRANCH"
        git pull origin "$BRANCH"
    fi
    
    # Copy environment file if it doesn't exist
    if [ ! -f ".env" ]; then
        log_warn "No .env file found. Copying from .env.example"
        cp .env.example .env
        log_warn "Please configure .env file before continuing"
        exit 1
    fi
    
    log_info "Repository updated ✓"
}

# Build and start containers
build_containers() {
    log_info "Building and starting containers..."
    
    cd "$DEPLOY_DIR"
    
    # Build containers
    docker-compose build --no-cache
    
    # Start containers
    docker-compose up -d
    
    # Wait for database to be ready
    log_info "Waiting for database to be ready..."
    sleep 30
    
    log_info "Containers started ✓"
}

# Run migrations and setup
setup_application() {
    log_info "Setting up application..."
    
    cd "$DEPLOY_DIR"
    
    # Generate application key if not set
    if ! grep -q "APP_KEY=base64:" .env; then
        log_info "Generating application key..."
        docker-compose exec -T app php artisan key:generate
    fi
    
    # Run central migrations
    log_info "Running central migrations..."
    docker-compose exec -T app php artisan migrate --force
    
    # Run tenant migrations
    log_info "Running tenant migrations..."
    docker-compose exec -T app php artisan tenants:migrate --force
    
    # Clear caches
    log_info "Clearing caches..."
    docker-compose exec -T app php artisan config:cache
    docker-compose exec -T app php artisan route:cache
    docker-compose exec -T app php artisan view:cache
    
    # Set permissions
    log_info "Setting permissions..."
    docker-compose exec -T app chown -R www-data:www-data /var/www/storage
    docker-compose exec -T app chown -R www-data:www-data /var/www/bootstrap/cache
    
    log_info "Application setup completed ✓"
}

# Health check
health_check() {
    log_info "Performing health check..."
    
    # Wait for application to be ready
    sleep 10
    
    # Check if application responds
    if curl -f -s http://localhost/health > /dev/null; then
        log_info "Health check passed ✓"
    else
        log_error "Health check failed ✗"
        log_error "Please check the application logs"
        exit 1
    fi
}

# Cleanup old images
cleanup() {
    log_info "Cleaning up old Docker images..."
    docker image prune -f
    log_info "Cleanup completed ✓"
}

# Main deployment process
main() {
    log_info "Starting deployment process..."
    
    check_prerequisites
    create_backup
    deploy_application
    build_containers
    setup_application
    health_check
    cleanup
    
    log_info "🎉 Deployment completed successfully!"
    log_info "Application is available at: http://localhost"
    log_info "Admin panel: http://localhost/admin"
}

# Handle script interruption
trap 'log_error "Deployment interrupted"; exit 1' INT TERM

# Run main function
main "$@"
