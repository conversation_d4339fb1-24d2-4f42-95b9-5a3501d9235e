# SyncPOS XAMPP Installation Guide

This guide will help you install and run SyncPOS on XAMPP for local development.

## 📋 Prerequisites

### Required Software
1. **XAMPP 8.2+** - Download from [apachefriends.org](https://www.apachefriends.org/)
2. **Composer** - Download from [getcomposer.org](https://getcomposer.org/)
3. **Node.js 18+** - Download from [nodejs.org](https://nodejs.org/)
4. **Git** - Download from [git-scm.com](https://git-scm.com/)

### System Requirements
- Windows 10/11
- At least 4GB RAM
- 2GB free disk space

## 🚀 Installation Steps

### Step 1: Install Prerequisites

1. **Install XAMPP**
   - Download and install XAMPP with PHP 8.2+
   - Start Apache and MySQL from XAMPP Control Panel

2. **Install Composer**
   - Download and run the Windows installer
   - Verify installation: `composer --version`

3. **Install Node.js**
   - Download and install the LTS version
   - Verify installation: `node --version` and `npm --version`

4. **Install Git**
   - Download and install Git for Windows
   - Verify installation: `git --version`

### Step 2: Configure XAMPP

1. **Enable PHP Extensions**
   
   Edit `C:\xampp\php\php.ini` and uncomment these lines:
   ```ini
   extension=curl
   extension=fileinfo
   extension=gd
   extension=mbstring
   extension=openssl
   extension=pdo_mysql
   extension=zip
   extension=intl
   extension=redis
   ```

2. **Increase PHP Limits**
   
   Add/modify these settings in `php.ini`:
   ```ini
   memory_limit = 512M
   upload_max_filesize = 100M
   post_max_size = 100M
   max_execution_time = 300
   max_input_vars = 3000
   ```

3. **Restart Apache** from XAMPP Control Panel

### Step 3: Setup Database

1. **Open phpMyAdmin**
   - Go to `http://localhost/phpmyadmin`
   - Login with username: `root`, password: (leave empty)

2. **Create Database**
   ```sql
   CREATE DATABASE syncpos_landlord CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```

### Step 4: Download and Setup SyncPOS

1. **Navigate to XAMPP htdocs**
   ```bash
   cd C:\xampp\htdocs
   ```

2. **Clone or Download SyncPOS**
   ```bash
   # Option 1: Clone from Git (if you have the repository)
   git clone https://github.com/your-repo/syncpos.git
   cd syncpos
   
   # Option 2: Extract downloaded ZIP file to C:\xampp\htdocs\syncpos
   ```

3. **Run Installation Script**
   ```bash
   # Double-click install-xampp.bat or run from command prompt:
   install-xampp.bat
   ```

### Step 5: Configure Environment

1. **Copy XAMPP Environment File**
   ```bash
   copy .env.xampp .env
   ```

2. **Edit .env File**
   
   Open `.env` in a text editor and verify these settings:
   ```env
   APP_URL=http://localhost/syncpos/public
   
   DB_CONNECTION=mysql
   DB_HOST=127.0.0.1
   DB_PORT=3306
   DB_DATABASE=syncpos_landlord
   DB_USERNAME=root
   DB_PASSWORD=
   ```

### Step 6: Run Database Migrations

1. **Open Command Prompt in Project Directory**
   ```bash
   cd C:\xampp\htdocs\syncpos
   ```

2. **Run Central Migrations**
   ```bash
   php artisan migrate
   ```

3. **Seed Roles and Permissions**
   ```bash
   php artisan db:seed --class=RolePermissionSeeder
   ```

### Step 7: Create Your First Tenant

1. **Create a Demo Tenant**
   ```bash
   php artisan tenant:create demo "Demo Company" <EMAIL>
   ```

2. **Run Tenant Migrations**
   ```bash
   php artisan tenants:migrate
   ```

3. **Seed Tenant Data**
   ```bash
   php artisan tenants:seed --class=RolePermissionSeeder
   ```

### Step 8: Access the Application

1. **Main Application**
   - URL: `http://localhost/syncpos/public`
   - This will show the landing page

2. **Demo Tenant**
   - Add this to your `C:\Windows\System32\drivers\etc\hosts` file:
     ```
     127.0.0.1 demo.localhost
     ```
   - Access: `http://demo.localhost/syncpos/public`

3. **Create First User**
   - Register a new account on the demo tenant
   - The first user will automatically become an admin

## 🔧 Development Workflow

### Starting Development
```bash
cd C:\xampp\htdocs\syncpos

# Start XAMPP services (Apache & MySQL)
# Then run the development server
php artisan serve --host=0.0.0.0 --port=8000
```

Access your application at: `http://localhost:8000`

### Building Assets
```bash
# Development build (with hot reload)
npm run dev

# Production build
npm run build

# Watch for changes
npm run watch
```

### Running Queues (Optional)
```bash
# For background jobs (in a separate command prompt)
php artisan queue:work
```

## 🛠 Troubleshooting

### Common Issues

#### 1. Composer Install Fails
```bash
# Clear Composer cache
composer clear-cache

# Install with verbose output
composer install -v
```

#### 2. NPM Install Fails
```bash
# Clear NPM cache
npm cache clean --force

# Delete node_modules and reinstall
rmdir /s node_modules
npm install
```

#### 3. Database Connection Error
- Verify MySQL is running in XAMPP
- Check database name and credentials in `.env`
- Ensure database exists in phpMyAdmin

#### 4. Permission Errors
```bash
# Run as administrator or set folder permissions
icacls "C:\xampp\htdocs\syncpos\storage" /grant Everyone:F /T
icacls "C:\xampp\htdocs\syncpos\bootstrap\cache" /grant Everyone:F /T
```

#### 5. Tenant Not Accessible
- Check hosts file entry
- Verify domain exists in database:
  ```sql
  SELECT * FROM domains WHERE domain = 'demo.localhost';
  ```

#### 6. Assets Not Loading
```bash
# Rebuild assets
npm run build

# Clear Laravel caches
php artisan config:clear
php artisan route:clear
php artisan view:clear
```

### Useful Commands

```bash
# Clear all caches
php artisan optimize:clear

# Create a new tenant
php artisan tenant:create {domain} {company_name} {email}

# List all tenants
php artisan tenants:list

# Run tenant-specific commands
php artisan tenants:run migrate

# Generate dummy data (optional)
php artisan db:seed
php artisan tenants:seed
```

## 📝 Development Notes

### File Structure
```
C:\xampp\htdocs\syncpos\
├── app/                 # Laravel application code
├── database/           # Migrations and seeders
├── public/             # Web accessible files
├── resources/          # Views, CSS, JS
├── routes/             # Route definitions
├── storage/            # Logs, cache, uploads
└── vendor/             # Composer dependencies
```

### Important URLs
- **Main App**: `http://localhost/syncpos/public`
- **phpMyAdmin**: `http://localhost/phpmyadmin`
- **XAMPP Control**: Launch from Start Menu

### Database Structure
- **Central Database**: `syncpos_landlord` (tenant management)
- **Tenant Databases**: `tenant{uuid}` (individual tenant data)

## 🔒 Security Notes for Development

1. **Never use in production** with these settings
2. **Change default passwords** before deploying
3. **Use HTTPS** in production
4. **Configure proper firewall** rules
5. **Regular backups** of database

## 📞 Getting Help

If you encounter issues:

1. Check the Laravel logs: `storage/logs/laravel.log`
2. Check XAMPP error logs
3. Verify all prerequisites are installed
4. Ensure XAMPP services are running
5. Check the main README.md for additional information

## 🎉 Next Steps

Once everything is working:

1. **Explore the POS Interface** - Create products and make test sales
2. **Test Multi-tenancy** - Create additional tenants
3. **Configure Integrations** - Set up Stripe, WooCommerce, or Shopify
4. **Customize Settings** - Modify business settings and preferences
5. **Add Sample Data** - Create products, customers, and test transactions

Happy coding! 🚀
