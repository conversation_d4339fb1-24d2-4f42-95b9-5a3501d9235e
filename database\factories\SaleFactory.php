<?php

namespace Database\Factories;

use App\Models\Sale;
use App\Models\Store;
use App\Models\Customer;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Sale>
 */
class SaleFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Sale::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $subtotal = $this->faker->randomFloat(2, 10, 500);
        $taxAmount = $subtotal * 0.1; // 10% tax
        $discountAmount = $this->faker->optional(0.3)->randomFloat(2, 0, $subtotal * 0.2);
        $totalAmount = $subtotal + $taxAmount - ($discountAmount ?? 0);

        return [
            'sale_number' => 'SALE-' . $this->faker->unique()->numerify('######'),
            'store_id' => Store::factory(),
            'customer_id' => $this->faker->optional(0.7)->randomElement([Customer::factory(), null]),
            'cashier_id' => User::factory(),
            'subtotal' => $subtotal,
            'tax_amount' => $taxAmount,
            'discount_amount' => $discountAmount ?? 0,
            'total_amount' => $totalAmount,
            'payment_method' => $this->faker->randomElement(['cash', 'card', 'mobile', 'bank_transfer']),
            'payment_status' => $this->faker->randomElement(['paid', 'pending', 'failed']),
            'status' => $this->faker->randomElement(['completed', 'pending', 'cancelled', 'refunded']),
            'notes' => $this->faker->optional(0.3)->sentence(),
            'receipt_data' => [
                'payments' => [
                    [
                        'method' => 'cash',
                        'amount' => $totalAmount,
                    ]
                ],
                'change' => 0,
            ],
        ];
    }

    /**
     * Indicate that the sale is completed.
     */
    public function completed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'completed',
            'payment_status' => 'paid',
        ]);
    }

    /**
     * Indicate that the sale is pending.
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'pending',
            'payment_status' => 'pending',
        ]);
    }

    /**
     * Indicate that the sale is cancelled.
     */
    public function cancelled(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'cancelled',
            'payment_status' => 'failed',
        ]);
    }

    /**
     * Indicate that the sale is refunded.
     */
    public function refunded(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'refunded',
            'payment_status' => 'paid',
        ]);
    }

    /**
     * Create a cash sale.
     */
    public function cash(): static
    {
        return $this->state(fn (array $attributes) => [
            'payment_method' => 'cash',
            'payment_status' => 'paid',
            'status' => 'completed',
        ]);
    }

    /**
     * Create a card sale.
     */
    public function card(): static
    {
        return $this->state(fn (array $attributes) => [
            'payment_method' => 'card',
            'payment_status' => 'paid',
            'status' => 'completed',
        ]);
    }

    /**
     * Create a sale with discount.
     */
    public function withDiscount(): static
    {
        return $this->state(function (array $attributes) {
            $subtotal = $attributes['subtotal'];
            $discountAmount = $this->faker->randomFloat(2, 5, $subtotal * 0.3);
            $taxAmount = $attributes['tax_amount'];
            $totalAmount = $subtotal + $taxAmount - $discountAmount;

            return [
                'discount_amount' => $discountAmount,
                'total_amount' => $totalAmount,
            ];
        });
    }

    /**
     * Create a sale for a specific customer.
     */
    public function forCustomer(Customer $customer): static
    {
        return $this->state(fn (array $attributes) => [
            'customer_id' => $customer->id,
        ]);
    }

    /**
     * Create a sale for a specific store.
     */
    public function forStore(Store $store): static
    {
        return $this->state(fn (array $attributes) => [
            'store_id' => $store->id,
        ]);
    }
}
