<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\PosController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\InventoryController;
use App\Http\Controllers\SaleController;
use App\Http\Controllers\CustomerController;
use App\Http\Controllers\SupplierController;
use App\Http\Controllers\PurchaseOrderController;
use App\Http\Controllers\ReportController;
use App\Http\Controllers\SettingsController;

Route::get('/', function () {
    return view('welcome');
});

// Test route to verify <PERSON><PERSON> is working
Route::get('/test', function () {
    return '<PERSON><PERSON> is working! SyncPOS is ready for installation.';
});

// Installation routes (accessible without middleware for now)
Route::get('/install', [App\Http\Controllers\InstallController::class, 'index'])->name('install.index');
Route::post('/install', [App\Http\Controllers\InstallController::class, 'process'])->name('install.process');
Route::get('/install/complete', [App\Http\Controllers\InstallController::class, 'complete'])->name('install.complete');

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
    
    // POS Routes
    Route::prefix('pos')->name('pos.')->group(function () {
        Route::get('/', [PosController::class, 'index'])->name('index');
        Route::post('/add-to-cart', [PosController::class, 'addToCart'])->name('add-to-cart');
        Route::post('/remove-from-cart', [PosController::class, 'removeFromCart'])->name('remove-from-cart');
        Route::post('/checkout', [PosController::class, 'checkout'])->name('checkout');
        Route::get('/receipt/{sale}', [PosController::class, 'receipt'])->name('receipt');
    });
    
    // Product Management
    Route::resource('products', ProductController::class);
    Route::post('products/import', [ProductController::class, 'import'])->name('products.import');
    Route::get('products/export', [ProductController::class, 'export'])->name('products.export');
    
    // Inventory Management
    Route::prefix('inventory')->name('inventory.')->group(function () {
        Route::get('/', [InventoryController::class, 'index'])->name('index');
        Route::post('/adjust', [InventoryController::class, 'adjust'])->name('adjust');
        Route::post('/transfer', [InventoryController::class, 'transfer'])->name('transfer');
        Route::get('/low-stock', [InventoryController::class, 'lowStock'])->name('low-stock');
    });
    
    // Sales Management
    Route::resource('sales', SaleController::class)->only(['index', 'show']);
    Route::post('sales/{sale}/refund', [SaleController::class, 'refund'])->name('sales.refund');
    
    // Customer Management
    Route::resource('customers', CustomerController::class);
    Route::post('customers/import', [CustomerController::class, 'import'])->name('customers.import');
    
    // Supplier Management
    Route::resource('suppliers', SupplierController::class);
    
    // Purchase Orders
    Route::resource('purchase-orders', PurchaseOrderController::class);
    Route::post('purchase-orders/{purchaseOrder}/receive', [PurchaseOrderController::class, 'receive'])->name('purchase-orders.receive');
    
    // Reports
    Route::prefix('reports')->name('reports.')->group(function () {
        Route::get('/sales', [ReportController::class, 'sales'])->name('sales');
        Route::get('/inventory', [ReportController::class, 'inventory'])->name('inventory');
        Route::get('/products', [ReportController::class, 'products'])->name('products');
        Route::get('/customers', [ReportController::class, 'customers'])->name('customers');
        Route::get('/staff', [ReportController::class, 'staff'])->name('staff');
    });
    
    // Settings
    Route::prefix('settings')->name('settings.')->group(function () {
        Route::get('/', [SettingsController::class, 'index'])->name('index');
        Route::post('/business', [SettingsController::class, 'updateBusiness'])->name('business');
        Route::post('/tax', [SettingsController::class, 'updateTax'])->name('tax');
        Route::post('/receipt', [SettingsController::class, 'updateReceipt'])->name('receipt');
        Route::post('/integrations', [SettingsController::class, 'updateIntegrations'])->name('integrations');
    });
});

require __DIR__.'/auth.php';
