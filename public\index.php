<?php

use Illuminate\Http\Request;

define('LARAVEL_START', microtime(true));

// Check if installation is required
if (!file_exists(__DIR__.'/../.env') && !file_exists(__DIR__.'/../storage/installed')) {
    // Redirect to installer
    $installerPath = '/install/index.php';
    $currentPath = $_SERVER['REQUEST_URI'];

    // Don't redirect if already in installer
    if (strpos($currentPath, '/install/') === false) {
        header('Location: ' . $installerPath);
        exit;
    }
}

// Determine if the application is in maintenance mode...
if (file_exists($maintenance = __DIR__.'/../storage/framework/maintenance.php')) {
    require $maintenance;
}

// Register the Composer autoloader...
require __DIR__.'/../vendor/autoload.php';

// Bootstrap Laravel and handle the request...
(require_once __DIR__.'/../bootstrap/app.php')
    ->handleRequest(Request::capture());
