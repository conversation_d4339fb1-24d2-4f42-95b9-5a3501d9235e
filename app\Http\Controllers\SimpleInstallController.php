<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Store;
use App\Models\Category;
use App\Models\CustomerGroup;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Storage;

class SimpleInstallController extends Controller
{
    public function index()
    {
        return view('install.simple');
    }

    public function process(Request $request)
    {
        try {
            $validated = $request->validate([
                'app_name' => 'required|string|max:255',
                'app_url' => 'required|url',
                'db_host' => 'required|string',
                'db_port' => 'required|integer',
                'db_name' => 'required|string',
                'db_user' => 'required|string',
                'db_pass' => 'nullable|string',
                'admin_name' => 'required|string|max:255',
                'admin_email' => 'required|email',
                'admin_password' => 'required|min:6',
                'store_name' => 'required|string|max:255',
            ]);

            // Create .env file
            $this->createEnvFile($validated);

            // Test database connection
            $this->testDatabase($validated);

            // Run setup
            $this->runSetup();

            // Create admin and store
            $this->createAdminAndStore($validated);

            // Mark as installed
            Storage::put('installed', now()->toDateTimeString());

            return redirect()->route('install.complete');

        } catch (\Exception $e) {
            return back()->withErrors(['error' => $e->getMessage()])->withInput();
        }
    }

    public function complete()
    {
        return view('install.complete');
    }

    private function createEnvFile($data)
    {
        $envContent = 'APP_NAME="' . $data['app_name'] . '"
APP_ENV=production
APP_KEY=base64:' . base64_encode(random_bytes(32)) . '
APP_DEBUG=false
APP_TIMEZONE=UTC
APP_URL=' . $data['app_url'] . '

DB_CONNECTION=mysql
DB_HOST=' . $data['db_host'] . '
DB_PORT=' . $data['db_port'] . '
DB_DATABASE=' . $data['db_name'] . '
DB_USERNAME=' . $data['db_user'] . '
DB_PASSWORD=' . $data['db_pass'] . '

SESSION_DRIVER=file
CACHE_STORE=file
QUEUE_CONNECTION=sync
MAIL_MAILER=log
';

        file_put_contents(base_path('.env'), $envContent);
    }

    private function testDatabase($data)
    {
        $pdo = new \PDO(
            "mysql:host={$data['db_host']};port={$data['db_port']};charset=utf8mb4",
            $data['db_user'],
            $data['db_pass'],
            [\PDO::ATTR_ERRMODE => \PDO::ERRMODE_EXCEPTION]
        );

        // Create database if it doesn't exist
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$data['db_name']}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    }

    private function runSetup()
    {
        // Clear config cache
        if (file_exists(base_path('bootstrap/cache/config.php'))) {
            unlink(base_path('bootstrap/cache/config.php'));
        }

        // Run migrations
        Artisan::call('migrate', ['--force' => true]);

        // Create basic data
        $this->createBasicData();
    }

    private function createBasicData()
    {
        // Create categories
        $categories = [
            ['name' => 'Electronics', 'slug' => 'electronics', 'is_active' => true],
            ['name' => 'Clothing', 'slug' => 'clothing', 'is_active' => true],
            ['name' => 'Food & Beverages', 'slug' => 'food-beverages', 'is_active' => true],
        ];

        foreach ($categories as $cat) {
            Category::firstOrCreate(['slug' => $cat['slug']], $cat);
        }

        // Create customer group
        CustomerGroup::firstOrCreate(['name' => 'Regular Customers'], [
            'name' => 'Regular Customers',
            'discount_percentage' => 0,
            'is_active' => true,
        ]);
    }

    private function createAdminAndStore($data)
    {
        // Create admin user
        $user = User::create([
            'name' => $data['admin_name'],
            'email' => $data['admin_email'],
            'password' => Hash::make($data['admin_password']),
            'email_verified_at' => now(),
            'is_active' => true,
        ]);

        // Create store
        $store = Store::create([
            'name' => $data['store_name'],
            'code' => 'MAIN',
            'email' => $data['admin_email'],
            'phone' => '******-0123',
            'address' => '123 Main Street',
            'city' => 'Your City',
            'state' => 'Your State',
            'country' => 'Your Country',
            'postal_code' => '12345',
            'is_active' => true,
            'settings' => ['currency' => 'USD', 'timezone' => 'UTC'],
        ]);

        // Assign user to store
        $store->users()->attach($user->id, ['is_primary' => true]);
    }
}
