<?php
/**
 * Quick Status Check for SyncPOS
 */

echo "<h1>🔍 SyncPOS Status Check</h1>";
echo "<style>body { font-family: Arial, sans-serif; margin: 20px; } .success { color: green; } .error { color: red; }</style>";

$checks = [
    'Composer Dependencies' => file_exists('vendor/autoload.php'),
    'Environment File' => file_exists('.env'),
    'Storage Writable' => is_writable('storage'),
    'Bootstrap Cache Writable' => is_writable('bootstrap/cache'),
];

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th style='padding: 10px; background: #f0f0f0;'>Check</th><th style='padding: 10px; background: #f0f0f0;'>Status</th></tr>";

$allGood = true;
foreach ($checks as $check => $status) {
    $statusText = $status ? "<span class='success'>✅ OK</span>" : "<span class='error'>❌ FAIL</span>";
    echo "<tr><td style='padding: 10px;'>$check</td><td style='padding: 10px;'>$statusText</td></tr>";
    if (!$status) $allGood = false;
}

echo "</table>";

if ($allGood) {
    echo "<h2 class='success'>🎉 All checks passed!</h2>";
    echo "<p><a href='public/install' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🚀 Start Installation</a></p>";
} else {
    echo "<h2 class='error'>❌ Some checks failed</h2>";
    echo "<p>Please run: <a href='install-dependencies.php'>install-dependencies.php</a> to fix issues.</p>";
}
?>
