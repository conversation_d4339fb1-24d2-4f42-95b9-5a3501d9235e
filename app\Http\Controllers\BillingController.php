<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Laravel\Cashier\Exceptions\IncompletePayment;

class BillingController extends Controller
{
    public function subscription()
    {
        $tenant = tenant();
        $subscription = $tenant->subscription('default');
        
        return view('billing.subscription', compact('tenant', 'subscription'));
    }

    public function plans()
    {
        $plans = [
            'basic' => [
                'name' => 'Basic Plan',
                'price' => '$29',
                'interval' => 'month',
                'stripe_price_id' => env('STRIPE_BASIC_PRICE_ID'),
                'features' => [
                    '1 Store Location',
                    '3 Users',
                    'Basic POS',
                    'Inventory Management',
                    'Basic Reports',
                    'Email Support',
                ],
                'limits' => [
                    'stores' => 1,
                    'users' => 3,
                    'products' => 1000,
                ],
            ],
            'professional' => [
                'name' => 'Professional Plan',
                'price' => '$79',
                'interval' => 'month',
                'stripe_price_id' => env('STRIPE_PROFESSIONAL_PRICE_ID'),
                'features' => [
                    '5 Store Locations',
                    '15 Users',
                    'Advanced POS',
                    'Inventory Management',
                    'Advanced Reports',
                    'WooCommerce Integration',
                    'Shopify Integration',
                    'Priority Support',
                ],
                'limits' => [
                    'stores' => 5,
                    'users' => 15,
                    'products' => 10000,
                ],
            ],
            'enterprise' => [
                'name' => 'Enterprise Plan',
                'price' => '$199',
                'interval' => 'month',
                'stripe_price_id' => env('STRIPE_ENTERPRISE_PRICE_ID'),
                'features' => [
                    'Unlimited Store Locations',
                    'Unlimited Users',
                    'Advanced POS',
                    'Inventory Management',
                    'Advanced Reports & Analytics',
                    'All Integrations',
                    'API Access',
                    'Custom Features',
                    '24/7 Phone Support',
                ],
                'limits' => [
                    'stores' => 999,
                    'users' => 999,
                    'products' => 999999,
                ],
            ],
        ];

        return view('billing.plans', compact('plans'));
    }

    public function subscribe(Request $request)
    {
        $request->validate([
            'plan' => 'required|in:basic,professional,enterprise',
            'payment_method' => 'required|string',
        ]);

        $tenant = tenant();
        $plan = $request->plan;
        
        $priceIds = [
            'basic' => env('STRIPE_BASIC_PRICE_ID'),
            'professional' => env('STRIPE_PROFESSIONAL_PRICE_ID'),
            'enterprise' => env('STRIPE_ENTERPRISE_PRICE_ID'),
        ];

        try {
            $subscription = $tenant->newSubscription('default', $priceIds[$plan])
                ->create($request->payment_method);

            $tenant->update(['subscription_plan' => $plan]);

            return redirect()->route('billing.subscription')
                ->with('success', 'Successfully subscribed to ' . ucfirst($plan) . ' plan!');

        } catch (IncompletePayment $exception) {
            return redirect()->route('cashier.payment', [$exception->payment->id, 'redirect' => route('billing.subscription')]);
        }
    }

    public function changePlan(Request $request)
    {
        $request->validate([
            'plan' => 'required|in:basic,professional,enterprise',
        ]);

        $tenant = tenant();
        $subscription = $tenant->subscription('default');
        
        if (!$subscription) {
            return redirect()->route('billing.plans')
                ->with('error', 'No active subscription found.');
        }

        $plan = $request->plan;
        $priceIds = [
            'basic' => env('STRIPE_BASIC_PRICE_ID'),
            'professional' => env('STRIPE_PROFESSIONAL_PRICE_ID'),
            'enterprise' => env('STRIPE_ENTERPRISE_PRICE_ID'),
        ];

        $subscription->swap($priceIds[$plan]);
        $tenant->update(['subscription_plan' => $plan]);

        return redirect()->route('billing.subscription')
            ->with('success', 'Successfully changed to ' . ucfirst($plan) . ' plan!');
    }

    public function cancel()
    {
        $tenant = tenant();
        $subscription = $tenant->subscription('default');

        if ($subscription) {
            $subscription->cancel();
        }

        return redirect()->route('billing.subscription')
            ->with('success', 'Subscription cancelled. You can continue using the service until the end of your billing period.');
    }

    public function resume()
    {
        $tenant = tenant();
        $subscription = $tenant->subscription('default');

        if ($subscription && $subscription->cancelled()) {
            $subscription->resume();
        }

        return redirect()->route('billing.subscription')
            ->with('success', 'Subscription resumed successfully!');
    }

    public function invoices()
    {
        $tenant = tenant();
        $invoices = $tenant->invoices();

        return view('billing.invoices', compact('invoices'));
    }

    public function downloadInvoice(Request $request, string $invoiceId)
    {
        $tenant = tenant();
        
        return $tenant->downloadInvoice($invoiceId, [
            'vendor' => 'SyncPOS',
            'product' => 'POS & Inventory Management',
        ]);
    }

    public function paymentMethods()
    {
        $tenant = tenant();
        $paymentMethods = $tenant->paymentMethods();
        $defaultPaymentMethod = $tenant->defaultPaymentMethod();

        return view('billing.payment-methods', compact('paymentMethods', 'defaultPaymentMethod'));
    }

    public function addPaymentMethod(Request $request)
    {
        $request->validate([
            'payment_method' => 'required|string',
        ]);

        $tenant = tenant();
        $tenant->addPaymentMethod($request->payment_method);

        return redirect()->route('billing.payment-methods')
            ->with('success', 'Payment method added successfully!');
    }

    public function deletePaymentMethod(Request $request, string $paymentMethodId)
    {
        $tenant = tenant();
        $paymentMethod = $tenant->findPaymentMethod($paymentMethodId);

        if ($paymentMethod) {
            $paymentMethod->delete();
        }

        return redirect()->route('billing.payment-methods')
            ->with('success', 'Payment method deleted successfully!');
    }

    public function setDefaultPaymentMethod(Request $request, string $paymentMethodId)
    {
        $tenant = tenant();
        $tenant->updateDefaultPaymentMethod($paymentMethodId);

        return redirect()->route('billing.payment-methods')
            ->with('success', 'Default payment method updated successfully!');
    }
}
