<?php

namespace App\Http\Controllers;

use App\Models\Store;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;

class SettingsController extends Controller
{
    public function index()
    {
        $user = auth()->user();
        $store = $user->primaryStore() ?? Store::active()->first();
        
        if (!$store) {
            return redirect()->route('dashboard')
                ->with('error', 'No store assigned. Please contact your administrator.');
        }

        $settings = $store->settings ?? [];
        
        return view('settings.index', compact('store', 'settings'));
    }

    public function updateBusiness(Request $request)
    {
        $user = auth()->user();
        $store = $user->primaryStore() ?? Store::active()->first();
        
        if (!$store) {
            return redirect()->back()->with('error', 'No store found.');
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:10|unique:stores,code,' . $store->id,
            'address' => 'required|string',
            'city' => 'required|string|max:100',
            'state' => 'required|string|max:100',
            'country' => 'required|string|max:100',
            'postal_code' => 'required|string|max:20',
            'phone' => 'required|string|max:20',
            'email' => 'required|email|max:255',
            'logo' => 'nullable|image|max:2048',
            'currency' => 'required|string|max:3',
            'timezone' => 'required|string',
        ]);

        // Handle logo upload
        if ($request->hasFile('logo')) {
            // Delete old logo
            $settings = $store->settings ?? [];
            if (isset($settings['logo']) && Storage::disk('public')->exists($settings['logo'])) {
                Storage::disk('public')->delete($settings['logo']);
            }
            
            $validated['logo'] = $request->file('logo')->store('logos', 'public');
        }

        // Update store basic info
        $store->update([
            'name' => $validated['name'],
            'code' => $validated['code'],
            'address' => $validated['address'],
            'city' => $validated['city'],
            'state' => $validated['state'],
            'country' => $validated['country'],
            'postal_code' => $validated['postal_code'],
            'phone' => $validated['phone'],
            'email' => $validated['email'],
        ]);

        // Update settings
        $settings = $store->settings ?? [];
        $settings['logo'] = $validated['logo'] ?? ($settings['logo'] ?? null);
        $settings['currency'] = $validated['currency'];
        $settings['timezone'] = $validated['timezone'];
        
        $store->update(['settings' => $settings]);

        return redirect()->back()->with('success', 'Business settings updated successfully.');
    }

    public function updateTax(Request $request)
    {
        $user = auth()->user();
        $store = $user->primaryStore() ?? Store::active()->first();
        
        if (!$store) {
            return redirect()->back()->with('error', 'No store found.');
        }

        $validated = $request->validate([
            'tax_enabled' => 'boolean',
            'tax_name' => 'required_if:tax_enabled,true|string|max:100',
            'tax_rate' => 'required_if:tax_enabled,true|numeric|min:0|max:100',
            'tax_number' => 'nullable|string|max:50',
            'tax_inclusive' => 'boolean',
        ]);

        $settings = $store->settings ?? [];
        $settings['tax'] = [
            'enabled' => $validated['tax_enabled'] ?? false,
            'name' => $validated['tax_name'] ?? 'Tax',
            'rate' => $validated['tax_rate'] ?? 0,
            'number' => $validated['tax_number'] ?? '',
            'inclusive' => $validated['tax_inclusive'] ?? false,
        ];
        
        $store->update(['settings' => $settings]);

        return redirect()->back()->with('success', 'Tax settings updated successfully.');
    }

    public function updateReceipt(Request $request)
    {
        $user = auth()->user();
        $store = $user->primaryStore() ?? Store::active()->first();
        
        if (!$store) {
            return redirect()->back()->with('error', 'No store found.');
        }

        $validated = $request->validate([
            'receipt_header' => 'nullable|string|max:500',
            'receipt_footer' => 'nullable|string|max:500',
            'show_logo' => 'boolean',
            'show_barcode' => 'boolean',
            'show_qr_code' => 'boolean',
            'paper_size' => 'required|in:80mm,58mm,A4',
            'auto_print' => 'boolean',
        ]);

        $settings = $store->settings ?? [];
        $settings['receipt'] = [
            'header' => $validated['receipt_header'] ?? '',
            'footer' => $validated['receipt_footer'] ?? '',
            'show_logo' => $validated['show_logo'] ?? false,
            'show_barcode' => $validated['show_barcode'] ?? false,
            'show_qr_code' => $validated['show_qr_code'] ?? false,
            'paper_size' => $validated['paper_size'],
            'auto_print' => $validated['auto_print'] ?? false,
        ];
        
        $store->update(['settings' => $settings]);

        return redirect()->back()->with('success', 'Receipt settings updated successfully.');
    }

    public function updateIntegrations(Request $request)
    {
        $user = auth()->user();
        $store = $user->primaryStore() ?? Store::active()->first();
        
        if (!$store) {
            return redirect()->back()->with('error', 'No store found.');
        }

        $validated = $request->validate([
            // WooCommerce
            'woocommerce_enabled' => 'boolean',
            'woocommerce_url' => 'nullable|url',
            'woocommerce_consumer_key' => 'nullable|string',
            'woocommerce_consumer_secret' => 'nullable|string',
            'woocommerce_sync_interval' => 'nullable|integer|min:5|max:1440',
            
            // Shopify
            'shopify_enabled' => 'boolean',
            'shopify_store_url' => 'nullable|url',
            'shopify_access_token' => 'nullable|string',
            'shopify_sync_interval' => 'nullable|integer|min:5|max:1440',
            
            // Email
            'email_notifications' => 'boolean',
            'low_stock_alerts' => 'boolean',
            'daily_reports' => 'boolean',
        ]);

        $settings = $store->settings ?? [];
        
        $settings['integrations'] = [
            'woocommerce' => [
                'enabled' => $validated['woocommerce_enabled'] ?? false,
                'url' => $validated['woocommerce_url'] ?? '',
                'consumer_key' => $validated['woocommerce_consumer_key'] ?? '',
                'consumer_secret' => $validated['woocommerce_consumer_secret'] ?? '',
                'sync_interval' => $validated['woocommerce_sync_interval'] ?? 30,
            ],
            'shopify' => [
                'enabled' => $validated['shopify_enabled'] ?? false,
                'store_url' => $validated['shopify_store_url'] ?? '',
                'access_token' => $validated['shopify_access_token'] ?? '',
                'sync_interval' => $validated['shopify_sync_interval'] ?? 30,
            ],
            'notifications' => [
                'email_notifications' => $validated['email_notifications'] ?? false,
                'low_stock_alerts' => $validated['low_stock_alerts'] ?? false,
                'daily_reports' => $validated['daily_reports'] ?? false,
            ],
        ];
        
        $store->update(['settings' => $settings]);

        return redirect()->back()->with('success', 'Integration settings updated successfully.');
    }

    public function testConnection(Request $request)
    {
        $type = $request->input('type');
        
        try {
            switch ($type) {
                case 'woocommerce':
                    return $this->testWooCommerceConnection($request);
                case 'shopify':
                    return $this->testShopifyConnection($request);
                default:
                    return response()->json(['success' => false, 'message' => 'Invalid connection type']);
            }
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    private function testWooCommerceConnection(Request $request)
    {
        $url = $request->input('url');
        $consumerKey = $request->input('consumer_key');
        $consumerSecret = $request->input('consumer_secret');
        
        // Test WooCommerce API connection
        $client = new \GuzzleHttp\Client();
        $response = $client->get($url . '/wp-json/wc/v3/system_status', [
            'auth' => [$consumerKey, $consumerSecret],
            'timeout' => 10,
        ]);
        
        if ($response->getStatusCode() === 200) {
            return response()->json(['success' => true, 'message' => 'WooCommerce connection successful']);
        }
        
        return response()->json(['success' => false, 'message' => 'WooCommerce connection failed']);
    }

    private function testShopifyConnection(Request $request)
    {
        $storeUrl = $request->input('store_url');
        $accessToken = $request->input('access_token');
        
        // Test Shopify API connection
        $client = new \GuzzleHttp\Client();
        $response = $client->get($storeUrl . '/admin/api/2023-10/shop.json', [
            'headers' => [
                'X-Shopify-Access-Token' => $accessToken,
            ],
            'timeout' => 10,
        ]);
        
        if ($response->getStatusCode() === 200) {
            return response()->json(['success' => true, 'message' => 'Shopify connection successful']);
        }
        
        return response()->json(['success' => false, 'message' => 'Shopify connection failed']);
    }
}
