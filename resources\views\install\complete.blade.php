<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Installation Complete - SyncPOS</title>
    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>
<body class="bg-gray-100">
    <div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
            <div class="text-center">
                <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100">
                    <svg class="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                </div>
                <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                    🎉 Installation Complete!
                </h2>
                <p class="mt-2 text-center text-sm text-gray-600">
                    SyncPOS has been successfully installed and configured.
                </p>
            </div>

            <div class="bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <div class="space-y-6">
                        <!-- Success Message -->
                        <div class="bg-green-50 border border-green-200 rounded-md p-4">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-sm font-medium text-green-800">Installation Successful</h3>
                                    <div class="mt-2 text-sm text-green-700">
                                        <ul class="list-disc list-inside space-y-1">
                                            <li>Database tables created successfully</li>
                                            <li>Administrator account configured</li>
                                            <li>Default store and sample data added</li>
                                            <li>Application settings configured</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Next Steps -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">What's Next?</h3>
                            <div class="space-y-3">
                                <div class="flex items-start">
                                    <div class="flex-shrink-0">
                                        <span class="flex items-center justify-center h-6 w-6 rounded-full bg-indigo-100 text-indigo-600 text-xs font-medium">1</span>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm text-gray-700">
                                            <strong>Login to your dashboard</strong> using the administrator credentials you created.
                                        </p>
                                    </div>
                                </div>

                                <div class="flex items-start">
                                    <div class="flex-shrink-0">
                                        <span class="flex items-center justify-center h-6 w-6 rounded-full bg-indigo-100 text-indigo-600 text-xs font-medium">2</span>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm text-gray-700">
                                            <strong>Configure your store settings</strong> including business information, tax settings, and receipt preferences.
                                        </p>
                                    </div>
                                </div>

                                <div class="flex items-start">
                                    <div class="flex-shrink-0">
                                        <span class="flex items-center justify-center h-6 w-6 rounded-full bg-indigo-100 text-indigo-600 text-xs font-medium">3</span>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm text-gray-700">
                                            <strong>Add your products</strong> and organize them into categories for easy management.
                                        </p>
                                    </div>
                                </div>

                                <div class="flex items-start">
                                    <div class="flex-shrink-0">
                                        <span class="flex items-center justify-center h-6 w-6 rounded-full bg-indigo-100 text-indigo-600 text-xs font-medium">4</span>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm text-gray-700">
                                            <strong>Start selling!</strong> Use the POS interface to process your first sale.
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Security Notice -->
                        <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-sm font-medium text-yellow-800">Security Recommendation</h3>
                                    <div class="mt-2 text-sm text-yellow-700">
                                        <p>For security reasons, consider removing or restricting access to the <code>/install</code> directory on your production server.</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="space-y-3">
                            <a href="{{ route('login') }}" 
                                class="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                Login to Dashboard
                            </a>
                            
                            <a href="{{ route('pos.index') }}" 
                                class="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                Go to POS
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Support Information -->
            <div class="text-center">
                <p class="text-xs text-gray-500 mb-2">
                    Need help getting started?
                </p>
                <div class="space-x-4 text-xs">
                    <a href="#" class="text-indigo-600 hover:text-indigo-500">Documentation</a>
                    <a href="#" class="text-indigo-600 hover:text-indigo-500">Support</a>
                    <a href="#" class="text-indigo-600 hover:text-indigo-500">Community</a>
                </div>
                <p class="text-xs text-gray-400 mt-4">
                    SyncPOS v1.0 - Thank you for choosing our POS solution!
                </p>
            </div>
        </div>
    </div>
</body>
</html>
