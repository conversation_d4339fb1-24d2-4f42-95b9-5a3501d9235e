# 🔧 SyncPOS Troubleshooting Guide

If you're getting an **Internal Server Error**, follow these steps to diagnose and fix the issue.

## 🚨 Quick Fix Steps

### Step 1: Run Diagnostic Check
Visit: `http://localhost/syncpos/install/check.php`

This will show you exactly what's causing the error.

### Step 2: Try Simple Installer
If the main installer fails, try: `http://localhost/syncpos/install/simple-install.php`

### Step 3: Try Minimal Installer
For basic setup only: `http://localhost/syncpos/install/minimal.php`

---

## 🔍 Common Issues & Solutions

### 1. "Internal Server Error" - Missing Dependencies

**Problem**: Composer dependencies not installed
**Solution**:
```bash
cd C:\xampp\htdocs\syncpos
composer install --no-dev
```

If Composer isn't installed:
1. Download from [getcomposer.org](https://getcomposer.org/)
2. Install and restart command prompt
3. Run the command above

### 2. "Internal Server Error" - PHP Extensions Missing

**Problem**: Required PHP extensions not enabled
**Solution**:
1. Edit `C:\xampp\php\php.ini`
2. Uncomment these lines (remove the `;`):
   ```ini
   extension=pdo_mysql
   extension=openssl
   extension=mbstring
   extension=fileinfo
   extension=tokenizer
   ```
3. Restart Apache in XAMPP

### 3. "Internal Server Error" - File Permissions

**Problem**: Web server can't write to directories
**Solution**:
```bash
# For Windows (run as Administrator)
icacls "C:\xampp\htdocs\syncpos\storage" /grant Everyone:F /T
icacls "C:\xampp\htdocs\syncpos\bootstrap\cache" /grant Everyone:F /T

# For Linux/Mac
chmod -R 777 storage
chmod -R 777 bootstrap/cache
```

### 4. "Internal Server Error" - .htaccess Issues

**Problem**: mod_rewrite not enabled or .htaccess conflicts
**Solution**:
1. **Enable mod_rewrite** in XAMPP:
   - Edit `C:\xampp\apache\conf\httpd.conf`
   - Uncomment: `LoadModule rewrite_module modules/mod_rewrite.so`
   - Restart Apache

2. **Check .htaccess files exist**:
   - Root: `syncpos/.htaccess`
   - Public: `syncpos/public/.htaccess`

### 5. "Page Not Found" After Installation

**Problem**: URL rewriting not working
**Solution**:
- Access via: `http://localhost/syncpos/public/index.php`
- Or fix mod_rewrite (see above)

---

## 🛠️ Diagnostic Tools

### Tool 1: Check Requirements
```
http://localhost/syncpos/install/check.php
```
Shows:
- PHP version and extensions
- File permissions
- Missing dependencies
- Apache modules

### Tool 2: Simple Installer
```
http://localhost/syncpos/install/simple-install.php
```
- Doesn't require Composer
- Basic functionality only
- Works with minimal setup

### Tool 3: Minimal Installer
```
http://localhost/syncpos/install/minimal.php
```
- Ultra-basic setup
- Just database + admin user
- For troubleshooting only

---

## 📋 Manual Installation (If All Else Fails)

### Step 1: Create .env File
Create `syncpos/.env` with:
```env
APP_NAME=SyncPOS
APP_ENV=local
APP_KEY=base64:your-32-character-key-here
APP_DEBUG=true
APP_URL=http://localhost/syncpos

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=syncpos_db
DB_USERNAME=root
DB_PASSWORD=

SESSION_DRIVER=file
CACHE_DRIVER=file
QUEUE_CONNECTION=sync
```

### Step 2: Create Database
1. Open phpMyAdmin: `http://localhost/phpmyadmin`
2. Create database: `syncpos_db`

### Step 3: Create Basic Table
Run this SQL in phpMyAdmin:
```sql
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

INSERT INTO users (name, email, password) VALUES 
('Administrator', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi');
```
Default password: `password`

### Step 4: Create Directories
Create these folders:
- `syncpos/storage/logs`
- `syncpos/bootstrap/cache`

---

## 🔧 Environment-Specific Solutions

### XAMPP on Windows
1. **Start Apache and MySQL** from XAMPP Control Panel
2. **Check PHP version**: Must be 8.1+
3. **Enable extensions** in `php.ini`
4. **Set permissions** for storage folders

### WAMP on Windows
1. **Left-click WAMP icon** → PHP → PHP Extensions
2. **Enable**: pdo_mysql, openssl, mbstring, fileinfo
3. **Restart services**

### MAMP on Mac
1. **MAMP → Preferences → PHP**
2. **Select PHP 8.1+**
3. **Enable required extensions**

### cPanel Hosting
1. **PHP Selector**: Choose PHP 8.1+
2. **PHP Extensions**: Enable required extensions
3. **File Manager**: Set folder permissions to 755
4. **Error Logs**: Check cPanel error logs

---

## 📞 Getting Help

### Check These First:
1. ✅ **PHP Version**: 8.1 or higher
2. ✅ **MySQL Running**: Check XAMPP Control Panel
3. ✅ **Extensions Enabled**: Run diagnostic check
4. ✅ **File Permissions**: Storage folders writable
5. ✅ **mod_rewrite**: Enabled in Apache

### Error Log Locations:
- **XAMPP**: `C:\xampp\apache\logs\error.log`
- **PHP Errors**: `C:\xampp\php\logs\php_error_log`
- **Application**: `syncpos/storage/logs/laravel.log`

### Still Need Help?
1. **Run diagnostic**: `install/check.php`
2. **Check error logs** (locations above)
3. **Try simple installer**: `install/simple-install.php`
4. **Use minimal installer**: `install/minimal.php`

---

## ✅ Success Checklist

After fixing issues:
- [ ] Can access: `http://localhost/syncpos`
- [ ] No error messages displayed
- [ ] Can login with admin credentials
- [ ] Database connection working
- [ ] All PHP extensions loaded

---

## 🎯 Quick Commands Reference

```bash
# Check PHP version
php -v

# Check PHP extensions
php -m

# Install Composer dependencies
composer install --no-dev

# Set permissions (Windows - run as Admin)
icacls "storage" /grant Everyone:F /T
icacls "bootstrap\cache" /grant Everyone:F /T

# Set permissions (Linux/Mac)
chmod -R 777 storage
chmod -R 777 bootstrap/cache
```

Remember: The diagnostic tools will show you exactly what's wrong! 🔍
