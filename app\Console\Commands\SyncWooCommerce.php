<?php

namespace App\Console\Commands;

use App\Services\WooCommerceService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class SyncWooCommerce extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:woocommerce 
                            {--direction=both : Sync direction (from, to, both)}
                            {--type=products : What to sync (products, inventory, both)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync data with WooCommerce store';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $direction = $this->option('direction');
        $type = $this->option('type');

        $this->info('Starting WooCommerce sync...');

        $wooCommerceService = new WooCommerceService();

        // Test connection first
        if (!$wooCommerceService->testConnection()) {
            $this->error('Failed to connect to WooCommerce store. Please check your credentials.');
            return 1;
        }

        $this->info('Connected to WooCommerce store successfully.');

        try {
            // Sync products from WooCommerce
            if (in_array($direction, ['from', 'both']) && in_array($type, ['products', 'both'])) {
                $this->info('Syncing products from WooCommerce...');
                $results = $wooCommerceService->syncProductsFromWooCommerce();
                $this->displayResults('Products from WooCommerce', $results);
            }

            // Sync products to WooCommerce
            if (in_array($direction, ['to', 'both']) && in_array($type, ['products', 'both'])) {
                $this->info('Syncing products to WooCommerce...');
                $results = $wooCommerceService->syncProductsToWooCommerce();
                $this->displayResults('Products to WooCommerce', $results);
            }

            // Sync inventory to WooCommerce
            if (in_array($direction, ['to', 'both']) && in_array($type, ['inventory', 'both'])) {
                $this->info('Syncing inventory to WooCommerce...');
                $results = $wooCommerceService->syncInventoryToWooCommerce();
                $this->displayResults('Inventory to WooCommerce', $results);
            }

            $this->info('WooCommerce sync completed successfully.');
            return 0;

        } catch (\Exception $e) {
            $this->error('Sync failed: ' . $e->getMessage());
            Log::error('WooCommerce sync command failed: ' . $e->getMessage());
            return 1;
        }
    }

    /**
     * Display sync results
     */
    protected function displayResults(string $title, array $results): void
    {
        $this->info("=== {$title} Results ===");
        
        if (isset($results['created'])) {
            $this->line("Created: {$results['created']}");
        }
        
        if (isset($results['updated'])) {
            $this->line("Updated: {$results['updated']}");
        }

        if (!empty($results['errors'])) {
            $this->warn("Errors: " . count($results['errors']));
            foreach ($results['errors'] as $error) {
                $this->error("  - {$error}");
            }
        }

        $this->line('');
    }
}
