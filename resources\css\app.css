@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Custom fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

/* Base styles */
@layer base {
    html {
        font-family: 'Inter', system-ui, sans-serif;
        scroll-behavior: smooth;
    }

    body {
        @apply bg-gray-50 text-gray-900 antialiased;
    }

    /* Custom scrollbar */
    ::-webkit-scrollbar {
        width: 6px;
        height: 6px;
    }

    ::-webkit-scrollbar-track {
        @apply bg-gray-100;
    }

    ::-webkit-scrollbar-thumb {
        @apply bg-gray-300 rounded-full;
    }

    ::-webkit-scrollbar-thumb:hover {
        @apply bg-gray-400;
    }

    /* Focus styles */
    *:focus {
        outline: none;
    }

    .focus-ring {
        @apply focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-white;
    }
}

/* Component styles */
@layer components {
    /* Buttons */
    .btn {
        @apply inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg shadow-sm transition-all duration-200 focus-ring disabled:opacity-50 disabled:cursor-not-allowed;
    }

    .btn-primary {
        @apply btn bg-primary-600 text-white hover:bg-primary-700 active:bg-primary-800;
    }

    .btn-secondary {
        @apply btn bg-gray-600 text-white hover:bg-gray-700 active:bg-gray-800;
    }

    .btn-success {
        @apply btn bg-success-600 text-white hover:bg-success-700 active:bg-success-800;
    }

    .btn-warning {
        @apply btn bg-warning-600 text-white hover:bg-warning-700 active:bg-warning-800;
    }

    .btn-danger {
        @apply btn bg-danger-600 text-white hover:bg-danger-700 active:bg-danger-800;
    }

    .btn-outline {
        @apply btn bg-transparent border-gray-300 text-gray-700 hover:bg-gray-50 active:bg-gray-100;
    }

    .btn-ghost {
        @apply btn bg-transparent border-transparent text-gray-700 hover:bg-gray-100 active:bg-gray-200;
    }

    /* Form elements */
    .form-input {
        @apply block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:ring-primary-500 focus:border-primary-500 sm:text-sm;
    }

    .form-select {
        @apply block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm;
    }

    .form-textarea {
        @apply block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:ring-primary-500 focus:border-primary-500 sm:text-sm;
    }

    .form-checkbox {
        @apply h-4 w-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500;
    }

    .form-radio {
        @apply h-4 w-4 text-primary-600 border-gray-300 focus:ring-primary-500;
    }

    /* Cards */
    .card {
        @apply bg-white rounded-lg shadow-soft border border-gray-200;
    }

    .card-header {
        @apply px-6 py-4 border-b border-gray-200;
    }

    .card-body {
        @apply px-6 py-4;
    }

    .card-footer {
        @apply px-6 py-4 border-t border-gray-200 bg-gray-50 rounded-b-lg;
    }

    /* Tables */
    .table {
        @apply min-w-full divide-y divide-gray-200;
    }

    .table-header {
        @apply bg-gray-50;
    }

    .table-header-cell {
        @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
    }

    .table-body {
        @apply bg-white divide-y divide-gray-200;
    }

    .table-row {
        @apply hover:bg-gray-50 transition-colors duration-150;
    }

    .table-cell {
        @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
    }

    /* Badges */
    .badge {
        @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
    }

    .badge-primary {
        @apply badge bg-primary-100 text-primary-800;
    }

    .badge-success {
        @apply badge bg-success-100 text-success-800;
    }

    .badge-warning {
        @apply badge bg-warning-100 text-warning-800;
    }

    .badge-danger {
        @apply badge bg-danger-100 text-danger-800;
    }

    .badge-gray {
        @apply badge bg-gray-100 text-gray-800;
    }

    /* Alerts */
    .alert {
        @apply p-4 rounded-lg border;
    }

    .alert-success {
        @apply alert bg-success-50 border-success-200 text-success-800;
    }

    .alert-warning {
        @apply alert bg-warning-50 border-warning-200 text-warning-800;
    }

    .alert-danger {
        @apply alert bg-danger-50 border-danger-200 text-danger-800;
    }

    .alert-info {
        @apply alert bg-primary-50 border-primary-200 text-primary-800;
    }

    /* Loading states */
    .loading {
        @apply animate-pulse;
    }

    .skeleton {
        @apply bg-gray-200 rounded;
    }

    /* POS specific styles */
    .pos-grid {
        @apply grid grid-cols-1 lg:grid-cols-3 gap-6 h-screen max-h-screen;
    }

    .pos-product-grid {
        @apply grid grid-cols-2 md:grid-cols-3 xl:grid-cols-4 gap-4;
    }

    .pos-product-card {
        @apply bg-white rounded-lg p-4 shadow-sm border border-gray-200 cursor-pointer transition-all duration-200 hover:shadow-medium hover:scale-105;
    }

    .pos-cart-item {
        @apply flex items-center justify-between py-3 border-b border-gray-200 last:border-b-0;
    }

    /* Responsive utilities */
    .container-fluid {
        @apply w-full px-4 sm:px-6 lg:px-8;
    }

    .section-padding {
        @apply py-8 sm:py-12 lg:py-16;
    }
}

/* Utility classes */
@layer utilities {
    .text-balance {
        text-wrap: balance;
    }

    .text-pretty {
        text-wrap: pretty;
    }

    .scrollbar-hide {
        -ms-overflow-style: none;
        scrollbar-width: none;
    }

    .scrollbar-hide::-webkit-scrollbar {
        display: none;
    }

    /* Print styles */
    @media print {
        .no-print {
            display: none !important;
        }

        .print-only {
            display: block !important;
        }

        body {
            @apply text-black bg-white;
        }

        .card {
            @apply shadow-none border border-gray-300;
        }
    }
}
