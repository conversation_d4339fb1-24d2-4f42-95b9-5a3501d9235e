<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Store;
use App\Models\Category;
use App\Models\CustomerGroup;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Artisan;
use Tests\TestCase;

class InstallationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Remove the installed flag for testing
        Storage::delete('installed');
        
        // Remove .env file if it exists
        if (file_exists(base_path('.env'))) {
            unlink(base_path('.env'));
        }
    }

    protected function tearDown(): void
    {
        // Clean up after tests
        Storage::delete('installed');
        
        if (file_exists(base_path('.env.testing.backup'))) {
            rename(base_path('.env.testing.backup'), base_path('.env'));
        }
        
        parent::tearDown();
    }

    public function test_installation_page_is_accessible_when_not_installed()
    {
        $response = $this->get('/install');
        
        $response->assertStatus(200);
        $response->assertViewIs('install.index');
    }

    public function test_installation_redirects_when_already_installed()
    {
        // Mark as installed
        Storage::put('installed', now()->toDateTimeString());
        file_put_contents(base_path('.env'), 'APP_NAME=Test');

        $response = $this->get('/install');
        
        $response->assertRedirect('/');
        $response->assertSessionHas('info', 'Application is already installed.');
    }

    public function test_requirements_check_passes_with_valid_environment()
    {
        $response = $this->post('/install', ['step' => 1]);
        
        // Should redirect to step 2 if requirements are met
        $response->assertRedirect('/install?step=2');
    }

    public function test_database_configuration_validation()
    {
        $response = $this->post('/install', [
            'step' => 2,
            'db_host' => '',
            'db_port' => '',
            'db_name' => '',
            'db_user' => '',
        ]);
        
        $response->assertSessionHasErrors([
            'db_host',
            'db_port', 
            'db_name',
            'db_user',
        ]);
    }

    public function test_database_connection_test()
    {
        $response = $this->post('/install', [
            'step' => 2,
            'test_connection' => '1',
            'db_host' => env('DB_HOST', 'localhost'),
            'db_port' => env('DB_PORT', '3306'),
            'db_name' => env('DB_DATABASE', 'testing'),
            'db_user' => env('DB_USERNAME', 'root'),
            'db_pass' => env('DB_PASSWORD', ''),
        ]);
        
        $response->assertStatus(200);
        $response->assertJson(['success' => true]);
    }

    public function test_application_configuration_validation()
    {
        // Set database config in session
        session(['db_config' => [
            'db_host' => 'localhost',
            'db_port' => '3306',
            'db_name' => 'testing',
            'db_user' => 'root',
            'db_pass' => '',
        ]]);

        $response = $this->post('/install', [
            'step' => 3,
            'app_name' => '',
            'app_url' => '',
            'admin_name' => '',
            'admin_email' => '',
            'admin_password' => '',
            'store_name' => '',
            'store_email' => '',
        ]);
        
        $response->assertSessionHasErrors([
            'app_name',
            'app_url',
            'admin_name',
            'admin_email',
            'admin_password',
            'store_name',
            'store_email',
        ]);
    }

    public function test_complete_installation_process()
    {
        // Backup existing .env if it exists
        if (file_exists(base_path('.env'))) {
            rename(base_path('.env'), base_path('.env.testing.backup'));
        }

        // Set database config in session
        session(['db_config' => [
            'db_host' => env('DB_HOST', 'localhost'),
            'db_port' => env('DB_PORT', '3306'),
            'db_name' => env('DB_DATABASE', 'testing'),
            'db_user' => env('DB_USERNAME', 'root'),
            'db_pass' => env('DB_PASSWORD', ''),
        ]]);

        $installData = [
            'step' => 3,
            'app_name' => 'Test SyncPOS',
            'app_url' => 'http://localhost',
            'app_env' => 'local',
            'admin_name' => 'Test Admin',
            'admin_email' => '<EMAIL>',
            'admin_password' => 'password123',
            'admin_password_confirmation' => 'password123',
            'store_name' => 'Test Store',
            'store_email' => '<EMAIL>',
            'store_phone' => '555-1234',
        ];

        $response = $this->post('/install', $installData);
        
        $response->assertRedirect('/install/complete');

        // Verify .env file was created
        $this->assertTrue(file_exists(base_path('.env')));

        // Verify admin user was created
        $this->assertDatabaseHas('users', [
            'name' => 'Test Admin',
            'email' => '<EMAIL>',
            'is_active' => true,
        ]);

        // Verify store was created
        $this->assertDatabaseHas('stores', [
            'name' => 'Test Store',
            'email' => '<EMAIL>',
            'code' => 'MAIN',
            'is_active' => true,
        ]);

        // Verify sample data was created
        $this->assertDatabaseHas('categories', [
            'name' => 'Electronics',
            'slug' => 'electronics',
        ]);

        $this->assertDatabaseHas('customer_groups', [
            'name' => 'Regular Customers',
            'discount_percentage' => 0,
        ]);

        // Verify user is assigned to store
        $user = User::where('email', '<EMAIL>')->first();
        $store = Store::where('code', 'MAIN')->first();
        
        $this->assertTrue($user->stores->contains($store));
        $this->assertTrue($user->stores->where('id', $store->id)->first()->pivot->is_primary);

        // Verify installation is marked as complete
        $this->assertTrue(Storage::exists('installed'));
    }

    public function test_installation_complete_page()
    {
        // Mark as installed
        Storage::put('installed', now()->toDateTimeString());
        file_put_contents(base_path('.env'), 'APP_NAME=Test');

        $response = $this->get('/install/complete');
        
        $response->assertStatus(200);
        $response->assertViewIs('install.complete');
    }

    public function test_installation_complete_redirects_when_not_installed()
    {
        $response = $this->get('/install/complete');
        
        $response->assertRedirect('/install');
    }

    public function test_main_routes_redirect_to_install_when_not_installed()
    {
        $response = $this->get('/');
        
        $response->assertRedirect('/install');
    }

    public function test_password_confirmation_validation()
    {
        session(['db_config' => [
            'db_host' => 'localhost',
            'db_port' => '3306',
            'db_name' => 'testing',
            'db_user' => 'root',
            'db_pass' => '',
        ]]);

        $response = $this->post('/install', [
            'step' => 3,
            'app_name' => 'Test SyncPOS',
            'app_url' => 'http://localhost',
            'app_env' => 'local',
            'admin_name' => 'Test Admin',
            'admin_email' => '<EMAIL>',
            'admin_password' => 'password123',
            'admin_password_confirmation' => 'different_password',
            'store_name' => 'Test Store',
            'store_email' => '<EMAIL>',
        ]);
        
        $response->assertSessionHasErrors(['admin_password']);
    }

    public function test_roles_are_created_during_installation()
    {
        if (!class_exists(\Spatie\Permission\Models\Role::class)) {
            $this->markTestSkipped('Spatie Permission package not available');
        }

        // Complete installation
        session(['db_config' => [
            'db_host' => env('DB_HOST', 'localhost'),
            'db_port' => env('DB_PORT', '3306'),
            'db_name' => env('DB_DATABASE', 'testing'),
            'db_user' => env('DB_USERNAME', 'root'),
            'db_pass' => env('DB_PASSWORD', ''),
        ]]);

        $this->post('/install', [
            'step' => 3,
            'app_name' => 'Test SyncPOS',
            'app_url' => 'http://localhost',
            'app_env' => 'local',
            'admin_name' => 'Test Admin',
            'admin_email' => '<EMAIL>',
            'admin_password' => 'password123',
            'admin_password_confirmation' => 'password123',
            'store_name' => 'Test Store',
            'store_email' => '<EMAIL>',
        ]);

        // Verify roles were created
        $this->assertDatabaseHas('roles', ['name' => 'admin']);
        $this->assertDatabaseHas('roles', ['name' => 'manager']);
        $this->assertDatabaseHas('roles', ['name' => 'cashier']);

        // Verify admin user has admin role
        $user = User::where('email', '<EMAIL>')->first();
        $this->assertTrue($user->hasRole('admin'));
    }
}
