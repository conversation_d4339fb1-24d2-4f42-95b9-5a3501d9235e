<?php
/**
 * SyncPOS Quick Installation Script
 * Run this file to quickly set up SyncPOS for testing
 */

// Check if running from command line or web
$isCLI = php_sapi_name() === 'cli';

if (!$isCLI) {
    echo "<h1>🚀 SyncPOS Quick Installation</h1>";
    echo "<style>body { font-family: Arial, sans-serif; margin: 20px; } .success { color: green; } .error { color: red; } .info { color: blue; }</style>";
}

function output($message, $type = 'info') {
    global $isCLI;
    
    if ($isCLI) {
        echo "[$type] $message\n";
    } else {
        $class = $type;
        echo "<p class='$class'>[$type] $message</p>";
    }
}

// Step 1: Check if we're in the right directory
if (!file_exists('composer.json')) {
    output('Error: composer.json not found. Please run this script from the SyncPOS root directory.', 'error');
    exit(1);
}

output('Starting SyncPOS installation...', 'info');

// Step 2: Check if .env exists
if (!file_exists('.env')) {
    if (file_exists('.env.example')) {
        copy('.env.example', '.env');
        output('.env file created from .env.example', 'success');
    } else {
        output('Error: .env.example not found', 'error');
        exit(1);
    }
} else {
    output('.env file already exists', 'info');
}

// Step 3: Generate application key if not set
$envContent = file_get_contents('.env');
if (strpos($envContent, 'APP_KEY=') === false || strpos($envContent, 'APP_KEY=base64:') === false) {
    output('Generating application key...', 'info');
    exec('php artisan key:generate --force 2>&1', $output_lines, $return_code);
    if ($return_code === 0) {
        output('Application key generated successfully', 'success');
    } else {
        output('Failed to generate application key: ' . implode("\n", $output_lines), 'error');
    }
} else {
    output('Application key already exists', 'info');
}

// Step 4: Clear caches
output('Clearing caches...', 'info');
$commands = [
    'php artisan cache:clear',
    'php artisan config:clear',
    'php artisan route:clear',
    'php artisan view:clear'
];

foreach ($commands as $command) {
    exec($command . ' 2>&1', $output_lines, $return_code);
    if ($return_code !== 0) {
        output("Warning: Failed to run '$command'", 'error');
    }
}
output('Caches cleared', 'success');

// Step 5: Set basic environment variables for testing
$envUpdates = [
    'APP_NAME' => 'SyncPOS',
    'APP_ENV' => 'local',
    'APP_DEBUG' => 'true',
    'APP_URL' => 'http://localhost',
    'DB_CONNECTION' => 'mysql',
    'DB_HOST' => '127.0.0.1',
    'DB_PORT' => '3306',
    'DB_DATABASE' => 'syncpos',
    'DB_USERNAME' => 'root',
    'DB_PASSWORD' => '',
];

$envContent = file_get_contents('.env');
$updated = false;

foreach ($envUpdates as $key => $value) {
    $pattern = "/^$key=.*$/m";
    $replacement = "$key=$value";
    
    if (preg_match($pattern, $envContent)) {
        // Update existing value only if it's empty or default
        if (preg_match("/^$key=$/m", $envContent) || preg_match("/^$key=laravel$/m", $envContent)) {
            $envContent = preg_replace($pattern, $replacement, $envContent);
            $updated = true;
        }
    } else {
        // Add new value
        $envContent .= "\n$replacement";
        $updated = true;
    }
}

if ($updated) {
    file_put_contents('.env', $envContent);
    output('Environment variables updated', 'success');
}

// Step 6: Test database connection
output('Testing database connection...', 'info');
exec('php artisan tinker --execute="DB::connection()->getPdo(); echo \'Database connected successfully\';" 2>&1', $db_output, $db_return);

if ($db_return === 0 && strpos(implode('', $db_output), 'Database connected successfully') !== false) {
    output('Database connection successful', 'success');
    
    // Step 7: Run migrations
    output('Running database migrations...', 'info');
    exec('php artisan migrate --force 2>&1', $migrate_output, $migrate_return);
    
    if ($migrate_return === 0) {
        output('Database migrations completed', 'success');
        
        // Step 8: Seed database
        output('Seeding database with sample data...', 'info');
        exec('php artisan db:seed --force 2>&1', $seed_output, $seed_return);
        
        if ($seed_return === 0) {
            output('Database seeded successfully', 'success');
        } else {
            output('Warning: Database seeding failed: ' . implode("\n", $seed_output), 'error');
        }
    } else {
        output('Database migration failed: ' . implode("\n", $migrate_output), 'error');
    }
} else {
    output('Database connection failed. Please check your database settings in .env', 'error');
    output('Make sure MySQL is running and the database exists', 'info');
}

// Step 9: Create storage link
output('Creating storage link...', 'info');
exec('php artisan storage:link 2>&1', $storage_output, $storage_return);
if ($storage_return === 0) {
    output('Storage link created', 'success');
}

// Final instructions
if (!$isCLI) {
    echo "<hr>";
    echo "<h2>🎉 Installation Complete!</h2>";
    echo "<p><strong>Next steps:</strong></p>";
    echo "<ol>";
    echo "<li>Update your database settings in <code>.env</code> if needed</li>";
    echo "<li>Test Laravel: <a href='http://localhost/syncpos/public/test' target='_blank'>http://localhost/syncpos/public/test</a></li>";
    echo "<li>Access installation: <a href='http://localhost/syncpos/public/install' target='_blank'>http://localhost/syncpos/public/install</a></li>";
    echo "<li>Or use Laravel server: <code>php artisan serve</code> then visit <a href='http://localhost:8000/install' target='_blank'>http://localhost:8000/install</a></li>";
    echo "</ol>";
    
    echo "<h3>🔧 Troubleshooting:</h3>";
    echo "<ul>";
    echo "<li>If you get 404 errors, make sure you're accessing the <code>public</code> directory</li>";
    echo "<li>If database connection fails, create the database: <code>CREATE DATABASE syncpos;</code></li>";
    echo "<li>Check error logs in <code>storage/logs/laravel.log</code></li>";
    echo "</ul>";
} else {
    output('Installation complete!', 'success');
    output('Next: Visit http://localhost/syncpos/public/install or run "php artisan serve" and visit http://localhost:8000/install', 'info');
}
?>
