# SyncPOS - SaaS Point of Sale & Inventory Management System

A comprehensive, multi-tenant SaaS-based Point of Sale and Inventory Management System built with Laravel 11, featuring WooCommerce and Shopify integrations, Stripe billing, and a modern responsive interface.

## 🚀 Features

### Core Features
- **Multi-Tenant Architecture** - Database-per-tenant isolation with custom domains
- **Subscription Billing** - Stripe-powered subscription management with multiple plans
- **Role-Based Access Control** - Admin, Manager, and Cashier roles with granular permissions
- **Modern POS Interface** - Responsive Vue.js interface optimized for desktop and touchscreen
- **Inventory Management** - Real-time stock tracking, transfers, and adjustments
- **Purchase Orders** - Complete supplier and purchase order workflow
- **Customer Management** - Customer profiles with loyalty points system
- **Comprehensive Reporting** - Sales, inventory, product, and staff performance reports

### Integrations
- **WooCommerce** - Bidirectional product and inventory sync
- **Shopify** - Bidirectional product and inventory sync
- **Stripe** - Subscription billing and payment processing

### Technical Features
- **Laravel 11** - Latest Laravel framework with modern PHP 8.2+
- **Vue.js 3** - Modern reactive frontend components
- **Multi-Store Support** - Manage multiple store locations
- **API-First Design** - RESTful APIs for all operations
- **Offline Support** - POS works offline with local storage
- **Real-time Updates** - Live inventory and sales updates
- **Export/Import** - Excel/CSV support for bulk operations

## 📋 Requirements

- PHP 8.2 or higher
- MySQL 8.0 or higher
- Composer
- Node.js 18+ and NPM
- Redis (optional, for caching and queues)

## 🛠 Installation

### 1. Clone the Repository
```bash
git clone https://github.com/your-repo/syncpos.git
cd syncpos
```

### 2. Install Dependencies
```bash
# Install PHP dependencies
composer install

# Install Node.js dependencies
npm install
```

### 3. Environment Setup
```bash
# Copy environment file
cp .env.example .env

# Generate application key
php artisan key:generate
```

### 4. Configure Environment Variables
Edit `.env` file with your database and service credentials:

```env
# Database Configuration
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=syncpos_landlord
DB_USERNAME=root
DB_PASSWORD=

# Stripe Configuration
STRIPE_KEY=pk_test_...
STRIPE_SECRET=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...

# WooCommerce Integration
WOOCOMMERCE_CONSUMER_KEY=ck_...
WOOCOMMERCE_CONSUMER_SECRET=cs_...
WOOCOMMERCE_STORE_URL=https://your-store.com

# Shopify Integration
SHOPIFY_API_KEY=...
SHOPIFY_API_SECRET=...
SHOPIFY_WEBHOOK_SECRET=...
```

### 5. Database Setup
```bash
# Run central database migrations
php artisan migrate

# Create tenant databases and run tenant migrations
php artisan tenants:migrate
```

### 6. Seed Data
```bash
# Seed roles and permissions for tenants
php artisan tenants:seed --class=RolePermissionSeeder

# Create demo tenant (optional)
php artisan tenant:create demo "Demo Company" <EMAIL>
```

### 7. Build Assets
```bash
# Development
npm run dev

# Production
npm run build
```

### 8. Start the Application
```bash
# Start Laravel development server
php artisan serve

# Start queue worker (in separate terminal)
php artisan queue:work

# Start scheduler (for production)
php artisan schedule:work
```

## 🏗 Architecture

### Multi-Tenancy
The system uses a database-per-tenant architecture:
- **Central Database**: Stores tenant information, domains, and subscriptions
- **Tenant Databases**: Each tenant has their own database for complete data isolation

### Directory Structure
```
app/
├── Console/Commands/     # Artisan commands
├── Http/Controllers/     # Web and API controllers
├── Models/              # Eloquent models
├── Services/            # Business logic services
└── Providers/           # Service providers

database/
├── migrations/          # Central database migrations
└── migrations/tenant/   # Tenant database migrations

resources/
├── js/                  # Vue.js components
├── css/                 # Stylesheets
└── views/               # Blade templates
```

## 🔧 Configuration

### Subscription Plans
Configure your Stripe price IDs in the environment:
```env
STRIPE_BASIC_PRICE_ID=price_...
STRIPE_PROFESSIONAL_PRICE_ID=price_...
STRIPE_ENTERPRISE_PRICE_ID=price_...
```

### Tenant Settings
Each tenant can configure:
- Business information
- Tax settings
- Receipt customization
- Integration credentials
- User permissions

## 📊 Usage

### Creating a Tenant
```bash
php artisan tenant:create {domain} {company_name} {email}
```

### Syncing Integrations
```bash
# Sync with WooCommerce
php artisan sync:woocommerce --direction=both --type=products

# Sync with Shopify
php artisan sync:shopify --direction=both --type=inventory
```

### Running Reports
Access comprehensive reports through the web interface:
- Sales reports with date ranges and filters
- Inventory valuation and low stock alerts
- Product performance analytics
- Customer behavior insights
- Staff performance metrics

## 🧪 Testing

```bash
# Run all tests
php artisan test

# Run specific test suite
php artisan test --testsuite=Feature

# Run with coverage
php artisan test --coverage
```

## 📚 API Documentation

The system provides RESTful APIs for all operations:

### Authentication
```bash
# Login
POST /api/login
{
    "email": "<EMAIL>",
    "password": "password"
}
```

### Products
```bash
# List products
GET /api/products?search=term&category_id=1

# Create product
POST /api/products
{
    "name": "Product Name",
    "sku": "SKU123",
    "price": 29.99
}
```

### Sales
```bash
# Create sale
POST /api/sales
{
    "store_id": 1,
    "customer_id": 1,
    "items": [
        {
            "product_id": 1,
            "quantity": 2,
            "price": 29.99
        }
    ]
}
```

## 🚀 Deployment

### Docker Deployment
```bash
# Build and start containers
docker-compose up -d

# Run migrations
docker-compose exec app php artisan migrate
```

### Production Checklist
- [ ] Set `APP_ENV=production`
- [ ] Configure proper database credentials
- [ ] Set up SSL certificates
- [ ] Configure queue workers
- [ ] Set up scheduled tasks
- [ ] Configure backup strategy
- [ ] Set up monitoring and logging

## 🔒 Security

- CSRF protection on all forms
- SQL injection prevention with Eloquent ORM
- XSS protection with Blade templating
- Rate limiting on API endpoints
- Secure password hashing
- Role-based access control
- Tenant data isolation

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- Email: <EMAIL>
- Documentation: https://docs.syncpos.com
- Issues: https://github.com/your-repo/syncpos/issues

## 🙏 Acknowledgments

- Laravel Framework
- Vue.js
- Stripe
- WooCommerce
- Shopify
- All open-source contributors
