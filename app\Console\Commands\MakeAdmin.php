<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Models\Store;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;

class MakeAdmin extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'make:admin {--email=<EMAIL>} {--password=admin123} {--name=Admin}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create an admin user for SyncPOS';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->option('email');
        $password = $this->option('password');
        $name = $this->option('name');

        // Check if user already exists
        if (User::where('email', $email)->exists()) {
            $this->error("User with email {$email} already exists!");
            return 1;
        }

        // Create admin role if it doesn't exist
        if (class_exists(Role::class)) {
            $adminRole = Role::firstOrCreate(['name' => 'admin']);
            $this->info('Admin role created/found.');
        }

        // Create user
        $user = User::create([
            'name' => $name,
            'email' => $email,
            'password' => Hash::make($password),
            'email_verified_at' => now(),
            'is_active' => true,
        ]);

        // Assign admin role
        if (class_exists(Role::class) && isset($adminRole)) {
            $user->assignRole($adminRole);
            $this->info('Admin role assigned to user.');
        }

        // Create or assign to main store
        $store = Store::firstOrCreate([
            'code' => 'MAIN'
        ], [
            'name' => 'Main Store',
            'email' => $email,
            'phone' => '******-0123',
            'address' => '123 Main Street',
            'city' => 'Your City',
            'state' => 'Your State',
            'country' => 'Your Country',
            'postal_code' => '12345',
            'is_active' => true,
            'settings' => [
                'currency' => 'USD',
                'timezone' => 'UTC',
                'tax' => [
                    'enabled' => false,
                    'rate' => 0,
                ],
            ],
        ]);

        // Assign user to store
        if (!$user->stores()->where('store_id', $store->id)->exists()) {
            $user->stores()->attach($store->id, ['is_primary' => true]);
            $this->info('User assigned to main store.');
        }

        $this->info('Admin user created successfully!');
        $this->table(['Field', 'Value'], [
            ['Name', $name],
            ['Email', $email],
            ['Password', $password],
            ['Store', $store->name],
        ]);

        $this->warn('Please change the default password after first login!');

        return 0;
    }
}
