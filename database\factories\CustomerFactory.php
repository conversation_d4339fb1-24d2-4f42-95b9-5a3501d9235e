<?php

namespace Database\Factories;

use App\Models\Customer;
use App\Models\CustomerGroup;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Customer>
 */
class CustomerFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Customer::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->name(),
            'email' => $this->faker->unique()->safeEmail(),
            'phone' => $this->faker->optional(0.8)->phoneNumber(),
            'address' => $this->faker->optional(0.7)->streetAddress(),
            'city' => $this->faker->optional(0.7)->city(),
            'state' => $this->faker->optional(0.7)->state(),
            'country' => $this->faker->optional(0.7)->country(),
            'postal_code' => $this->faker->optional(0.7)->postcode(),
            'date_of_birth' => $this->faker->optional(0.5)->dateTimeBetween('-80 years', '-18 years'),
            'customer_group_id' => $this->faker->optional(0.8)->randomElement([CustomerGroup::factory(), null]),
            'loyalty_points' => $this->faker->numberBetween(0, 1000),
            'total_spent' => $this->faker->randomFloat(2, 0, 5000),
            'notes' => $this->faker->optional(0.3)->paragraph(),
            'is_active' => $this->faker->boolean(95),
            'tax_number' => $this->faker->optional(0.2)->regexify('[A-Z0-9]{10}'),
            'company_name' => $this->faker->optional(0.3)->company(),
            'website' => $this->faker->optional(0.2)->url(),
        ];
    }

    /**
     * Indicate that the customer is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
        ]);
    }

    /**
     * Indicate that the customer is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Create a VIP customer with high loyalty points.
     */
    public function vip(): static
    {
        return $this->state(fn (array $attributes) => [
            'loyalty_points' => $this->faker->numberBetween(1000, 5000),
            'total_spent' => $this->faker->randomFloat(2, 2000, 10000),
        ]);
    }

    /**
     * Create a business customer.
     */
    public function business(): static
    {
        return $this->state(fn (array $attributes) => [
            'company_name' => $this->faker->company(),
            'tax_number' => $this->faker->regexify('[A-Z0-9]{10}'),
            'website' => $this->faker->url(),
            'total_spent' => $this->faker->randomFloat(2, 1000, 20000),
        ]);
    }

    /**
     * Create a customer with no email.
     */
    public function noEmail(): static
    {
        return $this->state(fn (array $attributes) => [
            'email' => null,
        ]);
    }

    /**
     * Create a customer with complete address.
     */
    public function withCompleteAddress(): static
    {
        return $this->state(fn (array $attributes) => [
            'address' => $this->faker->streetAddress(),
            'city' => $this->faker->city(),
            'state' => $this->faker->state(),
            'country' => $this->faker->country(),
            'postal_code' => $this->faker->postcode(),
        ]);
    }

    /**
     * Create a customer with high loyalty points.
     */
    public function withLoyaltyPoints(int $points = null): static
    {
        return $this->state(fn (array $attributes) => [
            'loyalty_points' => $points ?? $this->faker->numberBetween(500, 2000),
        ]);
    }

    /**
     * Create a customer with purchase history.
     */
    public function withPurchaseHistory(): static
    {
        return $this->state(fn (array $attributes) => [
            'total_spent' => $this->faker->randomFloat(2, 100, 5000),
            'loyalty_points' => $this->faker->numberBetween(50, 1000),
        ]);
    }
}
