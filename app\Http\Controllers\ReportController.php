<?php

namespace App\Http\Controllers;

use App\Models\Sale;
use App\Models\Product;
use App\Models\Customer;
use App\Models\User;
use App\Models\Store;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Barryvdh\DomPDF\Facade\Pdf;
use Maatwebsite\Excel\Facades\Excel;

class ReportController extends Controller
{
    public function sales(Request $request)
    {
        $startDate = $request->start_date ? Carbon::parse($request->start_date) : now()->startOfMonth();
        $endDate = $request->end_date ? Carbon::parse($request->end_date) : now()->endOfMonth();
        $storeId = $request->store_id;

        // Sales summary
        $salesQuery = Sale::whereBetween('created_at', [$startDate, $endDate])
            ->where('payment_status', 'paid');

        if ($storeId) {
            $salesQuery->where('store_id', $storeId);
        }

        $salesSummary = [
            'total_sales' => $salesQuery->sum('total_amount'),
            'total_transactions' => $salesQuery->count(),
            'average_transaction' => $salesQuery->avg('total_amount'),
            'total_tax' => $salesQuery->sum('tax_amount'),
            'total_discount' => $salesQuery->sum('discount_amount'),
        ];

        // Daily sales chart data
        $dailySales = $salesQuery->clone()
            ->select(
                DB::raw('DATE(created_at) as date'),
                DB::raw('SUM(total_amount) as total'),
                DB::raw('COUNT(*) as transactions')
            )
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        // Payment method breakdown
        $paymentMethods = $salesQuery->clone()
            ->select('payment_method', DB::raw('SUM(total_amount) as total'))
            ->groupBy('payment_method')
            ->get();

        // Top selling products
        $topProducts = Product::select('products.*', DB::raw('SUM(sale_items.quantity) as total_sold'))
            ->join('sale_items', 'products.id', '=', 'sale_items.product_id')
            ->join('sales', 'sale_items.sale_id', '=', 'sales.id')
            ->whereBetween('sales.created_at', [$startDate, $endDate])
            ->where('sales.payment_status', 'paid')
            ->when($storeId, function ($query) use ($storeId) {
                return $query->where('sales.store_id', $storeId);
            })
            ->groupBy('products.id')
            ->orderBy('total_sold', 'desc')
            ->limit(10)
            ->get();

        $stores = Store::active()->get();

        return view('reports.sales', compact(
            'salesSummary', 'dailySales', 'paymentMethods', 'topProducts',
            'startDate', 'endDate', 'storeId', 'stores'
        ));
    }

    public function inventory(Request $request)
    {
        $storeId = $request->store_id;
        $store = $storeId ? Store::find($storeId) : null;

        // Inventory summary
        $inventoryQuery = Product::with(['category', 'brand', 'stores']);

        if ($store) {
            $inventoryQuery->whereHas('stores', function ($q) use ($store) {
                $q->where('store_id', $store->id);
            });
        }

        $products = $inventoryQuery->get();

        $inventorySummary = [
            'total_products' => $products->count(),
            'total_value' => $products->sum(function ($product) use ($store) {
                $stock = $store ? $product->getStockForStore($store) : $product->total_stock;
                return $stock * $product->cost_price;
            }),
            'low_stock_items' => $products->filter(function ($product) use ($store) {
                if ($store) {
                    $pivot = $product->stores->where('id', $store->id)->first()?->pivot;
                    return $pivot && $pivot->stock_quantity <= $pivot->min_stock_level;
                }
                return false;
            })->count(),
            'out_of_stock_items' => $products->filter(function ($product) use ($store) {
                $stock = $store ? $product->getStockForStore($store) : $product->total_stock;
                return $stock <= 0;
            })->count(),
        ];

        // Low stock products
        $lowStockProducts = $products->filter(function ($product) use ($store) {
            if ($store) {
                $pivot = $product->stores->where('id', $store->id)->first()?->pivot;
                return $pivot && $pivot->stock_quantity <= $pivot->min_stock_level;
            }
            return false;
        })->take(20);

        // Category breakdown
        $categoryBreakdown = $products->groupBy('category.name')->map(function ($products, $category) use ($store) {
            return [
                'category' => $category ?: 'Uncategorized',
                'count' => $products->count(),
                'value' => $products->sum(function ($product) use ($store) {
                    $stock = $store ? $product->getStockForStore($store) : $product->total_stock;
                    return $stock * $product->cost_price;
                }),
            ];
        })->values();

        $stores = Store::active()->get();

        return view('reports.inventory', compact(
            'inventorySummary', 'lowStockProducts', 'categoryBreakdown',
            'storeId', 'stores', 'store'
        ));
    }

    public function products(Request $request)
    {
        $startDate = $request->start_date ? Carbon::parse($request->start_date) : now()->startOfMonth();
        $endDate = $request->end_date ? Carbon::parse($request->end_date) : now()->endOfMonth();
        $storeId = $request->store_id;

        // Product performance
        $productPerformance = Product::select(
                'products.*',
                DB::raw('SUM(sale_items.quantity) as total_sold'),
                DB::raw('SUM(sale_items.total) as total_revenue'),
                DB::raw('SUM(sale_items.quantity * sale_items.cost_price) as total_cost'),
                DB::raw('SUM(sale_items.total) - SUM(sale_items.quantity * sale_items.cost_price) as profit')
            )
            ->leftJoin('sale_items', 'products.id', '=', 'sale_items.product_id')
            ->leftJoin('sales', 'sale_items.sale_id', '=', 'sales.id')
            ->whereBetween('sales.created_at', [$startDate, $endDate])
            ->where('sales.payment_status', 'paid')
            ->when($storeId, function ($query) use ($storeId) {
                return $query->where('sales.store_id', $storeId);
            })
            ->groupBy('products.id')
            ->orderBy('total_revenue', 'desc')
            ->paginate(20);

        // Category performance
        $categoryPerformance = DB::table('categories')
            ->select(
                'categories.name',
                DB::raw('SUM(sale_items.quantity) as total_sold'),
                DB::raw('SUM(sale_items.total) as total_revenue')
            )
            ->join('products', 'categories.id', '=', 'products.category_id')
            ->join('sale_items', 'products.id', '=', 'sale_items.product_id')
            ->join('sales', 'sale_items.sale_id', '=', 'sales.id')
            ->whereBetween('sales.created_at', [$startDate, $endDate])
            ->where('sales.payment_status', 'paid')
            ->when($storeId, function ($query) use ($storeId) {
                return $query->where('sales.store_id', $storeId);
            })
            ->groupBy('categories.id', 'categories.name')
            ->orderBy('total_revenue', 'desc')
            ->get();

        $stores = Store::active()->get();

        return view('reports.products', compact(
            'productPerformance', 'categoryPerformance',
            'startDate', 'endDate', 'storeId', 'stores'
        ));
    }

    public function customers(Request $request)
    {
        $startDate = $request->start_date ? Carbon::parse($request->start_date) : now()->startOfMonth();
        $endDate = $request->end_date ? Carbon::parse($request->end_date) : now()->endOfMonth();

        // Customer summary
        $customerSummary = [
            'total_customers' => Customer::count(),
            'new_customers' => Customer::whereBetween('created_at', [$startDate, $endDate])->count(),
            'repeat_customers' => Customer::whereHas('sales', function ($query) use ($startDate, $endDate) {
                $query->whereBetween('created_at', [$startDate, $endDate]);
            }, '>', 1)->count(),
            'average_order_value' => Sale::whereBetween('created_at', [$startDate, $endDate])
                ->where('payment_status', 'paid')
                ->whereNotNull('customer_id')
                ->avg('total_amount'),
        ];

        // Top customers
        $topCustomers = Customer::select(
                'customers.*',
                DB::raw('COUNT(sales.id) as total_orders'),
                DB::raw('SUM(sales.total_amount) as total_spent')
            )
            ->join('sales', 'customers.id', '=', 'sales.customer_id')
            ->whereBetween('sales.created_at', [$startDate, $endDate])
            ->where('sales.payment_status', 'paid')
            ->groupBy('customers.id')
            ->orderBy('total_spent', 'desc')
            ->limit(20)
            ->get();

        // Customer acquisition chart
        $customerAcquisition = Customer::select(
                DB::raw('DATE(created_at) as date'),
                DB::raw('COUNT(*) as new_customers')
            )
            ->whereBetween('created_at', [$startDate, $endDate])
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        return view('reports.customers', compact(
            'customerSummary', 'topCustomers', 'customerAcquisition',
            'startDate', 'endDate'
        ));
    }

    public function staff(Request $request)
    {
        $startDate = $request->start_date ? Carbon::parse($request->start_date) : now()->startOfMonth();
        $endDate = $request->end_date ? Carbon::parse($request->end_date) : now()->endOfMonth();
        $storeId = $request->store_id;

        // Staff performance
        $staffPerformance = User::select(
                'users.*',
                DB::raw('COUNT(sales.id) as total_sales'),
                DB::raw('SUM(sales.total_amount) as total_revenue'),
                DB::raw('AVG(sales.total_amount) as average_sale')
            )
            ->join('sales', 'users.id', '=', 'sales.cashier_id')
            ->whereBetween('sales.created_at', [$startDate, $endDate])
            ->where('sales.payment_status', 'paid')
            ->when($storeId, function ($query) use ($storeId) {
                return $query->where('sales.store_id', $storeId);
            })
            ->groupBy('users.id')
            ->orderBy('total_revenue', 'desc')
            ->get();

        // Daily performance chart
        $dailyPerformance = Sale::select(
                DB::raw('DATE(created_at) as date'),
                'cashier_id',
                'users.name as cashier_name',
                DB::raw('SUM(total_amount) as daily_total')
            )
            ->join('users', 'sales.cashier_id', '=', 'users.id')
            ->whereBetween('sales.created_at', [$startDate, $endDate])
            ->where('sales.payment_status', 'paid')
            ->when($storeId, function ($query) use ($storeId) {
                return $query->where('sales.store_id', $storeId);
            })
            ->groupBy('date', 'cashier_id', 'users.name')
            ->orderBy('date')
            ->get();

        $stores = Store::active()->get();

        return view('reports.staff', compact(
            'staffPerformance', 'dailyPerformance',
            'startDate', 'endDate', 'storeId', 'stores'
        ));
    }

    public function exportPdf(Request $request, string $reportType)
    {
        $data = $this->getReportData($request, $reportType);
        
        $pdf = Pdf::loadView("reports.pdf.{$reportType}", $data);
        
        return $pdf->download("{$reportType}_report_" . now()->format('Y-m-d') . '.pdf');
    }

    public function exportExcel(Request $request, string $reportType)
    {
        $data = $this->getReportData($request, $reportType);
        
        return Excel::download(
            new \App\Exports\ReportExport($data, $reportType),
            "{$reportType}_report_" . now()->format('Y-m-d') . '.xlsx'
        );
    }

    private function getReportData(Request $request, string $reportType): array
    {
        switch ($reportType) {
            case 'sales':
                return $this->getSalesReportData($request);
            case 'inventory':
                return $this->getInventoryReportData($request);
            case 'products':
                return $this->getProductsReportData($request);
            case 'customers':
                return $this->getCustomersReportData($request);
            case 'staff':
                return $this->getStaffReportData($request);
            default:
                throw new \InvalidArgumentException("Invalid report type: {$reportType}");
        }
    }

    // Add private methods for each report type data gathering...
}
