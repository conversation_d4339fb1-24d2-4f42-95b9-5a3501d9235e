<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\ProductController;
use App\Http\Controllers\Api\InventoryController;
use App\Http\Controllers\Api\SaleController;
use App\Http\Controllers\Api\CustomerController;
use App\Http\Controllers\Api\IntegrationController;

Route::middleware(['auth:sanctum'])->group(function () {
    Route::get('/user', function (Request $request) {
        return $request->user();
    });
    
    // Product API
    Route::apiResource('products', ProductController::class);
    Route::get('products/search/{query}', [ProductController::class, 'search']);
    Route::get('products/barcode/{barcode}', [ProductController::class, 'findByBarcode']);
    
    // Inventory API
    Route::get('inventory', [InventoryController::class, 'index']);
    Route::post('inventory/adjust', [InventoryController::class, 'adjust']);
    Route::get('inventory/low-stock', [InventoryController::class, 'lowStock']);
    
    // Sales API
    Route::apiResource('sales', SaleController::class)->only(['index', 'store', 'show']);
    Route::post('sales/{sale}/refund', [SaleController::class, 'refund']);
    
    // Customer API
    Route::apiResource('customers', CustomerController::class);
    Route::get('customers/search/{query}', [CustomerController::class, 'search']);
    
    // Integration API
    Route::prefix('integrations')->group(function () {
        Route::post('woocommerce/sync', [IntegrationController::class, 'syncWooCommerce']);
        Route::post('shopify/sync', [IntegrationController::class, 'syncShopify']);
        Route::get('sync-status', [IntegrationController::class, 'syncStatus']);
    });
});

// Webhook routes (no auth required)
Route::prefix('webhooks')->group(function () {
    Route::post('stripe', [App\Http\Controllers\WebhookController::class, 'stripe']);
    Route::post('woocommerce', [App\Http\Controllers\WebhookController::class, 'woocommerce']);
    Route::post('shopify', [App\Http\Controllers\WebhookController::class, 'shopify']);
});
