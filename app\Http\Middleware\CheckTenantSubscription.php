<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckTenantSubscription
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string $feature = null): Response
    {
        $tenant = tenant();

        if (!$tenant) {
            return redirect()->route('login');
        }

        // Check if tenant is active
        if (!$tenant->is_active) {
            return response()->view('errors.tenant-suspended', [], 403);
        }

        // Check if tenant has active subscription or is on trial
        if (!$tenant->hasActiveSubscription()) {
            return redirect()->route('billing.subscription');
        }

        // Check feature access if specified
        if ($feature && !$tenant->canAccessFeature($feature)) {
            return response()->view('errors.feature-not-available', [
                'feature' => $feature,
                'current_plan' => $tenant->subscription_plan,
            ], 403);
        }

        return $next($request);
    }
}
