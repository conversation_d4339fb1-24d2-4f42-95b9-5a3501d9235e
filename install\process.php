<?php
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Location: index.php');
    exit;
}

$step = $_POST['step'] ?? 1;

try {
    if ($step == 2) {
        // Database configuration step
        $dbHost = $_POST['db_host'] ?? '';
        $dbName = $_POST['db_name'] ?? '';
        $dbUser = $_POST['db_user'] ?? '';
        $dbPass = $_POST['db_pass'] ?? '';
        $dbPort = $_POST['db_port'] ?? '3306';

        // Test database connection
        try {
            $dsn = "mysql:host={$dbHost};port={$dbPort};charset=utf8mb4";
            $pdo = new PDO($dsn, $dbUser, $dbPass, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            ]);

            // Check if database exists, create if not
            $stmt = $pdo->query("SHOW DATABASES LIKE '{$dbName}'");
            if ($stmt->rowCount() == 0) {
                $pdo->exec("CREATE DATABASE `{$dbName}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            }

            // Store database config in session
            $_SESSION['db_config'] = [
                'host' => $dbHost,
                'name' => $dbName,
                'user' => $dbUser,
                'pass' => $dbPass,
                'port' => $dbPort,
            ];

            $_SESSION['success'] = 'Database connection successful!';
            header('Location: index.php?step=3');
            exit;

        } catch (PDOException $e) {
            $_SESSION['error'] = 'Database connection failed: ' . $e->getMessage();
            header('Location: index.php?step=2');
            exit;
        }

    } elseif ($step == 3) {
        // Installation step
        $appName = $_POST['app_name'] ?? 'SyncPOS';
        $appUrl = rtrim($_POST['app_url'] ?? '', '/');
        $appEnv = $_POST['app_env'] ?? 'production';
        $adminEmail = $_POST['admin_email'] ?? '';
        $adminPassword = $_POST['admin_password'] ?? '';

        if (!$_SESSION['db_config']) {
            $_SESSION['error'] = 'Database configuration not found. Please start over.';
            header('Location: index.php?step=2');
            exit;
        }

        $dbConfig = $_SESSION['db_config'];

        // Generate APP_KEY
        $appKey = 'base64:' . base64_encode(random_bytes(32));

        // Create .env file
        $envContent = createEnvContent($appName, $appUrl, $appEnv, $appKey, $dbConfig);
        
        if (!file_put_contents('../.env', $envContent)) {
            throw new Exception('Could not create .env file. Please check file permissions.');
        }

        // Install Composer dependencies if composer.json exists
        if (file_exists('../composer.json')) {
            $composerOutput = [];
            $composerReturn = 0;
            
            // Try to run composer install
            exec('cd .. && composer install --no-dev --optimize-autoloader 2>&1', $composerOutput, $composerReturn);
            
            if ($composerReturn !== 0) {
                // If composer command fails, try alternative methods
                if (!file_exists('../vendor/autoload.php')) {
                    throw new Exception('Composer dependencies not installed. Please run "composer install" manually.');
                }
            }
        }

        // Include Laravel's autoloader
        if (file_exists('../vendor/autoload.php')) {
            require_once '../vendor/autoload.php';
            
            // Bootstrap Laravel
            $app = require_once '../bootstrap/app.php';
            $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
            $kernel->bootstrap();

            // Run migrations
            Artisan::call('migrate', ['--force' => true]);
            
            // Create admin user
            $user = new App\Models\User();
            $user->name = 'Administrator';
            $user->email = $adminEmail;
            $user->password = Hash::make($adminPassword);
            $user->email_verified_at = now();
            $user->is_active = true;
            $user->save();

            // Assign admin role if Spatie Permission is available
            if (class_exists('Spatie\Permission\Models\Role')) {
                $adminRole = Spatie\Permission\Models\Role::firstOrCreate(['name' => 'admin']);
                $user->assignRole($adminRole);
            }

        } else {
            // Manual installation without Laravel
            runManualMigrations($dbConfig);
            createAdminUser($dbConfig, $adminEmail, $adminPassword);
        }

        // Create storage directories
        createStorageDirectories();

        // Create installation lock file
        file_put_contents('../storage/installed', date('Y-m-d H:i:s'));

        $_SESSION['success'] = 'SyncPOS installed successfully!';
        unset($_SESSION['db_config']);
        header('Location: index.php?step=4');
        exit;
    }

} catch (Exception $e) {
    $_SESSION['error'] = 'Installation failed: ' . $e->getMessage();
    header('Location: index.php?step=' . ($step - 1));
    exit;
}

function createEnvContent($appName, $appUrl, $appEnv, $appKey, $dbConfig) {
    return "APP_NAME=\"{$appName}\"
APP_ENV={$appEnv}
APP_KEY={$appKey}
APP_DEBUG=" . ($appEnv === 'local' ? 'true' : 'false') . "
APP_TIMEZONE=UTC
APP_URL={$appUrl}

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=" . ($appEnv === 'local' ? 'debug' : 'error') . "

DB_CONNECTION=mysql
DB_HOST={$dbConfig['host']}
DB_PORT={$dbConfig['port']}
DB_DATABASE={$dbConfig['name']}
DB_USERNAME={$dbConfig['user']}
DB_PASSWORD={$dbConfig['pass']}

SESSION_DRIVER=file
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync

CACHE_STORE=file
CACHE_PREFIX=

MAIL_MAILER=log
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS=\"hello@{$appName}.com\"
MAIL_FROM_NAME=\"{$appName}\"

VITE_APP_NAME=\"{$appName}\"
";
}

function runManualMigrations($dbConfig) {
    $dsn = "mysql:host={$dbConfig['host']};port={$dbConfig['port']};dbname={$dbConfig['name']};charset=utf8mb4";
    $pdo = new PDO($dsn, $dbConfig['user'], $dbConfig['pass'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
    ]);

    // Create basic tables
    $sql = "
    CREATE TABLE IF NOT EXISTS `users` (
        `id` bigint unsigned NOT NULL AUTO_INCREMENT,
        `name` varchar(255) NOT NULL,
        `email` varchar(255) NOT NULL,
        `email_verified_at` timestamp NULL DEFAULT NULL,
        `password` varchar(255) NOT NULL,
        `phone` varchar(20) DEFAULT NULL,
        `avatar` varchar(255) DEFAULT NULL,
        `is_active` tinyint(1) NOT NULL DEFAULT '1',
        `last_login_at` timestamp NULL DEFAULT NULL,
        `settings` json DEFAULT NULL,
        `remember_token` varchar(100) DEFAULT NULL,
        `created_at` timestamp NULL DEFAULT NULL,
        `updated_at` timestamp NULL DEFAULT NULL,
        PRIMARY KEY (`id`),
        UNIQUE KEY `users_email_unique` (`email`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

    CREATE TABLE IF NOT EXISTS `tenants` (
        `id` varchar(255) NOT NULL,
        `company_name` varchar(255) NOT NULL,
        `company_email` varchar(255) NOT NULL,
        `company_phone` varchar(20) DEFAULT NULL,
        `company_address` text,
        `company_city` varchar(100) DEFAULT NULL,
        `company_state` varchar(100) DEFAULT NULL,
        `company_country` varchar(100) DEFAULT NULL,
        `company_postal_code` varchar(20) DEFAULT NULL,
        `subscription_plan` enum('basic','professional','enterprise') NOT NULL DEFAULT 'basic',
        `trial_ends_at` timestamp NULL DEFAULT NULL,
        `is_active` tinyint(1) NOT NULL DEFAULT '1',
        `created_at` timestamp NULL DEFAULT NULL,
        `updated_at` timestamp NULL DEFAULT NULL,
        PRIMARY KEY (`id`),
        UNIQUE KEY `tenants_company_email_unique` (`company_email`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

    CREATE TABLE IF NOT EXISTS `domains` (
        `id` int unsigned NOT NULL AUTO_INCREMENT,
        `domain` varchar(255) NOT NULL,
        `tenant_id` varchar(255) NOT NULL,
        `is_primary` tinyint(1) NOT NULL DEFAULT '0',
        `certificate` text,
        `created_at` timestamp NULL DEFAULT NULL,
        `updated_at` timestamp NULL DEFAULT NULL,
        PRIMARY KEY (`id`),
        UNIQUE KEY `domains_domain_unique` (`domain`),
        KEY `domains_tenant_id_foreign` (`tenant_id`),
        CONSTRAINT `domains_tenant_id_foreign` FOREIGN KEY (`tenant_id`) REFERENCES `tenants` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";

    $pdo->exec($sql);
}

function createAdminUser($dbConfig, $email, $password) {
    $dsn = "mysql:host={$dbConfig['host']};port={$dbConfig['port']};dbname={$dbConfig['name']};charset=utf8mb4";
    $pdo = new PDO($dsn, $dbConfig['user'], $dbConfig['pass'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
    ]);

    $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
    $now = date('Y-m-d H:i:s');

    $stmt = $pdo->prepare("INSERT INTO users (name, email, password, email_verified_at, is_active, created_at, updated_at) VALUES (?, ?, ?, ?, 1, ?, ?)");
    $stmt->execute(['Administrator', $email, $hashedPassword, $now, $now, $now]);
}

function createStorageDirectories() {
    $directories = [
        '../storage/app/public',
        '../storage/framework/cache',
        '../storage/framework/sessions',
        '../storage/framework/views',
        '../storage/logs',
        '../bootstrap/cache'
    ];

    foreach ($directories as $dir) {
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }
    }
}
?>
