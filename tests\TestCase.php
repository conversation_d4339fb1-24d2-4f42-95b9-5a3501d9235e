<?php

namespace Tests;

use Illuminate\Foundation\Testing\TestCase as BaseTestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Storage;

abstract class TestCase extends BaseTestCase
{
    use CreatesApplication;

    protected function setUp(): void
    {
        parent::setUp();

        // Set up fake storage for testing
        Storage::fake('public');
        Storage::fake('local');

        // Disable installation middleware for tests
        $this->withoutMiddleware(\App\Http\Middleware\InstallationMiddleware::class);
    }

    /**
     * Create a user and authenticate them.
     */
    protected function authenticateUser($user = null)
    {
        $user = $user ?: \App\Models\User::factory()->create();
        
        return $this->actingAs($user);
    }

    /**
     * Create a user with specific role.
     */
    protected function authenticateUserWithRole(string $role)
    {
        $user = \App\Models\User::factory()->create();
        
        if (class_exists(\Spatie\Permission\Models\Role::class)) {
            $roleModel = \Spatie\Permission\Models\Role::firstOrCreate(['name' => $role]);
            $user->assignRole($roleModel);
        }
        
        return $this->actingAs($user);
    }

    /**
     * Create an admin user and authenticate.
     */
    protected function authenticateAdmin()
    {
        return $this->authenticateUserWithRole('admin');
    }

    /**
     * Create a manager user and authenticate.
     */
    protected function authenticateManager()
    {
        return $this->authenticateUserWithRole('manager');
    }

    /**
     * Create a cashier user and authenticate.
     */
    protected function authenticateCashier()
    {
        return $this->authenticateUserWithRole('cashier');
    }

    /**
     * Create a store with products and assign user to it.
     */
    protected function createStoreWithProducts($user = null, $productCount = 5)
    {
        $user = $user ?: \App\Models\User::factory()->create();
        $store = \App\Models\Store::factory()->create();
        $products = \App\Models\Product::factory($productCount)->create();

        // Assign user to store
        $store->users()->attach($user->id, ['is_primary' => true]);

        // Attach products to store with stock
        foreach ($products as $product) {
            $store->products()->attach($product->id, [
                'stock_quantity' => rand(50, 200),
                'min_stock_level' => rand(5, 15),
                'max_stock_level' => rand(200, 500),
            ]);
        }

        return [$store, $products, $user];
    }

    /**
     * Assert that a model was logged.
     */
    protected function assertModelLogged(string $action, $model)
    {
        $this->assertTrue(
            \Illuminate\Support\Facades\Log::hasBeenCalled(),
            "Expected log entry for action '{$action}' was not found."
        );
    }

    /**
     * Assert JSON response structure matches expected structure.
     */
    protected function assertJsonResponseStructure(array $structure, $response)
    {
        $response->assertJsonStructure($structure);
        return $this;
    }

    /**
     * Assert that response contains validation errors for specific fields.
     */
    protected function assertValidationErrors(array $fields, $response)
    {
        $response->assertSessionHasErrors($fields);
        return $this;
    }

    /**
     * Create a complete sale scenario for testing.
     */
    protected function createSaleScenario()
    {
        $user = \App\Models\User::factory()->create();
        $store = \App\Models\Store::factory()->create();
        $customer = \App\Models\Customer::factory()->create();
        $product = \App\Models\Product::factory()->create([
            'price' => 10.00,
            'cost_price' => 5.00,
        ]);

        // Assign user to store
        $store->users()->attach($user->id, ['is_primary' => true]);

        // Add product to store with stock
        $store->products()->attach($product->id, [
            'stock_quantity' => 100,
            'min_stock_level' => 10,
            'max_stock_level' => 200,
        ]);

        return compact('user', 'store', 'customer', 'product');
    }

    /**
     * Create sample sale data for API testing.
     */
    protected function createSaleData($storeId, $productId, $customerId = null)
    {
        return [
            'store_id' => $storeId,
            'customer_id' => $customerId,
            'items' => [
                [
                    'product_id' => $productId,
                    'quantity' => 2,
                    'price' => 10.00,
                    'tax_rate' => 10,
                ]
            ],
            'discount_amount' => 0,
            'payment_method' => 'cash',
            'payments' => [
                [
                    'method' => 'cash',
                    'amount' => 22.00, // 2 * 10.00 + 10% tax
                ]
            ],
            'notes' => 'Test sale',
        ];
    }

    /**
     * Assert that inventory was updated correctly.
     */
    protected function assertInventoryUpdated($store, $product, $expectedQuantity)
    {
        $actualQuantity = $store->getProductStock($product);
        $this->assertEquals(
            $expectedQuantity,
            $actualQuantity,
            "Expected inventory quantity {$expectedQuantity}, but got {$actualQuantity}"
        );
    }

    /**
     * Assert that customer loyalty points were updated.
     */
    protected function assertLoyaltyPointsUpdated($customer, $expectedPoints)
    {
        $customer->refresh();
        $this->assertEquals(
            $expectedPoints,
            $customer->loyalty_points,
            "Expected loyalty points {$expectedPoints}, but got {$customer->loyalty_points}"
        );
    }

    /**
     * Mock external API calls for testing.
     */
    protected function mockExternalAPIs()
    {
        // Mock WooCommerce API
        $this->mock(\App\Services\WooCommerceService::class, function ($mock) {
            $mock->shouldReceive('testConnection')->andReturn(['status' => 'connected']);
            $mock->shouldReceive('importProducts')->andReturn(['imported' => 0, 'updated' => 0, 'errors' => []]);
            $mock->shouldReceive('exportProducts')->andReturn(['exported' => 0, 'updated' => 0, 'errors' => []]);
        });

        // Mock Shopify API
        $this->mock(\App\Services\ShopifyService::class, function ($mock) {
            $mock->shouldReceive('testConnection')->andReturn(['status' => 'connected']);
            $mock->shouldReceive('importProducts')->andReturn(['imported' => 0, 'updated' => 0, 'errors' => []]);
            $mock->shouldReceive('exportProducts')->andReturn(['exported' => 0, 'updated' => 0, 'errors' => []]);
        });
    }

    /**
     * Skip test if feature is not available.
     */
    protected function skipIfFeatureNotAvailable(string $feature)
    {
        $features = [
            'tenancy' => class_exists(\Stancl\Tenancy\Tenancy::class),
            'permissions' => class_exists(\Spatie\Permission\Models\Role::class),
        ];

        if (!($features[$feature] ?? false)) {
            $this->markTestSkipped("Feature '{$feature}' is not available");
        }
    }
}
