<?php

namespace App\Traits;

use Illuminate\Support\Facades\Log;

trait LogsActivity
{
    /**
     * Log user activity.
     */
    protected function logActivity(string $action, array $data = [], string $level = 'info'): void
    {
        $context = [
            'user_id' => auth()->id(),
            'user_email' => auth()->user()?->email,
            'action' => $action,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'timestamp' => now()->toISOString(),
        ];

        if (!empty($data)) {
            $context['data'] = $data;
        }

        Log::channel('activity')->{$level}($action, $context);
    }

    /**
     * Log successful operations.
     */
    protected function logSuccess(string $action, array $data = []): void
    {
        $this->logActivity($action, $data, 'info');
    }

    /**
     * Log failed operations.
     */
    protected function logError(string $action, \Throwable $exception, array $data = []): void
    {
        $context = array_merge($data, [
            'exception' => [
                'message' => $exception->getMessage(),
                'file' => $exception->getFile(),
                'line' => $exception->getLine(),
                'trace' => $exception->getTraceAsString(),
            ],
        ]);

        $this->logActivity($action . ' failed', $context, 'error');
    }

    /**
     * Log warning events.
     */
    protected function logWarning(string $action, string $message, array $data = []): void
    {
        $context = array_merge($data, ['warning_message' => $message]);
        $this->logActivity($action, $context, 'warning');
    }

    /**
     * Log business logic events.
     */
    protected function logBusinessEvent(string $event, array $data = []): void
    {
        $context = [
            'event' => $event,
            'user_id' => auth()->id(),
            'timestamp' => now()->toISOString(),
        ];

        if (!empty($data)) {
            $context = array_merge($context, $data);
        }

        Log::channel('business')->info($event, $context);
    }

    /**
     * Log inventory changes.
     */
    protected function logInventoryChange(
        int $productId,
        int $storeId,
        string $type,
        int $quantity,
        int $previousQuantity,
        int $newQuantity,
        string $reason
    ): void {
        $this->logBusinessEvent('inventory_changed', [
            'product_id' => $productId,
            'store_id' => $storeId,
            'adjustment_type' => $type,
            'quantity' => $quantity,
            'previous_quantity' => $previousQuantity,
            'new_quantity' => $newQuantity,
            'reason' => $reason,
        ]);
    }

    /**
     * Log sale events.
     */
    protected function logSaleEvent(string $event, int $saleId, array $additionalData = []): void
    {
        $this->logBusinessEvent("sale_{$event}", array_merge([
            'sale_id' => $saleId,
        ], $additionalData));
    }

    /**
     * Log customer events.
     */
    protected function logCustomerEvent(string $event, int $customerId, array $additionalData = []): void
    {
        $this->logBusinessEvent("customer_{$event}", array_merge([
            'customer_id' => $customerId,
        ], $additionalData));
    }

    /**
     * Log product events.
     */
    protected function logProductEvent(string $event, int $productId, array $additionalData = []): void
    {
        $this->logBusinessEvent("product_{$event}", array_merge([
            'product_id' => $productId,
        ], $additionalData));
    }

    /**
     * Log integration events.
     */
    protected function logIntegrationEvent(string $integration, string $event, array $data = []): void
    {
        $this->logBusinessEvent("integration_{$integration}_{$event}", $data);
    }

    /**
     * Log security events.
     */
    protected function logSecurityEvent(string $event, array $data = []): void
    {
        $context = [
            'event' => $event,
            'user_id' => auth()->id(),
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'timestamp' => now()->toISOString(),
        ];

        if (!empty($data)) {
            $context = array_merge($context, $data);
        }

        Log::channel('security')->warning($event, $context);
    }

    /**
     * Log performance metrics.
     */
    protected function logPerformance(string $operation, float $duration, array $data = []): void
    {
        $context = [
            'operation' => $operation,
            'duration_ms' => round($duration * 1000, 2),
            'user_id' => auth()->id(),
            'timestamp' => now()->toISOString(),
        ];

        if (!empty($data)) {
            $context = array_merge($context, $data);
        }

        Log::channel('performance')->info($operation, $context);
    }

    /**
     * Log API requests.
     */
    protected function logApiRequest(string $endpoint, string $method, array $data = []): void
    {
        $context = [
            'endpoint' => $endpoint,
            'method' => $method,
            'user_id' => auth()->id(),
            'ip_address' => request()->ip(),
            'timestamp' => now()->toISOString(),
        ];

        if (!empty($data)) {
            $context = array_merge($context, $data);
        }

        Log::channel('api')->info("API request: {$method} {$endpoint}", $context);
    }
}
