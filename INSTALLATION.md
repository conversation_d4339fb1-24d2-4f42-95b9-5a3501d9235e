# 🚀 SyncPOS Easy Installation Guide

SyncPOS now comes with a **web-based installer** that makes installation super simple! No command line required.

## 📋 Quick Start

### 1. Download & Upload
- Download SyncPOS files
- Upload to your web server (localhost or hosting)

### 2. Open in Browser
- Go to your website URL
- The installer will start automatically

### 3. Follow the Steps
- System requirements check ✅
- Database configuration 🗄️
- Application settings ⚙️
- Admin account creation 👤

### 4. Start Using SyncPOS! 🎉

---

## 🏠 Localhost Installation (XAMPP/WAMP/MAMP)

### Step 1: Setup Local Server
1. **Install XAMPP** (recommended) from [apachefriends.org](https://www.apachefriends.org/)
2. **Start Apache and MySQL** from XAMPP Control Panel

### Step 2: Extract SyncPOS
1. **Extract** the SyncPOS files to:
   - XAMPP: `C:\xampp\htdocs\syncpos`
   - WAMP: `C:\wamp64\www\syncpos`
   - MAMP: `/Applications/MAMP/htdocs/syncpos`

### Step 3: Run Installer
1. **Open browser** and go to: `http://localhost/syncpos`
2. **Follow the installer** steps:
   - Requirements check (automatic)
   - Database: Host=`localhost`, User=`root`, Password=`(empty)`
   - Create a database name like `syncpos_db`
   - Set admin email and password

### Step 4: Access Your POS
- **Main Application**: `http://localhost/syncpos`
- **Admin Panel**: `http://localhost/syncpos/admin`

---

## 🌐 cPanel/Shared Hosting Installation

### Step 1: Create Database
1. **Login to cPanel**
2. **Go to MySQL Databases**
3. **Create new database** (e.g., `youruser_syncpos`)
4. **Create database user** with strong password
5. **Assign user to database** with ALL PRIVILEGES

### Step 2: Upload Files
1. **Go to File Manager** in cPanel
2. **Navigate to public_html** (or subdirectory)
3. **Upload SyncPOS ZIP file**
4. **Extract** the files

### Step 3: Run Installer
1. **Visit your domain** in browser: `https://yourdomain.com`
2. **Enter database details** from Step 1
3. **Complete installation**

### Step 4: Security (Important!)
1. **Delete install folder** via File Manager: `/public_html/install`
2. **Set file permissions** (usually done automatically)

---

## 🖥️ VPS/Dedicated Server Installation

### Step 1: Upload Files
```bash
# Upload to web directory
cd /var/www/html
wget https://github.com/your-repo/syncpos/archive/main.zip
unzip main.zip
mv syncpos-main syncpos
```

### Step 2: Set Permissions
```bash
chmod -R 755 syncpos
chmod -R 777 syncpos/storage
chmod -R 777 syncpos/bootstrap/cache
chown -R www-data:www-data syncpos
```

### Step 3: Run Installer
1. **Visit your server IP** or domain
2. **Follow installer steps**

---

## 🔧 System Requirements

The installer automatically checks these:

### PHP Requirements
- ✅ PHP 8.1 or higher
- ✅ PDO Extension
- ✅ PDO MySQL Extension
- ✅ OpenSSL Extension
- ✅ Mbstring Extension
- ✅ Tokenizer Extension
- ✅ XML Extension
- ✅ Ctype Extension
- ✅ JSON Extension
- ✅ BCMath Extension
- ✅ Fileinfo Extension

### Database
- MySQL 5.7+ or MariaDB 10.3+

### Web Server
- Apache with mod_rewrite
- Nginx (with proper configuration)

---

## 🛠️ Installer Features

### Step 1: Requirements Check
- Automatically verifies PHP version
- Checks all required extensions
- Shows clear pass/fail status
- Provides guidance for missing requirements

### Step 2: Database Configuration
- Tests database connection
- Creates database if it doesn't exist
- Validates credentials
- User-friendly error messages

### Step 3: Application Setup
- Generates secure application key
- Creates admin account
- Sets up storage directories
- Configures environment settings

### Step 4: Completion
- Confirms successful installation
- Provides next steps
- Links to application and admin panel

---

## 🔒 Security Features

### Automatic Security
- Generates random application key
- Sets secure file permissions
- Creates protected .env file
- Blocks access to sensitive directories

### Post-Installation
- **Delete installer** folder for security
- **Change default passwords**
- **Enable HTTPS** (recommended)
- **Regular backups**

---

## 🚨 Troubleshooting

### Common Issues & Solutions

#### "Requirements not met"
**Problem**: Missing PHP extensions
**Solution**: Contact hosting provider or enable in php.ini

#### "Database connection failed"
**Problem**: Wrong database credentials
**Solution**: 
- Verify database exists
- Check username/password
- Ensure user has proper permissions

#### "Permission denied"
**Problem**: File permission issues
**Solution**:
```bash
chmod -R 755 storage
chmod -R 755 bootstrap/cache
```

#### "Page not found" after installation
**Problem**: URL rewriting not working
**Solution**:
- Check .htaccess files exist
- Enable mod_rewrite (Apache)
- Configure URL rewriting (Nginx)

#### Installer won't start
**Problem**: Files not uploaded correctly
**Solution**:
- Re-upload all files
- Check file structure
- Verify index.php exists

### Getting Help

1. **Check requirements** first
2. **Verify database connection** manually
3. **Review error messages** carefully
4. **Check hosting documentation**
5. **Contact hosting support** for server issues

---

## 🎯 What's Included

### Core Features Ready to Use
- ✅ Multi-tenant architecture
- ✅ Point of Sale interface
- ✅ Inventory management
- ✅ Customer management
- ✅ Sales reporting
- ✅ User management with roles
- ✅ Responsive design

### Optional Integrations
- 🔌 Stripe payments (configure after installation)
- 🔌 WooCommerce sync (configure after installation)
- 🔌 Shopify sync (configure after installation)

---

## 🎉 After Installation

### First Steps
1. **Login** with your admin account
2. **Create your first store**
3. **Add product categories**
4. **Import or add products**
5. **Create staff accounts**
6. **Configure business settings**

### Optional Configuration
- **Payment gateways** (Stripe)
- **Email settings** (SMTP)
- **Backup settings**
- **SSL certificate**
- **Custom domain** (for multi-tenant)

---

## 📞 Support

### Self-Help Resources
- 📖 **Documentation**: Check README.md
- 🔧 **Troubleshooting**: See common issues above
- 💡 **Tips**: Review installation guide

### Professional Support
- 🌐 **Hosting Issues**: Contact your hosting provider
- 🔧 **Server Configuration**: Consult system administrator
- 📧 **Application Support**: Contact SyncPOS support

---

## 🚀 Ready to Start?

1. **Download SyncPOS**
2. **Upload to your server**
3. **Open in browser**
4. **Follow the installer**
5. **Start selling!**

The web installer makes it that simple. No technical knowledge required! 🎉
