<?php

namespace App\Models;

use Stancl\Tenancy\Database\Models\Tenant as BaseTenant;
use Stancl\Tenancy\Contracts\TenantWithDatabase;
use Stancl\Tenancy\Database\Concerns\HasDatabase;
use Stancl\Tenancy\Database\Concerns\HasDomains;
use <PERSON><PERSON>\Cashier\Billable;

class Tenant extends BaseTenant implements TenantWithDatabase
{
    use HasDatabase, HasDomains, Billable;

    protected $fillable = [
        'id',
        'company_name',
        'company_email',
        'company_phone',
        'company_address',
        'company_city',
        'company_state',
        'company_country',
        'company_postal_code',
        'company_logo',
        'subscription_plan',
        'trial_ends_at',
        'is_active',
        'settings',
    ];

    protected $casts = [
        'trial_ends_at' => 'datetime',
        'is_active' => 'boolean',
        'settings' => 'array',
    ];

    public static function getCustomColumns(): array
    {
        return [
            'id',
            'company_name',
            'company_email',
            'company_phone',
            'company_address',
            'company_city',
            'company_state',
            'company_country',
            'company_postal_code',
            'company_logo',
            'subscription_plan',
            'trial_ends_at',
            'is_active',
            'settings',
        ];
    }

    public function getRouteKeyName()
    {
        return 'id';
    }

    public function isOnTrial(): bool
    {
        return $this->trial_ends_at && $this->trial_ends_at->isFuture();
    }

    public function hasActiveSubscription(): bool
    {
        return $this->subscribed('default') || $this->isOnTrial();
    }

    public function canAccessFeature(string $feature): bool
    {
        $plan = $this->subscription_plan ?? 'basic';
        
        $features = [
            'basic' => ['pos', 'inventory', 'basic_reports'],
            'professional' => ['pos', 'inventory', 'reports', 'integrations', 'multi_store'],
            'enterprise' => ['pos', 'inventory', 'reports', 'integrations', 'multi_store', 'advanced_analytics', 'api_access'],
        ];

        return in_array($feature, $features[$plan] ?? []);
    }

    public function getMaxStores(): int
    {
        $limits = [
            'basic' => 1,
            'professional' => 5,
            'enterprise' => 999,
        ];

        return $limits[$this->subscription_plan ?? 'basic'];
    }

    public function getMaxUsers(): int
    {
        $limits = [
            'basic' => 3,
            'professional' => 15,
            'enterprise' => 999,
        ];

        return $limits[$this->subscription_plan ?? 'basic'];
    }
}
