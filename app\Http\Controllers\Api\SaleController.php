<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Sale;
use App\Models\SaleItem;
use App\Models\Product;
use App\Models\Customer;
use App\Models\Store;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class SaleController extends Controller
{
    public function index(Request $request)
    {
        $query = Sale::with(['customer', 'cashier', 'store']);

        // Filter by store
        $storeId = $request->store_id ?? auth()->user()->primaryStore()?->id;
        if ($storeId) {
            $query->where('store_id', $storeId);
        }

        // Search by sale number or customer
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('sale_number', 'like', "%{$search}%")
                  ->orWhereHas('customer', function ($customerQuery) use ($search) {
                      $customerQuery->where('name', 'like', "%{$search}%")
                                   ->orWhere('email', 'like', "%{$search}%");
                  });
            });
        }

        // Filter by payment status
        if ($request->filled('payment_status')) {
            $query->where('payment_status', $request->payment_status);
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by date range
        if ($request->filled('start_date')) {
            $query->whereDate('created_at', '>=', $request->start_date);
        }
        if ($request->filled('end_date')) {
            $query->whereDate('created_at', '<=', $request->end_date);
        }

        $sales = $query->latest()->paginate($request->per_page ?? 20);

        return response()->json([
            'data' => $sales->items(),
            'meta' => [
                'current_page' => $sales->currentPage(),
                'last_page' => $sales->lastPage(),
                'per_page' => $sales->perPage(),
                'total' => $sales->total(),
            ],
        ]);
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'store_id' => 'required|exists:stores,id',
            'customer_id' => 'nullable|exists:customers,id',
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required|exists:products,id',
            'items.*.quantity' => 'required|integer|min:1',
            'items.*.price' => 'required|numeric|min:0',
            'items.*.tax_rate' => 'nullable|numeric|min:0|max:100',
            'discount_amount' => 'nullable|numeric|min:0',
            'payment_method' => 'required|in:cash,card,mobile,bank_transfer,other',
            'payments' => 'required|array|min:1',
            'payments.*.method' => 'required|string',
            'payments.*.amount' => 'required|numeric|min:0',
            'notes' => 'nullable|string',
        ]);

        try {
            DB::beginTransaction();

            $store = Store::findOrFail($validated['store_id']);
            
            // Check if user has access to this store
            if (!auth()->user()->hasAccessToStore($store)) {
                return response()->json([
                    'message' => 'You do not have access to this store.',
                ], 403);
            }

            // Validate stock availability
            foreach ($validated['items'] as $item) {
                $product = Product::findOrFail($item['product_id']);
                if (!$product->isInStock($store, $item['quantity'])) {
                    return response()->json([
                        'message' => "Insufficient stock for {$product->name}.",
                    ], 400);
                }
            }

            // Calculate totals
            $subtotal = 0;
            $taxAmount = 0;
            
            foreach ($validated['items'] as $item) {
                $itemTotal = $item['quantity'] * $item['price'];
                $subtotal += $itemTotal;
                $taxAmount += $itemTotal * (($item['tax_rate'] ?? 0) / 100);
            }

            $discountAmount = $validated['discount_amount'] ?? 0;
            $totalAmount = $subtotal + $taxAmount - $discountAmount;

            // Validate payment amount
            $totalPayments = array_sum(array_column($validated['payments'], 'amount'));
            if ($totalPayments < $totalAmount) {
                return response()->json([
                    'message' => 'Insufficient payment amount.',
                ], 400);
            }

            // Create sale
            $sale = Sale::create([
                'sale_number' => Sale::generateSaleNumber(),
                'store_id' => $store->id,
                'customer_id' => $validated['customer_id'],
                'cashier_id' => auth()->id(),
                'subtotal' => $subtotal,
                'tax_amount' => $taxAmount,
                'discount_amount' => $discountAmount,
                'total_amount' => $totalAmount,
                'payment_method' => $validated['payment_method'],
                'payment_status' => 'paid',
                'status' => 'completed',
                'notes' => $validated['notes'],
                'receipt_data' => [
                    'payments' => $validated['payments'],
                    'change' => $totalPayments - $totalAmount,
                ],
            ]);

            // Create sale items and update inventory
            foreach ($validated['items'] as $item) {
                $product = Product::findOrFail($item['product_id']);
                
                // Create sale item
                SaleItem::create([
                    'sale_id' => $sale->id,
                    'product_id' => $product->id,
                    'quantity' => $item['quantity'],
                    'price' => $item['price'],
                    'cost_price' => $product->cost_price,
                    'tax_rate' => $item['tax_rate'] ?? 0,
                    'total' => $item['quantity'] * $item['price'],
                ]);

                // Update inventory
                $currentStock = $store->getProductStock($product);
                $newStock = $currentStock - $item['quantity'];
                
                $store->products()->updateExistingPivot($product->id, [
                    'stock_quantity' => $newStock,
                ]);
            }

            // Update customer loyalty points
            if ($validated['customer_id']) {
                $customer = Customer::find($validated['customer_id']);
                $points = floor($totalAmount / 10); // 1 point per $10 spent
                $customer->addLoyaltyPoints($points, "Sale #{$sale->sale_number}");
                $customer->updateTotalSpent();
            }

            DB::commit();

            // Load relationships for response
            $sale->load(['items.product', 'customer', 'cashier', 'store']);

            return response()->json([
                'message' => 'Sale completed successfully.',
                'data' => $sale,
                'change' => $totalPayments - $totalAmount,
            ], 201);

        } catch (\Exception $e) {
            DB::rollback();
            
            return response()->json([
                'message' => 'Sale creation failed.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function show(Sale $sale)
    {
        // Check if user has access to this sale's store
        if (!auth()->user()->hasAccessToStore($sale->store)) {
            return response()->json([
                'message' => 'You do not have access to this sale.',
            ], 403);
        }

        $sale->load(['items.product', 'customer', 'cashier', 'store', 'refunds']);
        
        return response()->json([
            'data' => $sale,
        ]);
    }

    public function refund(Request $request, Sale $sale)
    {
        $validated = $request->validate([
            'items' => 'required|array',
            'items.*.sale_item_id' => 'required|exists:sale_items,id',
            'items.*.quantity' => 'required|integer|min:1',
            'reason' => 'required|string|max:255',
            'notes' => 'nullable|string',
        ]);

        // Check if user has access to this sale's store
        if (!auth()->user()->hasAccessToStore($sale->store)) {
            return response()->json([
                'message' => 'You do not have access to this sale.',
            ], 403);
        }

        if ($sale->status === 'refunded') {
            return response()->json([
                'message' => 'Sale has already been refunded.',
            ], 400);
        }

        try {
            DB::beginTransaction();

            $totalRefundAmount = 0;

            foreach ($validated['items'] as $itemData) {
                $saleItem = SaleItem::findOrFail($itemData['sale_item_id']);
                
                // Validate the sale item belongs to this sale
                if ($saleItem->sale_id !== $sale->id) {
                    return response()->json([
                        'message' => 'Invalid sale item.',
                    ], 400);
                }

                // Validate refund quantity
                $alreadyRefunded = $saleItem->refunds()->sum('quantity');
                $availableToRefund = $saleItem->quantity - $alreadyRefunded;
                
                if ($itemData['quantity'] > $availableToRefund) {
                    return response()->json([
                        'message' => "Cannot refund {$itemData['quantity']} of {$saleItem->product->name}. Only {$availableToRefund} available.",
                    ], 400);
                }

                $refundAmount = $saleItem->price * $itemData['quantity'];
                $totalRefundAmount += $refundAmount;

                // Create refund record
                $saleItem->refunds()->create([
                    'sale_id' => $sale->id,
                    'quantity' => $itemData['quantity'],
                    'amount' => $refundAmount,
                    'reason' => $validated['reason'],
                    'notes' => $validated['notes'],
                    'processed_by' => auth()->id(),
                ]);

                // Return stock to inventory
                $store = $sale->store;
                $currentStock = $store->getProductStock($saleItem->product);
                $newStock = $currentStock + $itemData['quantity'];
                
                $store->products()->updateExistingPivot($saleItem->product->id, [
                    'stock_quantity' => $newStock,
                ]);
            }

            // Update sale status if fully refunded
            $totalRefunded = $sale->refunds()->sum('amount');
            if ($totalRefunded >= $sale->total_amount) {
                $sale->update(['status' => 'refunded']);
            }

            // Update customer loyalty points (deduct)
            if ($sale->customer_id) {
                $customer = $sale->customer;
                $pointsToDeduct = floor($totalRefundAmount / 10);
                if ($customer->loyalty_points >= $pointsToDeduct) {
                    $customer->redeemLoyaltyPoints($pointsToDeduct, "Refund for Sale #{$sale->sale_number}");
                }
                $customer->updateTotalSpent();
            }

            DB::commit();

            return response()->json([
                'message' => 'Refund processed successfully.',
                'data' => [
                    'refund_amount' => $totalRefundAmount,
                    'sale_status' => $sale->fresh()->status,
                ],
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            
            return response()->json([
                'message' => 'Refund processing failed.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
