<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schedule;

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote')->hourly();

// Schedule sync jobs
Schedule::command('sync:woocommerce')->everyThirtyMinutes();
Schedule::command('sync:shopify')->everyThirtyMinutes();
Schedule::command('inventory:check-low-stock')->daily();
Schedule::command('reports:generate-daily')->daily();
