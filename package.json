{"name": "syncpos", "version": "1.0.0", "description": "SaaS Point of Sale and Inventory Management System", "main": "index.js", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "watch": "vite build --watch", "hot": "vite --host", "lint": "eslint resources/js --ext .js,.vue", "lint:fix": "eslint resources/js --ext .js,.vue --fix", "test": "vitest", "test:ui": "vitest --ui", "coverage": "vitest --coverage"}, "keywords": ["pos", "inventory", "saas", "laravel", "vue", "ecommerce"], "author": "SyncPOS Team", "license": "MIT", "devDependencies": {"@vitejs/plugin-vue": "^4.5.0", "autoprefixer": "^10.4.16", "axios": "^1.6.0", "eslint": "^8.54.0", "eslint-plugin-vue": "^9.18.1", "laravel-vite-plugin": "^0.8.1", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "vite": "^4.5.0", "vitest": "^0.34.6", "@vue/test-utils": "^2.4.2", "jsdom": "^22.1.0"}, "dependencies": {"vue": "^3.3.8", "@headlessui/vue": "^1.7.16", "@heroicons/vue": "^2.0.18", "chart.js": "^4.4.0", "vue-chartjs": "^5.2.0", "vue-router": "^4.2.5", "pinia": "^2.1.7", "vue-toastification": "^2.0.0-rc.5", "date-fns": "^2.30.0", "lodash": "^4.17.21", "numeral": "^2.0.6"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}