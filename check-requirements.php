<?php
/**
 * SyncPOS System Requirements Checker
 * Run this file in your browser to check if your system meets the requirements
 */

echo "<h1>🚀 SyncPOS System Requirements Check</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .pass { color: green; font-weight: bold; }
    .fail { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    table { border-collapse: collapse; width: 100%; margin: 20px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
</style>";

$requirements = [
    'PHP Version >= 8.2' => version_compare(PHP_VERSION, '8.2.0', '>='),
    'PDO Extension' => extension_loaded('pdo'),
    'PDO MySQL Extension' => extension_loaded('pdo_mysql'),
    'OpenSSL Extension' => extension_loaded('openssl'),
    'Mbstring Extension' => extension_loaded('mbstring'),
    'Tokenizer Extension' => extension_loaded('tokenizer'),
    'XML Extension' => extension_loaded('xml'),
    'Ctype Extension' => extension_loaded('ctype'),
    'JSON Extension' => extension_loaded('json'),
    'BCMath Extension' => extension_loaded('bcmath'),
    'Fileinfo Extension' => extension_loaded('fileinfo'),
    'GD Extension' => extension_loaded('gd'),
    'Zip Extension' => extension_loaded('zip'),
];

$permissions = [
    'storage/ directory' => is_writable(__DIR__ . '/storage'),
    'bootstrap/cache/ directory' => is_writable(__DIR__ . '/bootstrap/cache'),
    '.env file writable' => is_writable(__DIR__) || is_writable(__DIR__ . '/.env'),
];

echo "<h2>📋 PHP Extensions</h2>";
echo "<table>";
echo "<tr><th>Requirement</th><th>Status</th><th>Current</th></tr>";

foreach ($requirements as $requirement => $passed) {
    $status = $passed ? '<span class="pass">✅ PASS</span>' : '<span class="fail">❌ FAIL</span>';
    $current = '';
    
    if ($requirement === 'PHP Version >= 8.2') {
        $current = PHP_VERSION;
    } elseif (strpos($requirement, 'Extension') !== false) {
        $ext = str_replace([' Extension', 'PDO '], '', $requirement);
        $ext = strtolower($ext);
        $current = extension_loaded($ext) ? 'Loaded' : 'Not Loaded';
    }
    
    echo "<tr><td>$requirement</td><td>$status</td><td>$current</td></tr>";
}

echo "</table>";

echo "<h2>📁 Directory Permissions</h2>";
echo "<table>";
echo "<tr><th>Directory/File</th><th>Status</th><th>Path</th></tr>";

foreach ($permissions as $permission => $passed) {
    $status = $passed ? '<span class="pass">✅ WRITABLE</span>' : '<span class="fail">❌ NOT WRITABLE</span>';
    $path = '';
    
    if (strpos($permission, 'storage') !== false) {
        $path = __DIR__ . '/storage';
    } elseif (strpos($permission, 'bootstrap') !== false) {
        $path = __DIR__ . '/bootstrap/cache';
    } elseif (strpos($permission, '.env') !== false) {
        $path = __DIR__ . '/.env';
    }
    
    echo "<tr><td>$permission</td><td>$status</td><td>$path</td></tr>";
}

echo "</table>";

// Check if Composer is installed
echo "<h2>🎼 Composer & Dependencies</h2>";
echo "<table>";
echo "<tr><th>Check</th><th>Status</th><th>Details</th></tr>";

$composerInstalled = file_exists(__DIR__ . '/vendor/autoload.php');
$composerStatus = $composerInstalled ? '<span class="pass">✅ INSTALLED</span>' : '<span class="fail">❌ NOT INSTALLED</span>';
echo "<tr><td>Composer Dependencies</td><td>$composerStatus</td><td>" . (__DIR__ . '/vendor/autoload.php') . "</td></tr>";

$envExists = file_exists(__DIR__ . '/.env');
$envStatus = $envExists ? '<span class="pass">✅ EXISTS</span>' : '<span class="warning">⚠️ MISSING</span>';
echo "<tr><td>.env File</td><td>$envStatus</td><td>" . (__DIR__ . '/.env') . "</td></tr>";

echo "</table>";

// Overall status
$allPassed = true;
foreach (array_merge($requirements, $permissions) as $check => $passed) {
    if (!$passed) {
        $allPassed = false;
        break;
    }
}

echo "<h2>📊 Overall Status</h2>";
if ($allPassed && $composerInstalled) {
    echo '<div style="background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px;">';
    echo '<strong>✅ System Ready!</strong> Your system meets all requirements for SyncPOS.';
    echo '</div>';
} else {
    echo '<div style="background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px;">';
    echo '<strong>❌ System Not Ready!</strong> Please fix the issues above before proceeding.';
    echo '</div>';
}

echo "<h2>🔧 Next Steps</h2>";
echo "<ol>";
echo "<li>Fix any failed requirements above</li>";
echo "<li>Run: <code>composer install</code></li>";
echo "<li>Copy <code>.env.example</code> to <code>.env</code></li>";
echo "<li>Set proper permissions: <code>chmod -R 755 storage bootstrap/cache</code></li>";
echo "<li>Visit: <code>http://yourdomain.com/install</code></li>";
echo "</ol>";

echo "<h2>🆘 Common Issues & Solutions</h2>";
echo "<ul>";
echo "<li><strong>Internal Server Error:</strong> Check error logs and ensure all extensions are installed</li>";
echo "<li><strong>Permission Denied:</strong> Run <code>sudo chown -R www-data:www-data /path/to/syncpos</code></li>";
echo "<li><strong>Composer not found:</strong> Install Composer from <a href='https://getcomposer.org'>getcomposer.org</a></li>";
echo "<li><strong>Missing extensions:</strong> Install via <code>sudo apt install php8.2-[extension]</code></li>";
echo "</ul>";

echo "<hr>";
echo "<p><small>SyncPOS Requirements Checker v1.0 | Current Time: " . date('Y-m-d H:i:s') . "</small></p>";
?>
