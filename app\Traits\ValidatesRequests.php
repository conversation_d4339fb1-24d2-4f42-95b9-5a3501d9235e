<?php

namespace App\Traits;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

trait ValidatesRequests
{
    /**
     * Validate store access for the current user.
     */
    protected function validateStoreAccess($storeId): void
    {
        $store = \App\Models\Store::findOrFail($storeId);
        
        if (!auth()->user()->hasAccessToStore($store)) {
            throw \App\Exceptions\BusinessLogicException::storeAccessDenied($store->name);
        }
    }

    /**
     * Validate product stock availability.
     */
    protected function validateProductStock($productId, $storeId, $quantity): void
    {
        $product = \App\Models\Product::findOrFail($productId);
        $store = \App\Models\Store::findOrFail($storeId);
        
        if (!$product->isInStock($store, $quantity)) {
            $available = $store->getProductStock($product);
            throw \App\Exceptions\BusinessLogicException::insufficientStock(
                $product->name,
                $available,
                $quantity
            );
        }
    }

    /**
     * Validate that a sale can be refunded.
     */
    protected function validateSaleRefund($sale): void
    {
        if ($sale->status === 'refunded') {
            throw \App\Exceptions\BusinessLogicException::invalidOperation(
                'refund sale',
                'Sale has already been fully refunded'
            );
        }

        if ($sale->payment_status !== 'paid') {
            throw \App\Exceptions\BusinessLogicException::invalidOperation(
                'refund sale',
                'Sale must be paid before it can be refunded'
            );
        }
    }

    /**
     * Validate inventory adjustment data.
     */
    protected function validateInventoryAdjustment(array $data): array
    {
        $rules = [
            'product_id' => 'required|exists:products,id',
            'store_id' => 'required|exists:stores,id',
            'adjustment_type' => 'required|in:increase,decrease',
            'quantity' => 'required|integer|min:1',
            'reason' => 'required|string|max:255',
            'notes' => 'nullable|string|max:1000',
        ];

        $validator = Validator::make($data, $rules);

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }

        return $validator->validated();
    }

    /**
     * Validate sale creation data.
     */
    protected function validateSaleData(array $data): array
    {
        $rules = [
            'store_id' => 'required|exists:stores,id',
            'customer_id' => 'nullable|exists:customers,id',
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required|exists:products,id',
            'items.*.quantity' => 'required|integer|min:1',
            'items.*.price' => 'required|numeric|min:0',
            'items.*.tax_rate' => 'nullable|numeric|min:0|max:100',
            'discount_amount' => 'nullable|numeric|min:0',
            'payment_method' => 'required|in:cash,card,mobile,bank_transfer,other',
            'payments' => 'required|array|min:1',
            'payments.*.method' => 'required|string',
            'payments.*.amount' => 'required|numeric|min:0',
            'notes' => 'nullable|string|max:1000',
        ];

        $validator = Validator::make($data, $rules);

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }

        return $validator->validated();
    }

    /**
     * Validate customer data.
     */
    protected function validateCustomerData(array $data, $customerId = null): array
    {
        $rules = [
            'name' => 'required|string|max:255',
            'email' => [
                'nullable',
                'email',
                'max:255',
                $customerId ? "unique:customers,email,{$customerId}" : 'unique:customers,email'
            ],
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:500',
            'city' => 'nullable|string|max:100',
            'state' => 'nullable|string|max:100',
            'country' => 'nullable|string|max:100',
            'postal_code' => 'nullable|string|max:20',
            'date_of_birth' => 'nullable|date|before:today',
            'customer_group_id' => 'nullable|exists:customer_groups,id',
            'notes' => 'nullable|string|max:1000',
            'is_active' => 'boolean',
        ];

        $validator = Validator::make($data, $rules);

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }

        return $validator->validated();
    }

    /**
     * Validate product data.
     */
    protected function validateProductData(array $data, $productId = null): array
    {
        $rules = [
            'name' => 'required|string|max:255',
            'sku' => [
                'required',
                'string',
                'max:100',
                $productId ? "unique:products,sku,{$productId}" : 'unique:products,sku'
            ],
            'barcode' => [
                'nullable',
                'string',
                'max:100',
                $productId ? "unique:products,barcode,{$productId}" : 'unique:products,barcode'
            ],
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0|max:999999.99',
            'cost_price' => 'required|numeric|min:0|max:999999.99',
            'category_id' => 'required|exists:categories,id',
            'brand_id' => 'nullable|exists:brands,id',
            'supplier_id' => 'nullable|exists:suppliers,id',
            'weight' => 'nullable|numeric|min:0|max:99999.99',
            'is_active' => 'boolean',
            'track_quantity' => 'boolean',
            'allow_backorder' => 'boolean',
        ];

        $validator = Validator::make($data, $rules);

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }

        return $validator->validated();
    }

    /**
     * Validate pagination parameters.
     */
    protected function validatePaginationParams(Request $request): array
    {
        return $request->validate([
            'page' => 'nullable|integer|min:1',
            'per_page' => 'nullable|integer|min:1|max:100',
            'sort_by' => 'nullable|string|max:50',
            'sort_order' => 'nullable|in:asc,desc',
            'search' => 'nullable|string|max:255',
        ]);
    }

    /**
     * Validate date range parameters.
     */
    protected function validateDateRange(Request $request): array
    {
        return $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
        ]);
    }
}
