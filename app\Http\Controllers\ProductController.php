<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\Category;
use App\Models\Brand;
use App\Models\Store;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Facades\Excel;
use App\Imports\ProductImport;
use App\Exports\ProductExport;

class ProductController extends Controller
{
    public function index(Request $request)
    {
        $query = Product::with(['category', 'brand', 'stores']);

        // Search
        if ($request->filled('search')) {
            $query->search($request->search);
        }

        // Filter by category
        if ($request->filled('category_id')) {
            $query->where('category_id', $request->category_id);
        }

        // Filter by brand
        if ($request->filled('brand_id')) {
            $query->where('brand_id', $request->brand_id);
        }

        // Filter by status
        if ($request->filled('status')) {
            if ($request->status === 'active') {
                $query->active();
            } elseif ($request->status === 'inactive') {
                $query->where('is_active', false);
            }
        }

        // Filter by stock status
        if ($request->filled('stock_status')) {
            $store = Store::find($request->store_id ?? auth()->user()->primaryStore()?->id);
            if ($store && $request->stock_status === 'low') {
                $query->lowStock($store);
            }
        }

        $products = $query->paginate(20);
        $categories = Category::active()->get();
        $brands = Brand::active()->get();
        $stores = Store::active()->get();

        return view('products.index', compact('products', 'categories', 'brands', 'stores'));
    }

    public function create()
    {
        $categories = Category::active()->get();
        $brands = Brand::active()->get();
        $stores = Store::active()->get();

        return view('products.create', compact('categories', 'brands', 'stores'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'sku' => 'required|string|unique:products,sku',
            'barcode' => 'nullable|string|unique:products,barcode',
            'price' => 'required|numeric|min:0',
            'cost_price' => 'nullable|numeric|min:0',
            'category_id' => 'nullable|exists:categories,id',
            'brand_id' => 'nullable|exists:brands,id',
            'unit' => 'required|string|max:50',
            'weight' => 'nullable|numeric|min:0',
            'tax_rate' => 'nullable|numeric|min:0|max:100',
            'is_active' => 'boolean',
            'is_trackable' => 'boolean',
            'image' => 'nullable|image|max:2048',
            'stores' => 'required|array',
            'stores.*.store_id' => 'required|exists:stores,id',
            'stores.*.stock_quantity' => 'required|integer|min:0',
            'stores.*.min_stock_level' => 'nullable|integer|min:0',
            'stores.*.max_stock_level' => 'nullable|integer|min:0',
        ]);

        // Handle image upload
        if ($request->hasFile('image')) {
            $validated['image'] = $request->file('image')->store('products', 'public');
        }

        $product = Product::create($validated);

        // Attach to stores with stock information
        foreach ($validated['stores'] as $storeData) {
            $product->stores()->attach($storeData['store_id'], [
                'stock_quantity' => $storeData['stock_quantity'],
                'min_stock_level' => $storeData['min_stock_level'] ?? 0,
                'max_stock_level' => $storeData['max_stock_level'] ?? 0,
            ]);
        }

        return redirect()->route('products.index')
            ->with('success', 'Product created successfully.');
    }

    public function show(Product $product)
    {
        $product->load(['category', 'brand', 'stores', 'saleItems.sale', 'inventoryAdjustments']);
        return view('products.show', compact('product'));
    }

    public function edit(Product $product)
    {
        $categories = Category::active()->get();
        $brands = Brand::active()->get();
        $stores = Store::active()->get();
        $product->load('stores');

        return view('products.edit', compact('product', 'categories', 'brands', 'stores'));
    }

    public function update(Request $request, Product $product)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'sku' => 'required|string|unique:products,sku,' . $product->id,
            'barcode' => 'nullable|string|unique:products,barcode,' . $product->id,
            'price' => 'required|numeric|min:0',
            'cost_price' => 'nullable|numeric|min:0',
            'category_id' => 'nullable|exists:categories,id',
            'brand_id' => 'nullable|exists:brands,id',
            'unit' => 'required|string|max:50',
            'weight' => 'nullable|numeric|min:0',
            'tax_rate' => 'nullable|numeric|min:0|max:100',
            'is_active' => 'boolean',
            'is_trackable' => 'boolean',
            'image' => 'nullable|image|max:2048',
            'stores' => 'required|array',
            'stores.*.store_id' => 'required|exists:stores,id',
            'stores.*.stock_quantity' => 'required|integer|min:0',
            'stores.*.min_stock_level' => 'nullable|integer|min:0',
            'stores.*.max_stock_level' => 'nullable|integer|min:0',
        ]);

        // Handle image upload
        if ($request->hasFile('image')) {
            $validated['image'] = $request->file('image')->store('products', 'public');
        }

        $product->update($validated);

        // Sync stores with stock information
        $storeData = [];
        foreach ($validated['stores'] as $store) {
            $storeData[$store['store_id']] = [
                'stock_quantity' => $store['stock_quantity'],
                'min_stock_level' => $store['min_stock_level'] ?? 0,
                'max_stock_level' => $store['max_stock_level'] ?? 0,
            ];
        }
        $product->stores()->sync($storeData);

        return redirect()->route('products.show', $product)
            ->with('success', 'Product updated successfully.');
    }

    public function destroy(Product $product)
    {
        $product->delete();

        return redirect()->route('products.index')
            ->with('success', 'Product deleted successfully.');
    }

    public function import(Request $request)
    {
        $request->validate([
            'file' => 'required|mimes:xlsx,csv',
        ]);

        Excel::import(new ProductImport, $request->file('file'));

        return redirect()->route('products.index')
            ->with('success', 'Products imported successfully.');
    }

    public function export(Request $request)
    {
        return Excel::download(new ProductExport, 'products.xlsx');
    }
}
