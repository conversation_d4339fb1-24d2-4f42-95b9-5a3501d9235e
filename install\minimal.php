<?php
// Ultra-minimal installer for troubleshooting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html><html><head><title>SyncPOS Minimal Installer</title>";
echo "<style>body{font-family:Arial;margin:40px;} .btn{background:#007cba;color:white;padding:10px 20px;text-decoration:none;border-radius:5px;} .error{color:red;background:#fee;padding:10px;margin:10px 0;} .success{color:green;background:#efe;padding:10px;margin:10px 0;}</style></head><body>";

echo "<h1>SyncPOS Minimal Installer</h1>";

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Process installation
    $dbHost = $_POST['db_host'] ?? 'localhost';
    $dbName = $_POST['db_name'] ?? '';
    $dbUser = $_POST['db_user'] ?? 'root';
    $dbPass = $_POST['db_pass'] ?? '';
    $adminEmail = $_POST['admin_email'] ?? '';
    $adminPassword = $_POST['admin_password'] ?? '';

    try {
        // Test database connection
        $pdo = new PDO("mysql:host={$dbHost};charset=utf8mb4", $dbUser, $dbPass);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        // Create database
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$dbName}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        $pdo->exec("USE `{$dbName}`");

        // Create basic table
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS `users` (
                `id` int AUTO_INCREMENT PRIMARY KEY,
                `name` varchar(255) NOT NULL,
                `email` varchar(255) UNIQUE NOT NULL,
                `password` varchar(255) NOT NULL,
                `created_at` timestamp DEFAULT CURRENT_TIMESTAMP
            )
        ");

        // Create admin user
        $hashedPassword = password_hash($adminPassword, PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("INSERT INTO users (name, email, password) VALUES (?, ?, ?)");
        $stmt->execute(['Administrator', $adminEmail, $hashedPassword]);

        // Create .env file
        $appKey = 'base64:' . base64_encode(random_bytes(32));
        $envContent = "APP_NAME=SyncPOS
APP_ENV=production
APP_KEY={$appKey}
APP_DEBUG=false
APP_URL=" . (isset($_SERVER['HTTPS']) ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI'], 2) . "

DB_CONNECTION=mysql
DB_HOST={$dbHost}
DB_DATABASE={$dbName}
DB_USERNAME={$dbUser}
DB_PASSWORD={$dbPass}
";

        file_put_contents('../.env', $envContent);

        // Create directories
        @mkdir('../storage', 0755, true);
        @mkdir('../storage/logs', 0755, true);
        @mkdir('../bootstrap/cache', 0755, true);

        // Create installed flag
        file_put_contents('../storage/installed', date('Y-m-d H:i:s'));

        echo "<div class='success'><h2>✅ Installation Successful!</h2>";
        echo "<p>SyncPOS has been installed with basic functionality.</p>";
        echo "<p><strong>Admin Email:</strong> {$adminEmail}</p>";
        echo "<p><strong>Next Steps:</strong></p>";
        echo "<ol><li>Delete the /install folder</li><li>Access your application</li></ol>";
        echo "<p><a href='../public' class='btn'>Access SyncPOS</a></p></div>";

    } catch (Exception $e) {
        echo "<div class='error'><h3>Installation Failed</h3>";
        echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p></div>";
        showForm();
    }
} else {
    showForm();
}

function showForm() {
    echo "<form method='POST' style='max-width:500px;'>";
    echo "<h3>Database Configuration</h3>";
    echo "<p><label>Host:</label><br><input type='text' name='db_host' value='localhost' style='width:100%;padding:8px;margin:5px 0;'></p>";
    echo "<p><label>Database Name:</label><br><input type='text' name='db_name' placeholder='syncpos_db' required style='width:100%;padding:8px;margin:5px 0;'></p>";
    echo "<p><label>Username:</label><br><input type='text' name='db_user' value='root' required style='width:100%;padding:8px;margin:5px 0;'></p>";
    echo "<p><label>Password:</label><br><input type='password' name='db_pass' style='width:100%;padding:8px;margin:5px 0;'></p>";
    
    echo "<h3>Admin Account</h3>";
    echo "<p><label>Admin Email:</label><br><input type='email' name='admin_email' required style='width:100%;padding:8px;margin:5px 0;'></p>";
    echo "<p><label>Admin Password:</label><br><input type='password' name='admin_password' required minlength='6' style='width:100%;padding:8px;margin:5px 0;'></p>";
    
    echo "<p><button type='submit' class='btn' style='border:none;cursor:pointer;'>Install SyncPOS</button></p>";
    echo "</form>";
}

echo "</body></html>";
?>
