<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class StockTransfer extends Model
{
    use HasFactory;

    protected $fillable = [
        'transfer_number',
        'from_store_id',
        'to_store_id',
        'status',
        'notes',
        'transferred_by',
        'received_by',
        'transferred_at',
        'received_at',
    ];

    protected $casts = [
        'transferred_at' => 'datetime',
        'received_at' => 'datetime',
    ];

    /**
     * Get the source store.
     */
    public function fromStore()
    {
        return $this->belongsTo(Store::class, 'from_store_id');
    }

    /**
     * Get the destination store.
     */
    public function toStore()
    {
        return $this->belongsTo(Store::class, 'to_store_id');
    }

    /**
     * Get the user who initiated the transfer.
     */
    public function transferredBy()
    {
        return $this->belongsTo(User::class, 'transferred_by');
    }

    /**
     * Get the user who received the transfer.
     */
    public function receivedBy()
    {
        return $this->belongsTo(User::class, 'received_by');
    }

    /**
     * Get the transfer items.
     */
    public function items()
    {
        return $this->hasMany(StockTransferItem::class);
    }

    /**
     * Generate a unique transfer number.
     */
    public static function generateTransferNumber(): string
    {
        $prefix = 'TRF-';
        $date = now()->format('Ymd');
        $lastTransfer = static::whereDate('created_at', today())
            ->orderBy('id', 'desc')
            ->first();

        $sequence = $lastTransfer ? (int) substr($lastTransfer->transfer_number, -4) + 1 : 1;

        return $prefix . $date . '-' . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Calculate total value of the transfer.
     */
    public function getTotalValueAttribute(): float
    {
        return $this->items()->sum(\DB::raw('quantity * cost_price'));
    }

    /**
     * Check if transfer is pending.
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Check if transfer is in transit.
     */
    public function isInTransit(): bool
    {
        return $this->status === 'in_transit';
    }

    /**
     * Check if transfer is completed.
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Mark transfer as sent.
     */
    public function markAsSent(): void
    {
        $this->update([
            'status' => 'in_transit',
            'transferred_at' => now(),
            'transferred_by' => auth()->id(),
        ]);
    }

    /**
     * Mark transfer as received.
     */
    public function markAsReceived(): void
    {
        $this->update([
            'status' => 'completed',
            'received_at' => now(),
            'received_by' => auth()->id(),
        ]);
    }
}
