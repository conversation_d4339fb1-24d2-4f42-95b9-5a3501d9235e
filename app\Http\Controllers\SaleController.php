<?php

namespace App\Http\Controllers;

use App\Models\Sale;
use App\Models\SaleItem;
use App\Models\Store;
use App\Models\Customer;
use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Barryvdh\DomPDF\Facade\Pdf;

class SaleController extends Controller
{
    public function index(Request $request)
    {
        $query = Sale::with(['customer', 'cashier', 'store']);

        // Filter by store
        $storeId = $request->store_id ?? auth()->user()->primaryStore()?->id;
        if ($storeId) {
            $query->where('store_id', $storeId);
        }

        // Search by sale number or customer
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('sale_number', 'like', "%{$search}%")
                  ->orWhereHas('customer', function ($customerQuery) use ($search) {
                      $customerQuery->where('name', 'like', "%{$search}%")
                                   ->orWhere('email', 'like', "%{$search}%");
                  });
            });
        }

        // Filter by payment status
        if ($request->filled('payment_status')) {
            $query->where('payment_status', $request->payment_status);
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by date range
        if ($request->filled('start_date')) {
            $query->whereDate('created_at', '>=', $request->start_date);
        }
        if ($request->filled('end_date')) {
            $query->whereDate('created_at', '<=', $request->end_date);
        }

        $sales = $query->latest()->paginate(20);
        $stores = Store::active()->get();

        return view('sales.index', compact('sales', 'stores'));
    }

    public function show(Sale $sale)
    {
        $sale->load(['items.product', 'customer', 'cashier', 'store', 'refunds']);
        
        return view('sales.show', compact('sale'));
    }

    public function refund(Request $request, Sale $sale)
    {
        $validated = $request->validate([
            'items' => 'required|array',
            'items.*.sale_item_id' => 'required|exists:sale_items,id',
            'items.*.quantity' => 'required|integer|min:1',
            'reason' => 'required|string|max:255',
            'notes' => 'nullable|string',
        ]);

        if ($sale->status === 'refunded') {
            return redirect()->back()->with('error', 'Sale has already been refunded.');
        }

        try {
            DB::beginTransaction();

            $totalRefundAmount = 0;
            $refundItems = [];

            foreach ($validated['items'] as $itemData) {
                $saleItem = SaleItem::findOrFail($itemData['sale_item_id']);
                
                // Validate the sale item belongs to this sale
                if ($saleItem->sale_id !== $sale->id) {
                    throw new \Exception('Invalid sale item.');
                }

                // Validate refund quantity
                $alreadyRefunded = $saleItem->refunds()->sum('quantity');
                $availableToRefund = $saleItem->quantity - $alreadyRefunded;
                
                if ($itemData['quantity'] > $availableToRefund) {
                    throw new \Exception("Cannot refund {$itemData['quantity']} of {$saleItem->product->name}. Only {$availableToRefund} available.");
                }

                $refundAmount = $saleItem->price * $itemData['quantity'];
                $totalRefundAmount += $refundAmount;

                // Create refund record
                $refund = $saleItem->refunds()->create([
                    'sale_id' => $sale->id,
                    'quantity' => $itemData['quantity'],
                    'amount' => $refundAmount,
                    'reason' => $validated['reason'],
                    'notes' => $validated['notes'],
                    'processed_by' => auth()->id(),
                ]);

                // Return stock to inventory
                $store = $sale->store;
                $currentStock = $store->getProductStock($saleItem->product);
                $newStock = $currentStock + $itemData['quantity'];
                
                $store->products()->updateExistingPivot($saleItem->product->id, [
                    'stock_quantity' => $newStock,
                ]);

                $refundItems[] = [
                    'product_name' => $saleItem->product->name,
                    'quantity' => $itemData['quantity'],
                    'amount' => $refundAmount,
                ];
            }

            // Update sale status if fully refunded
            $totalRefunded = $sale->refunds()->sum('amount');
            if ($totalRefunded >= $sale->total_amount) {
                $sale->update(['status' => 'refunded']);
            }

            // Update customer loyalty points (deduct)
            if ($sale->customer_id) {
                $customer = $sale->customer;
                $pointsToDeduct = floor($totalRefundAmount / 10);
                if ($customer->loyalty_points >= $pointsToDeduct) {
                    $customer->redeemLoyaltyPoints($pointsToDeduct, "Refund for Sale #{$sale->sale_number}");
                }
                $customer->updateTotalSpent();
            }

            DB::commit();

            return redirect()->route('sales.show', $sale)
                ->with('success', "Refund processed successfully. Total refunded: $" . number_format($totalRefundAmount, 2));

        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()
                ->with('error', 'Refund failed: ' . $e->getMessage());
        }
    }

    public function receipt(Sale $sale)
    {
        $sale->load(['items.product', 'customer', 'store']);
        
        return view('sales.receipt', compact('sale'));
    }

    public function printReceipt(Sale $sale)
    {
        $sale->load(['items.product', 'customer', 'store']);
        
        $pdf = Pdf::loadView('sales.receipt-pdf', compact('sale'));
        $pdf->setPaper([0, 0, 226.77, 841.89], 'portrait'); // 80mm width thermal paper
        
        return $pdf->stream("receipt-{$sale->sale_number}.pdf");
    }

    public function duplicate(Sale $sale)
    {
        $sale->load(['items.product']);
        
        // Add items to POS cart
        $cart = [];
        foreach ($sale->items as $item) {
            $cart[] = [
                'product_id' => $item->product_id,
                'name' => $item->product->name,
                'price' => $item->price,
                'cost_price' => $item->cost_price,
                'tax_rate' => $item->tax_rate,
                'quantity' => $item->quantity,
            ];
        }
        
        session(['pos_cart' => $cart]);
        
        return redirect()->route('pos.index')
            ->with('success', 'Sale items added to cart.');
    }
}
