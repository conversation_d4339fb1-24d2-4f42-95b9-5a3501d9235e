<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Supplier extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'company_name',
        'email',
        'phone',
        'address',
        'city',
        'state',
        'country',
        'postal_code',
        'tax_number',
        'payment_terms',
        'credit_limit',
        'is_active',
        'notes',
    ];

    protected $casts = [
        'credit_limit' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    /**
     * Get the purchase orders for this supplier.
     */
    public function purchaseOrders()
    {
        return $this->hasMany(PurchaseOrder::class);
    }

    /**
     * Get the products supplied by this supplier.
     */
    public function products()
    {
        return $this->belongsToMany(Product::class, 'supplier_products')
            ->withPivot('supplier_sku', 'cost_price', 'lead_time_days')
            ->withTimestamps();
    }

    /**
     * Get total amount of pending orders.
     */
    public function getPendingOrdersAmountAttribute(): float
    {
        return $this->purchaseOrders()
            ->whereIn('status', ['draft', 'sent', 'confirmed'])
            ->sum('total_amount');
    }

    /**
     * Get total amount paid to supplier.
     */
    public function getTotalPaidAttribute(): float
    {
        return $this->purchaseOrders()
            ->where('payment_status', 'paid')
            ->sum('total_amount');
    }

    /**
     * Scope for active suppliers.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Search suppliers.
     */
    public function scopeSearch($query, string $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('name', 'like', "%{$search}%")
              ->orWhere('company_name', 'like', "%{$search}%")
              ->orWhere('email', 'like', "%{$search}%")
              ->orWhere('phone', 'like', "%{$search}%");
        });
    }
}
