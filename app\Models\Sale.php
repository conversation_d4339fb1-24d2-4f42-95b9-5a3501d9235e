<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Sale extends Model
{
    use HasFactory;

    protected $fillable = [
        'sale_number',
        'store_id',
        'customer_id',
        'cashier_id',
        'subtotal',
        'tax_amount',
        'discount_amount',
        'total_amount',
        'payment_method',
        'payment_status',
        'status',
        'notes',
        'receipt_data',
    ];

    protected $casts = [
        'subtotal' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'receipt_data' => 'array',
    ];

    /**
     * Get the store where the sale was made.
     */
    public function store()
    {
        return $this->belongsTo(Store::class);
    }

    /**
     * Get the customer who made the purchase.
     */
    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Get the cashier who processed the sale.
     */
    public function cashier()
    {
        return $this->belongsTo(User::class, 'cashier_id');
    }

    /**
     * Get the sale items.
     */
    public function items()
    {
        return $this->hasMany(SaleItem::class);
    }

    /**
     * Get the payments for this sale.
     */
    public function payments()
    {
        return $this->hasMany(SalePayment::class);
    }

    /**
     * Get the refunds for this sale.
     */
    public function refunds()
    {
        return $this->hasMany(SaleRefund::class);
    }

    /**
     * Generate a unique sale number.
     */
    public static function generateSaleNumber(): string
    {
        $prefix = 'SALE-';
        $date = now()->format('Ymd');
        $lastSale = static::whereDate('created_at', today())
            ->orderBy('id', 'desc')
            ->first();

        $sequence = $lastSale ? (int) substr($lastSale->sale_number, -4) + 1 : 1;

        return $prefix . $date . '-' . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Calculate totals based on items.
     */
    public function calculateTotals(): void
    {
        $subtotal = $this->items()->sum(\DB::raw('quantity * price'));
        $taxAmount = $this->items()->sum(\DB::raw('quantity * price * (tax_rate / 100)'));
        $total = $subtotal + $taxAmount - $this->discount_amount;

        $this->update([
            'subtotal' => $subtotal,
            'tax_amount' => $taxAmount,
            'total_amount' => $total,
        ]);
    }

    /**
     * Check if sale is paid.
     */
    public function isPaid(): bool
    {
        return $this->payment_status === 'paid';
    }

    /**
     * Check if sale is refunded.
     */
    public function isRefunded(): bool
    {
        return $this->status === 'refunded';
    }

    /**
     * Get the total refunded amount.
     */
    public function getTotalRefundedAttribute(): float
    {
        return $this->refunds()->sum('amount');
    }

    /**
     * Get the remaining refundable amount.
     */
    public function getRefundableAmountAttribute(): float
    {
        return $this->total_amount - $this->total_refunded;
    }

    /**
     * Process refund.
     */
    public function processRefund(float $amount, string $reason = null): SaleRefund
    {
        if ($amount > $this->refundable_amount) {
            throw new \Exception('Refund amount cannot exceed refundable amount.');
        }

        $refund = $this->refunds()->create([
            'amount' => $amount,
            'reason' => $reason,
            'processed_by' => auth()->id(),
        ]);

        // Update sale status if fully refunded
        if ($this->total_refunded >= $this->total_amount) {
            $this->update(['status' => 'refunded']);
        }

        return $refund;
    }

    /**
     * Scope for today's sales.
     */
    public function scopeToday($query)
    {
        return $query->whereDate('created_at', today());
    }

    /**
     * Scope for sales in date range.
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * Scope for paid sales.
     */
    public function scopePaid($query)
    {
        return $query->where('payment_status', 'paid');
    }
}
