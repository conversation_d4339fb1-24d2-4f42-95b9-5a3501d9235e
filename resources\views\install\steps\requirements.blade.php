@php
    $requirements = [
        'PHP Version >= 8.2' => version_compare(PHP_VERSION, '8.2.0', '>='),
        'PDO Extension' => extension_loaded('pdo'),
        'PDO MySQL Extension' => extension_loaded('pdo_mysql'),
        'OpenSSL Extension' => extension_loaded('openssl'),
        'Mbstring Extension' => extension_loaded('mbstring'),
        'Tokenizer Extension' => extension_loaded('tokenizer'),
        'XML Extension' => extension_loaded('xml'),
        'Ctype Extension' => extension_loaded('ctype'),
        'JSON Extension' => extension_loaded('json'),
        'BCMath Extension' => extension_loaded('bcmath'),
        'Fileinfo Extension' => extension_loaded('fileinfo'),
        'GD Extension' => extension_loaded('gd'),
        'Zip Extension' => extension_loaded('zip'),
    ];

    $permissions = [
        'storage/ directory' => is_writable(storage_path()),
        'bootstrap/cache/ directory' => is_writable(base_path('bootstrap/cache')),
        '.env file' => is_writable(base_path()) || is_writable(base_path('.env')),
    ];

    $allPassed = true;
    foreach (array_merge($requirements, $permissions) as $requirement => $passed) {
        $allPassed = $allPassed && $passed;
    }
@endphp

<div>
    <h3 class="text-lg font-medium text-gray-900 mb-4">System Requirements</h3>
    
    <div class="space-y-4">
        <!-- PHP Extensions -->
        <div>
            <h4 class="text-sm font-medium text-gray-700 mb-2">PHP Extensions</h4>
            <div class="space-y-2">
                @foreach ($requirements as $requirement => $passed)
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">{{ $requirement }}</span>
                        <span class="flex items-center">
                            @if ($passed)
                                <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            @else
                                <svg class="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            @endif
                        </span>
                    </div>
                @endforeach
            </div>
        </div>

        <!-- Directory Permissions -->
        <div>
            <h4 class="text-sm font-medium text-gray-700 mb-2">Directory Permissions</h4>
            <div class="space-y-2">
                @foreach ($permissions as $permission => $passed)
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">{{ $permission }}</span>
                        <span class="flex items-center">
                            @if ($passed)
                                <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            @else
                                <svg class="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            @endif
                        </span>
                    </div>
                @endforeach
            </div>
        </div>
    </div>

    @if ($allPassed)
        <div class="mt-6 bg-green-50 border border-green-200 rounded-md p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm text-green-800">
                        Great! All system requirements are met. You can proceed to the next step.
                    </p>
                </div>
            </div>
        </div>

        <form method="POST" action="{{ route('install.process') }}" class="mt-6">
            @csrf
            <input type="hidden" name="step" value="1">
            <button type="submit" class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                Continue to Database Setup
            </button>
        </form>
    @else
        <div class="mt-6 bg-red-50 border border-red-200 rounded-md p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm text-red-800">
                        Some system requirements are not met. Please fix the issues above before continuing.
                    </p>
                </div>
            </div>
        </div>

        <button type="button" onclick="window.location.reload()" class="mt-6 w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
            Check Again
        </button>
    @endif
</div>
