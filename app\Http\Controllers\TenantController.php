<?php

namespace App\Http\Controllers;

use App\Models\Tenant;
use App\Models\Domain;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class TenantController extends Controller
{
    public function index()
    {
        $tenants = Tenant::with('domains')->paginate(20);
        return view('admin.tenants.index', compact('tenants'));
    }

    public function create()
    {
        return view('admin.tenants.create');
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'company_name' => 'required|string|max:255',
            'company_email' => 'required|email|unique:tenants,company_email',
            'company_phone' => 'nullable|string|max:20',
            'company_address' => 'nullable|string',
            'company_city' => 'nullable|string|max:100',
            'company_state' => 'nullable|string|max:100',
            'company_country' => 'nullable|string|max:100',
            'company_postal_code' => 'nullable|string|max:20',
            'subscription_plan' => 'required|in:basic,professional,enterprise',
            'domain' => 'required|string|unique:domains,domain',
        ]);

        // Create tenant
        $tenant = Tenant::create([
            'id' => Str::uuid(),
            'company_name' => $validated['company_name'],
            'company_email' => $validated['company_email'],
            'company_phone' => $validated['company_phone'],
            'company_address' => $validated['company_address'],
            'company_city' => $validated['company_city'],
            'company_state' => $validated['company_state'],
            'company_country' => $validated['company_country'],
            'company_postal_code' => $validated['company_postal_code'],
            'subscription_plan' => $validated['subscription_plan'],
            'trial_ends_at' => now()->addDays(14), // 14-day trial
            'is_active' => true,
        ]);

        // Create domain
        $tenant->domains()->create([
            'domain' => $validated['domain'],
            'is_primary' => true,
        ]);

        return redirect()->route('admin.tenants.index')
            ->with('success', 'Tenant created successfully.');
    }

    public function show(Tenant $tenant)
    {
        $tenant->load('domains');
        return view('admin.tenants.show', compact('tenant'));
    }

    public function edit(Tenant $tenant)
    {
        return view('admin.tenants.edit', compact('tenant'));
    }

    public function update(Request $request, Tenant $tenant)
    {
        $validated = $request->validate([
            'company_name' => 'required|string|max:255',
            'company_email' => ['required', 'email', Rule::unique('tenants')->ignore($tenant->id)],
            'company_phone' => 'nullable|string|max:20',
            'company_address' => 'nullable|string',
            'company_city' => 'nullable|string|max:100',
            'company_state' => 'nullable|string|max:100',
            'company_country' => 'nullable|string|max:100',
            'company_postal_code' => 'nullable|string|max:20',
            'subscription_plan' => 'required|in:basic,professional,enterprise',
            'is_active' => 'boolean',
        ]);

        $tenant->update($validated);

        return redirect()->route('admin.tenants.show', $tenant)
            ->with('success', 'Tenant updated successfully.');
    }

    public function destroy(Tenant $tenant)
    {
        $tenant->delete();

        return redirect()->route('admin.tenants.index')
            ->with('success', 'Tenant deleted successfully.');
    }

    public function suspend(Tenant $tenant)
    {
        $tenant->update(['is_active' => false]);

        return redirect()->back()
            ->with('success', 'Tenant suspended successfully.');
    }

    public function activate(Tenant $tenant)
    {
        $tenant->update(['is_active' => true]);

        return redirect()->back()
            ->with('success', 'Tenant activated successfully.');
    }
}
