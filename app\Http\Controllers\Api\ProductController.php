<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\Store;
use Illuminate\Http\Request;

class ProductController extends Controller
{
    public function index(Request $request)
    {
        $query = Product::with(['category', 'brand', 'stores']);

        // Search
        if ($request->filled('search')) {
            $query->search($request->search);
        }

        // Filter by category
        if ($request->filled('category_id')) {
            $query->where('category_id', $request->category_id);
        }

        // Filter by brand
        if ($request->filled('brand_id')) {
            $query->where('brand_id', $request->brand_id);
        }

        // Filter by store
        if ($request->filled('store_id')) {
            $query->whereHas('stores', function ($q) use ($request) {
                $q->where('store_id', $request->store_id);
            });
        }

        // Only active products
        $query->active();

        $products = $query->paginate($request->per_page ?? 20);

        return response()->json([
            'data' => $products->items(),
            'meta' => [
                'current_page' => $products->currentPage(),
                'last_page' => $products->lastPage(),
                'per_page' => $products->perPage(),
                'total' => $products->total(),
            ],
        ]);
    }

    public function show(Product $product)
    {
        $product->load(['category', 'brand', 'stores']);
        
        return response()->json([
            'data' => $product,
        ]);
    }

    public function search(Request $request, string $query)
    {
        $products = Product::search($query)
            ->active()
            ->with(['category', 'brand', 'stores'])
            ->limit(10)
            ->get();

        // Add stock information for current store
        if ($request->filled('store_id')) {
            $store = Store::find($request->store_id);
            if ($store) {
                $products->each(function ($product) use ($store) {
                    $product->current_stock = $product->getStockForStore($store);
                    $product->in_stock = $product->isInStock($store);
                });
            }
        }

        return response()->json([
            'data' => $products,
        ]);
    }

    public function findByBarcode(Request $request, string $barcode)
    {
        $product = Product::where('barcode', $barcode)
            ->active()
            ->with(['category', 'brand', 'stores'])
            ->first();

        if (!$product) {
            return response()->json([
                'message' => 'Product not found',
            ], 404);
        }

        // Add stock information for current store
        if ($request->filled('store_id')) {
            $store = Store::find($request->store_id);
            if ($store) {
                $product->current_stock = $product->getStockForStore($store);
                $product->in_stock = $product->isInStock($store);
            }
        }

        return response()->json([
            'data' => $product,
        ]);
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'sku' => 'required|string|unique:products,sku',
            'barcode' => 'nullable|string|unique:products,barcode',
            'price' => 'required|numeric|min:0',
            'cost_price' => 'nullable|numeric|min:0',
            'category_id' => 'nullable|exists:categories,id',
            'brand_id' => 'nullable|exists:brands,id',
            'unit' => 'required|string|max:50',
            'weight' => 'nullable|numeric|min:0',
            'tax_rate' => 'nullable|numeric|min:0|max:100',
            'is_active' => 'boolean',
            'is_trackable' => 'boolean',
        ]);

        $product = Product::create($validated);

        return response()->json([
            'data' => $product,
            'message' => 'Product created successfully',
        ], 201);
    }

    public function update(Request $request, Product $product)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'sku' => 'required|string|unique:products,sku,' . $product->id,
            'barcode' => 'nullable|string|unique:products,barcode,' . $product->id,
            'price' => 'required|numeric|min:0',
            'cost_price' => 'nullable|numeric|min:0',
            'category_id' => 'nullable|exists:categories,id',
            'brand_id' => 'nullable|exists:brands,id',
            'unit' => 'required|string|max:50',
            'weight' => 'nullable|numeric|min:0',
            'tax_rate' => 'nullable|numeric|min:0|max:100',
            'is_active' => 'boolean',
            'is_trackable' => 'boolean',
        ]);

        $product->update($validated);

        return response()->json([
            'data' => $product,
            'message' => 'Product updated successfully',
        ]);
    }

    public function destroy(Product $product)
    {
        $product->delete();

        return response()->json([
            'message' => 'Product deleted successfully',
        ]);
    }
}
