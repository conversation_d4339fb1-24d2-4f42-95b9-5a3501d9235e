<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Customer extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'email',
        'phone',
        'address',
        'city',
        'state',
        'country',
        'postal_code',
        'date_of_birth',
        'customer_group_id',
        'loyalty_points',
        'total_spent',
        'notes',
        'is_active',
    ];

    protected $casts = [
        'date_of_birth' => 'date',
        'loyalty_points' => 'integer',
        'total_spent' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    /**
     * Get the customer group.
     */
    public function customerGroup()
    {
        return $this->belongsTo(CustomerGroup::class);
    }

    /**
     * Get the sales for this customer.
     */
    public function sales()
    {
        return $this->hasMany(Sale::class);
    }

    /**
     * Get the loyalty transactions.
     */
    public function loyaltyTransactions()
    {
        return $this->hasMany(LoyaltyTransaction::class);
    }

    /**
     * Add loyalty points.
     */
    public function addLoyaltyPoints(int $points, string $reason = null): void
    {
        $this->increment('loyalty_points', $points);

        $this->loyaltyTransactions()->create([
            'type' => 'earned',
            'points' => $points,
            'reason' => $reason ?? 'Purchase reward',
        ]);
    }

    /**
     * Redeem loyalty points.
     */
    public function redeemLoyaltyPoints(int $points, string $reason = null): bool
    {
        if ($this->loyalty_points < $points) {
            return false;
        }

        $this->decrement('loyalty_points', $points);

        $this->loyaltyTransactions()->create([
            'type' => 'redeemed',
            'points' => $points,
            'reason' => $reason ?? 'Points redemption',
        ]);

        return true;
    }

    /**
     * Update total spent.
     */
    public function updateTotalSpent(): void
    {
        $totalSpent = $this->sales()->where('payment_status', 'paid')->sum('total_amount');
        $this->update(['total_spent' => $totalSpent]);
    }

    /**
     * Get customer tier based on total spent.
     */
    public function getTierAttribute(): string
    {
        if ($this->total_spent >= 10000) {
            return 'VIP';
        } elseif ($this->total_spent >= 5000) {
            return 'Gold';
        } elseif ($this->total_spent >= 1000) {
            return 'Silver';
        }

        return 'Bronze';
    }

    /**
     * Scope for active customers.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Search customers.
     */
    public function scopeSearch($query, string $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('name', 'like', "%{$search}%")
              ->orWhere('email', 'like', "%{$search}%")
              ->orWhere('phone', 'like', "%{$search}%");
        });
    }
}
