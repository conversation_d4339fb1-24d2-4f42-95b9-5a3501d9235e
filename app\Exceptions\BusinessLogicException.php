<?php

namespace App\Exceptions;

use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class BusinessLogicException extends Exception
{
    protected $statusCode;
    protected $errorCode;

    public function __construct(
        string $message = 'Business logic error occurred',
        int $statusCode = 400,
        string $errorCode = 'BUSINESS_LOGIC_ERROR',
        ?\Throwable $previous = null
    ) {
        parent::__construct($message, 0, $previous);
        $this->statusCode = $statusCode;
        $this->errorCode = $errorCode;
    }

    /**
     * Render the exception as an HTTP response.
     */
    public function render(Request $request): JsonResponse
    {
        if ($request->expectsJson()) {
            return response()->json([
                'message' => $this->getMessage(),
                'error_code' => $this->errorCode,
            ], $this->statusCode);
        }

        return response()->json([
            'message' => $this->getMessage(),
        ], $this->statusCode);
    }

    /**
     * Create an insufficient stock exception.
     */
    public static function insufficientStock(string $productName, int $available, int $requested): self
    {
        return new self(
            "Insufficient stock for {$productName}. Available: {$available}, Requested: {$requested}",
            400,
            'INSUFFICIENT_STOCK'
        );
    }

    /**
     * Create an invalid operation exception.
     */
    public static function invalidOperation(string $operation, string $reason = ''): self
    {
        $message = "Invalid operation: {$operation}";
        if ($reason) {
            $message .= ". Reason: {$reason}";
        }

        return new self($message, 400, 'INVALID_OPERATION');
    }

    /**
     * Create a payment failed exception.
     */
    public static function paymentFailed(string $reason = ''): self
    {
        $message = 'Payment processing failed';
        if ($reason) {
            $message .= ": {$reason}";
        }

        return new self($message, 402, 'PAYMENT_FAILED');
    }

    /**
     * Create a store access denied exception.
     */
    public static function storeAccessDenied(string $storeName = ''): self
    {
        $message = 'You do not have access to this store';
        if ($storeName) {
            $message .= ": {$storeName}";
        }

        return new self($message, 403, 'STORE_ACCESS_DENIED');
    }

    /**
     * Create a duplicate entry exception.
     */
    public static function duplicateEntry(string $field, string $value): self
    {
        return new self(
            "Duplicate entry for {$field}: {$value}",
            409,
            'DUPLICATE_ENTRY'
        );
    }

    /**
     * Create a resource locked exception.
     */
    public static function resourceLocked(string $resource, string $reason = ''): self
    {
        $message = "Resource is locked: {$resource}";
        if ($reason) {
            $message .= ". Reason: {$reason}";
        }

        return new self($message, 423, 'RESOURCE_LOCKED');
    }
}
