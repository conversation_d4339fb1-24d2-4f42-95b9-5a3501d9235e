<div>
    <h3 class="text-lg font-medium text-gray-900 mb-4">Database Configuration</h3>
    <p class="text-sm text-gray-600 mb-6">
        Please provide your database connection details. Make sure the database exists and the user has proper permissions.
    </p>

    <form method="POST" action="{{ route('install.process') }}" id="database-form">
        @csrf
        <input type="hidden" name="step" value="2">

        <div class="space-y-4">
            <div>
                <label for="db_host" class="block text-sm font-medium text-gray-700">Database Host</label>
                <input type="text" name="db_host" id="db_host" value="{{ old('db_host', 'localhost') }}" required
                    class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                <p class="mt-1 text-xs text-gray-500">Usually 'localhost' or '127.0.0.1'</p>
            </div>

            <div>
                <label for="db_port" class="block text-sm font-medium text-gray-700">Database Port</label>
                <input type="number" name="db_port" id="db_port" value="{{ old('db_port', '3306') }}" required min="1" max="65535"
                    class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                <p class="mt-1 text-xs text-gray-500">Default MySQL port is 3306</p>
            </div>

            <div>
                <label for="db_name" class="block text-sm font-medium text-gray-700">Database Name</label>
                <input type="text" name="db_name" id="db_name" value="{{ old('db_name', 'syncpos') }}" required
                    class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                <p class="mt-1 text-xs text-gray-500">The database will be created if it doesn't exist</p>
            </div>

            <div>
                <label for="db_user" class="block text-sm font-medium text-gray-700">Database Username</label>
                <input type="text" name="db_user" id="db_user" value="{{ old('db_user', 'root') }}" required
                    class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
            </div>

            <div>
                <label for="db_pass" class="block text-sm font-medium text-gray-700">Database Password</label>
                <input type="password" name="db_pass" id="db_pass" value="{{ old('db_pass') }}"
                    class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                <p class="mt-1 text-xs text-gray-500">Leave empty if no password is required</p>
            </div>
        </div>

        <div class="mt-6 flex space-x-3">
            <button type="button" id="test-connection" onclick="testDatabaseConnection()"
                class="flex-1 flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                Test Connection
            </button>
            <button type="submit"
                class="flex-1 flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                Continue
            </button>
        </div>
    </form>

    <div class="mt-6">
        <a href="{{ route('install.index', ['step' => 1]) }}" class="text-sm text-indigo-600 hover:text-indigo-500">
            ← Back to Requirements
        </a>
    </div>

    <!-- Database Setup Instructions -->
    <div class="mt-8 bg-blue-50 border border-blue-200 rounded-md p-4">
        <h4 class="text-sm font-medium text-blue-800 mb-2">Database Setup Instructions</h4>
        <div class="text-xs text-blue-700 space-y-1">
            <p><strong>For cPanel/Shared Hosting:</strong></p>
            <ul class="list-disc list-inside ml-2 space-y-1">
                <li>Create a new MySQL database in cPanel</li>
                <li>Create a database user and assign it to the database</li>
                <li>Grant all privileges to the user</li>
                <li>Use the full database name (usually prefixed with your username)</li>
            </ul>
            
            <p class="mt-3"><strong>For Local Development:</strong></p>
            <ul class="list-disc list-inside ml-2 space-y-1">
                <li>Make sure MySQL/MariaDB is running</li>
                <li>Use 'localhost' as host and '3306' as port</li>
                <li>Create the database manually or let the installer create it</li>
            </ul>
        </div>
    </div>
</div>

<script>
function testDatabaseConnection() {
    const form = document.getElementById('database-form');
    const formData = new FormData(form);
    const button = document.getElementById('test-connection');
    const originalText = button.textContent;
    
    button.textContent = 'Testing...';
    button.disabled = true;
    
    // Add test flag
    formData.append('test_connection', '1');
    
    fetch('{{ route("install.process") }}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('Database connection successful!', 'success');
        } else {
            showAlert('Database connection failed: ' + (data.message || 'Unknown error'), 'error');
        }
    })
    .catch(error => {
        showAlert('Error testing connection: ' + error.message, 'error');
    })
    .finally(() => {
        button.textContent = originalText;
        button.disabled = false;
    });
}

function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `mt-4 p-4 rounded-md ${type === 'success' ? 'bg-green-100 border border-green-400 text-green-700' : 'bg-red-100 border border-red-400 text-red-700'}`;
    alertDiv.textContent = message;
    
    const form = document.getElementById('database-form');
    form.parentNode.insertBefore(alertDiv, form.nextSibling);
    
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}
</script>
