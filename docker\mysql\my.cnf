[mysqld]
# Basic settings
default-storage-engine=innodb
sql_mode=STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO

# Character set
character-set-server=utf8mb4
collation-server=utf8mb4_unicode_ci

# Connection settings
max_connections=200
max_connect_errors=1000
wait_timeout=28800
interactive_timeout=28800

# Buffer settings
innodb_buffer_pool_size=1G
innodb_log_file_size=256M
innodb_log_buffer_size=64M
innodb_flush_log_at_trx_commit=2

# Query cache
query_cache_type=1
query_cache_size=64M
query_cache_limit=2M

# Slow query log
slow_query_log=1
slow_query_log_file=/var/log/mysql/slow.log
long_query_time=2

# Binary logging
log-bin=mysql-bin
binlog_format=ROW
expire_logs_days=7

# Security
local-infile=0

[mysql]
default-character-set=utf8mb4

[client]
default-character-set=utf8mb4
