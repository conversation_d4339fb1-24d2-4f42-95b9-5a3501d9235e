<?php
/**
 * Complete SyncPOS Setup Script
 * This script will install all dependencies and prepare SyncPOS for installation
 */

set_time_limit(300); // 5 minutes timeout

echo "<h1>🚀 Complete SyncPOS Setup</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .info { color: blue; }
    .terminal { background: #000; color: #0f0; padding: 10px; border-radius: 5px; margin: 10px 0; font-family: monospace; white-space: pre-wrap; overflow-x: auto; }
    .button { background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 5px; }
    .step { background: #f8f9fa; border-left: 4px solid #007bff; padding: 15px; margin: 10px 0; }
    .progress { background: #e9ecef; border-radius: 10px; height: 20px; margin: 10px 0; }
    .progress-bar { background: #007bff; height: 100%; border-radius: 10px; transition: width 0.3s; }
</style>";

$steps = [
    'check_composer' => 'Check Composer Installation',
    'install_php_deps' => 'Install PHP Dependencies',
    'setup_env' => 'Setup Environment File',
    'set_permissions' => 'Set File Permissions',
    'install_node_deps' => 'Install Node.js Dependencies (Optional)',
    'build_assets' => 'Build Frontend Assets (Optional)',
    'clear_caches' => 'Clear All Caches',
    'verify_setup' => 'Verify Installation'
];

$currentStep = $_GET['step'] ?? 'start';
$stepKeys = array_keys($steps);
$currentIndex = array_search($currentStep, $stepKeys);

if ($currentStep === 'start') {
    echo "<div class='step'>";
    echo "<h2>🎯 SyncPOS Complete Setup</h2>";
    echo "<p>This script will automatically:</p>";
    echo "<ul>";
    foreach ($steps as $step => $title) {
        echo "<li>$title</li>";
    }
    echo "</ul>";
    echo "<p><strong>Estimated time:</strong> 2-5 minutes</p>";
    echo "<p><a href='?step=check_composer' class='button'>🚀 Start Setup</a></p>";
    echo "</div>";
    exit;
}

// Progress bar
$progress = $currentIndex !== false ? (($currentIndex + 1) / count($steps)) * 100 : 0;
echo "<div class='progress'>";
echo "<div class='progress-bar' style='width: {$progress}%'></div>";
echo "</div>";
echo "<p>Progress: " . round($progress) . "% - Step " . ($currentIndex + 1) . " of " . count($steps) . "</p>";

switch ($currentStep) {
    case 'check_composer':
        echo "<div class='step'>";
        echo "<h2>🔍 Step 1: Checking Composer</h2>";
        
        exec('composer --version 2>&1', $output, $return);
        echo "<div class='terminal'>" . htmlspecialchars(implode("\n", $output)) . "</div>";
        
        if ($return === 0) {
            echo "<p class='success'>✅ Composer is working!</p>";
            echo "<script>setTimeout(() => window.location.href = '?step=install_php_deps', 2000);</script>";
        } else {
            echo "<p class='error'>❌ Composer not found. Please install Composer first.</p>";
            echo "<p><a href='https://getcomposer.org/download/' target='_blank'>Download Composer</a></p>";
        }
        echo "</div>";
        break;

    case 'install_php_deps':
        echo "<div class='step'>";
        echo "<h2>📦 Step 2: Installing PHP Dependencies</h2>";
        
        if (file_exists('vendor/autoload.php')) {
            echo "<p class='success'>✅ Dependencies already installed!</p>";
            echo "<script>setTimeout(() => window.location.href = '?step=setup_env', 1000);</script>";
        } else {
            echo "<p class='info'>Installing Composer dependencies...</p>";
            echo "<div class='terminal'>";
            
            $command = 'composer install --optimize-autoloader --no-dev 2>&1';
            $handle = popen($command, 'r');
            
            if ($handle) {
                while (($line = fgets($handle)) !== false) {
                    echo htmlspecialchars($line);
                    flush();
                    ob_flush();
                }
                $return = pclose($handle);
                
                if ($return === 0 && file_exists('vendor/autoload.php')) {
                    echo "\n<span class='success'>✅ Dependencies installed successfully!</span>";
                    echo "<script>setTimeout(() => window.location.href = '?step=setup_env', 3000);</script>";
                } else {
                    echo "\n<span class='error'>❌ Installation failed!</span>";
                }
            }
            echo "</div>";
        }
        echo "</div>";
        break;

    case 'setup_env':
        echo "<div class='step'>";
        echo "<h2>⚙️ Step 3: Setting up Environment</h2>";
        
        if (!file_exists('.env')) {
            if (file_exists('.env.example')) {
                copy('.env.example', '.env');
                echo "<p class='success'>✅ .env file created from .env.example</p>";
            } else {
                // Create basic .env file
                $envContent = 'APP_NAME="SyncPOS"
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_TIMEZONE=UTC
APP_URL=http://localhost

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=syncpos
DB_USERNAME=root
DB_PASSWORD=

SESSION_DRIVER=file
CACHE_STORE=file
QUEUE_CONNECTION=sync
MAIL_MAILER=log
';
                file_put_contents('.env', $envContent);
                echo "<p class='success'>✅ Basic .env file created</p>";
            }
        } else {
            echo "<p class='info'>✅ .env file already exists</p>";
        }
        
        // Generate application key
        echo "<p class='info'>Generating application key...</p>";
        exec('php artisan key:generate --force 2>&1', $keyOutput, $keyReturn);
        echo "<div class='terminal'>" . htmlspecialchars(implode("\n", $keyOutput)) . "</div>";
        
        if ($keyReturn === 0) {
            echo "<p class='success'>✅ Application key generated</p>";
        }
        
        echo "<script>setTimeout(() => window.location.href = '?step=set_permissions', 2000);</script>";
        echo "</div>";
        break;

    case 'set_permissions':
        echo "<div class='step'>";
        echo "<h2>🔐 Step 4: Setting File Permissions</h2>";
        
        $directories = ['storage', 'bootstrap/cache'];
        
        foreach ($directories as $dir) {
            if (is_dir($dir)) {
                if (is_writable($dir)) {
                    echo "<p class='success'>✅ $dir is writable</p>";
                } else {
                    echo "<p class='warning'>⚠️ $dir may not be writable</p>";
                }
            } else {
                echo "<p class='error'>❌ $dir directory not found</p>";
            }
        }
        
        echo "<script>setTimeout(() => window.location.href = '?step=install_node_deps', 2000);</script>";
        echo "</div>";
        break;

    case 'install_node_deps':
        echo "<div class='step'>";
        echo "<h2>📦 Step 5: Installing Node.js Dependencies (Optional)</h2>";
        
        exec('npm --version 2>&1', $npmOutput, $npmReturn);
        
        if ($npmReturn === 0) {
            echo "<p class='success'>✅ Node.js/NPM is available</p>";
            echo "<div class='terminal'>" . htmlspecialchars(implode("\n", $npmOutput)) . "</div>";
            
            if (file_exists('package.json')) {
                echo "<p class='info'>Installing Node.js dependencies...</p>";
                echo "<div class='terminal'>";
                
                $command = 'npm install 2>&1';
                $handle = popen($command, 'r');
                
                if ($handle) {
                    while (($line = fgets($handle)) !== false) {
                        echo htmlspecialchars($line);
                        flush();
                        ob_flush();
                    }
                    pclose($handle);
                }
                echo "</div>";
            }
        } else {
            echo "<p class='warning'>⚠️ Node.js/NPM not found. Frontend assets won't be built.</p>";
            echo "<p class='info'>This is optional for basic functionality.</p>";
        }
        
        echo "<script>setTimeout(() => window.location.href = '?step=build_assets', 3000);</script>";
        echo "</div>";
        break;

    case 'build_assets':
        echo "<div class='step'>";
        echo "<h2>🎨 Step 6: Building Frontend Assets (Optional)</h2>";
        
        if (file_exists('node_modules') && file_exists('package.json')) {
            echo "<p class='info'>Building frontend assets...</p>";
            echo "<div class='terminal'>";
            
            $command = 'npm run build 2>&1';
            $handle = popen($command, 'r');
            
            if ($handle) {
                while (($line = fgets($handle)) !== false) {
                    echo htmlspecialchars($line);
                    flush();
                    ob_flush();
                }
                pclose($handle);
            }
            echo "</div>";
        } else {
            echo "<p class='info'>⚠️ Skipping asset build (Node.js dependencies not installed)</p>";
        }
        
        echo "<script>setTimeout(() => window.location.href = '?step=clear_caches', 2000);</script>";
        echo "</div>";
        break;

    case 'clear_caches':
        echo "<div class='step'>";
        echo "<h2>🧹 Step 7: Clearing All Caches</h2>";
        
        $cacheCommands = [
            'php artisan cache:clear',
            'php artisan config:clear',
            'php artisan route:clear',
            'php artisan view:clear'
        ];
        
        foreach ($cacheCommands as $command) {
            exec($command . ' 2>&1', $output, $return);
            if ($return === 0) {
                echo "<p class='success'>✅ " . htmlspecialchars($command) . "</p>";
            } else {
                echo "<p class='warning'>⚠️ " . htmlspecialchars($command) . " - " . htmlspecialchars(implode(' ', $output)) . "</p>";
            }
            $output = [];
        }
        
        echo "<script>setTimeout(() => window.location.href = '?step=verify_setup', 2000);</script>";
        echo "</div>";
        break;

    case 'verify_setup':
        echo "<div class='step'>";
        echo "<h2>✅ Step 8: Verifying Installation</h2>";
        
        $checks = [
            'Composer Dependencies' => file_exists('vendor/autoload.php'),
            'Environment File' => file_exists('.env'),
            'Application Key Set' => strpos(file_get_contents('.env'), 'APP_KEY=base64:') !== false,
            'Storage Writable' => is_writable('storage'),
            'Bootstrap Cache Writable' => is_writable('bootstrap/cache'),
        ];
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th style='padding: 10px; background: #f0f0f0;'>Check</th><th style='padding: 10px; background: #f0f0f0;'>Status</th></tr>";
        
        $allGood = true;
        foreach ($checks as $check => $status) {
            $statusText = $status ? "<span class='success'>✅ OK</span>" : "<span class='error'>❌ FAIL</span>";
            echo "<tr><td style='padding: 10px;'>$check</td><td style='padding: 10px;'>$statusText</td></tr>";
            if (!$status) $allGood = false;
        }
        echo "</table>";
        
        if ($allGood) {
            echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 20px; border-radius: 10px; margin: 20px 0; text-align: center;'>";
            echo "<h2 class='success'>🎉 Setup Complete!</h2>";
            echo "<p>SyncPOS is now ready for installation!</p>";
            echo "<p><a href='public/install' class='button' style='font-size: 18px; padding: 15px 30px;'>🚀 Start SyncPOS Installation</a></p>";
            echo "</div>";
        } else {
            echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
            echo "<h3 class='error'>❌ Setup Issues Found</h3>";
            echo "<p>Please fix the failed checks above before proceeding.</p>";
            echo "<p><a href='?step=check_composer' class='button'>🔄 Restart Setup</a></p>";
            echo "</div>";
        }
        echo "</div>";
        break;
}

echo "<hr>";
echo "<p style='text-align: center; color: #666;'>";
echo "<strong>SyncPOS Complete Setup</strong><br>";
echo "Automated installation and configuration";
echo "</p>";
?>
