<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PurchaseOrder extends Model
{
    use HasFactory;

    protected $fillable = [
        'po_number',
        'supplier_id',
        'store_id',
        'created_by',
        'subtotal',
        'tax_amount',
        'total_amount',
        'status',
        'payment_status',
        'expected_delivery_date',
        'received_date',
        'notes',
    ];

    protected $casts = [
        'subtotal' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'expected_delivery_date' => 'date',
        'received_date' => 'date',
    ];

    /**
     * Get the supplier for this purchase order.
     */
    public function supplier()
    {
        return $this->belongsTo(Supplier::class);
    }

    /**
     * Get the store for this purchase order.
     */
    public function store()
    {
        return $this->belongsTo(Store::class);
    }

    /**
     * Get the user who created this purchase order.
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the purchase order items.
     */
    public function items()
    {
        return $this->hasMany(PurchaseOrderItem::class);
    }

    /**
     * Generate a unique PO number.
     */
    public static function generatePoNumber(): string
    {
        $prefix = 'PO-';
        $date = now()->format('Ymd');
        $lastPo = static::whereDate('created_at', today())
            ->orderBy('id', 'desc')
            ->first();

        $sequence = $lastPo ? (int) substr($lastPo->po_number, -4) + 1 : 1;

        return $prefix . $date . '-' . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Calculate totals based on items.
     */
    public function calculateTotals(): void
    {
        $subtotal = $this->items()->sum(\DB::raw('quantity * cost_price'));
        $taxAmount = $subtotal * 0.1; // Assuming 10% tax
        $total = $subtotal + $taxAmount;

        $this->update([
            'subtotal' => $subtotal,
            'tax_amount' => $taxAmount,
            'total_amount' => $total,
        ]);
    }

    /**
     * Mark as received and update inventory.
     */
    public function markAsReceived(): void
    {
        $this->update([
            'status' => 'received',
            'received_date' => now(),
        ]);

        // Update inventory for each item
        foreach ($this->items as $item) {
            $currentStock = $this->store->getProductStock($item->product);
            $newStock = $currentStock + $item->quantity;
            
            $this->store->updateProductStock(
                $item->product,
                $newStock,
                "Purchase Order #{$this->po_number} received"
            );

            // Update product cost price
            $item->product->update(['cost_price' => $item->cost_price]);
        }
    }

    /**
     * Check if PO can be received.
     */
    public function canBeReceived(): bool
    {
        return in_array($this->status, ['sent', 'confirmed']);
    }

    /**
     * Scope for pending orders.
     */
    public function scopePending($query)
    {
        return $query->whereIn('status', ['draft', 'sent', 'confirmed']);
    }

    /**
     * Scope for received orders.
     */
    public function scopeReceived($query)
    {
        return $query->where('status', 'received');
    }
}
