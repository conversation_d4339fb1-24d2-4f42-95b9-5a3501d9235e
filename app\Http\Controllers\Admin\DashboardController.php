<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Tenant;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class DashboardController extends Controller
{
    public function index()
    {
        // Platform statistics
        $stats = [
            'total_tenants' => Tenant::count(),
            'active_tenants' => Tenant::where('is_active', true)->count(),
            'trial_tenants' => Tenant::whereNotNull('trial_ends_at')
                ->where('trial_ends_at', '>', now())
                ->count(),
            'subscribed_tenants' => Tenant::whereHas('subscriptions', function ($query) {
                $query->where('stripe_status', 'active');
            })->count(),
        ];

        // Revenue statistics
        $revenue = [
            'monthly_revenue' => Tenant::whereHas('subscriptions', function ($query) {
                $query->where('stripe_status', 'active');
            })->sum('subscriptions.stripe_price'),
            'annual_revenue' => Tenant::whereHas('subscriptions', function ($query) {
                $query->where('stripe_status', 'active');
            })->sum('subscriptions.stripe_price') * 12,
        ];

        // Recent tenants
        $recentTenants = Tenant::with('domains')
            ->latest()
            ->limit(10)
            ->get();

        // Subscription breakdown
        $subscriptionBreakdown = Tenant::select('subscription_plan', DB::raw('count(*) as count'))
            ->groupBy('subscription_plan')
            ->get();

        return view('admin.dashboard', compact(
            'stats', 'revenue', 'recentTenants', 'subscriptionBreakdown'
        ));
    }
}
