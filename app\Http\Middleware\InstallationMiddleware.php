<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Symfony\Component\HttpFoundation\Response;

class InstallationMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if application is already installed
        if ($this->isInstalled()) {
            // If trying to access install routes when already installed, redirect to home
            if ($request->is('install*')) {
                return redirect('/')->with('info', 'Application is already installed.');
            }
        } else {
            // If not installed and not accessing install routes, redirect to install
            if (!$request->is('install*')) {
                return redirect('/install');
            }
        }

        return $next($request);
    }

    /**
     * Check if the application is installed.
     */
    private function isInstalled(): bool
    {
        return Storage::exists('installed') && file_exists(base_path('.env'));
    }
}
