<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SyncPOS Installation</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
    <div class="min-h-screen flex items-center justify-center py-12 px-4">
        <div class="max-w-2xl w-full space-y-8">
            <div class="text-center">
                <h1 class="text-3xl font-bold text-gray-900">🚀 SyncPOS Installation</h1>
                <p class="mt-2 text-gray-600">Let's get your Point of Sale system up and running!</p>
            </div>

            @if($errors->any())
                <div class="bg-red-50 border border-red-200 rounded-md p-4">
                    <h3 class="text-red-800 font-medium">Installation Error:</h3>
                    <ul class="mt-2 text-red-700">
                        @foreach($errors->all() as $error)
                            <li>• {{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            <div class="bg-white shadow rounded-lg p-6">
                <form method="POST" action="{{ route('install.process') }}" class="space-y-6">
                    @csrf

                    <!-- Application Settings -->
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Application Settings</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Application Name</label>
                                <input type="text" name="app_name" value="{{ old('app_name', 'SyncPOS') }}" 
                                       class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2" required>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Application URL</label>
                                <input type="url" name="app_url" value="{{ old('app_url', request()->getSchemeAndHttpHost()) }}" 
                                       class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2" required>
                            </div>
                        </div>
                    </div>

                    <!-- Database Settings -->
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Database Settings</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Database Host</label>
                                <input type="text" name="db_host" value="{{ old('db_host', '127.0.0.1') }}" 
                                       class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2" required>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Database Port</label>
                                <input type="number" name="db_port" value="{{ old('db_port', '3306') }}" 
                                       class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2" required>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Database Name</label>
                                <input type="text" name="db_name" value="{{ old('db_name', 'syncpos') }}" 
                                       class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2" required>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Database Username</label>
                                <input type="text" name="db_user" value="{{ old('db_user', 'root') }}" 
                                       class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2" required>
                            </div>
                            <div class="md:col-span-2">
                                <label class="block text-sm font-medium text-gray-700">Database Password</label>
                                <input type="password" name="db_pass" value="{{ old('db_pass') }}" 
                                       class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2">
                            </div>
                        </div>
                    </div>

                    <!-- Admin Settings -->
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Administrator Account</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Admin Name</label>
                                <input type="text" name="admin_name" value="{{ old('admin_name', 'Administrator') }}" 
                                       class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2" required>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Admin Email</label>
                                <input type="email" name="admin_email" value="{{ old('admin_email', '<EMAIL>') }}" 
                                       class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2" required>
                            </div>
                            <div class="md:col-span-2">
                                <label class="block text-sm font-medium text-gray-700">Admin Password</label>
                                <input type="password" name="admin_password" 
                                       class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2" required>
                                <p class="text-sm text-gray-500">Minimum 6 characters</p>
                            </div>
                        </div>
                    </div>

                    <!-- Store Settings -->
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Store Information</h3>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Store Name</label>
                            <input type="text" name="store_name" value="{{ old('store_name', 'My Store') }}" 
                                   class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2" required>
                        </div>
                    </div>

                    <div class="text-center">
                        <button type="submit" class="bg-blue-600 text-white px-8 py-3 rounded-md hover:bg-blue-700 font-medium">
                            🚀 Install SyncPOS
                        </button>
                    </div>
                </form>
            </div>

            <div class="text-center">
                <p class="text-sm text-gray-500">
                    SyncPOS v1.0 - Modern Point of Sale System
                </p>
            </div>
        </div>
    </div>
</body>
</html>
