# 🤝 Contributing to <PERSON>ync<PERSON><PERSON>

Thank you for your interest in contributing to SyncPOS! This guide will help you get started with contributing to our project.

## 📋 Table of Contents

- [Code of Conduct](#code-of-conduct)
- [Getting Started](#getting-started)
- [Development Setup](#development-setup)
- [Contributing Guidelines](#contributing-guidelines)
- [Pull Request Process](#pull-request-process)
- [Coding Standards](#coding-standards)
- [Testing](#testing)
- [Documentation](#documentation)

## 📜 Code of Conduct

By participating in this project, you agree to abide by our [Code of Conduct](CODE_OF_CONDUCT.md). Please read it before contributing.

## 🚀 Getting Started

### Prerequisites

- PHP 8.2 or higher
- Composer
- Node.js & NPM
- MySQL 8.0+
- Git

### Types of Contributions

We welcome various types of contributions:

- 🐛 **Bug Reports**: Help us identify and fix issues
- ✨ **Feature Requests**: Suggest new features or improvements
- 💻 **Code Contributions**: Submit bug fixes or new features
- 📚 **Documentation**: Improve our documentation
- 🧪 **Testing**: Add or improve test coverage
- 🌐 **Translations**: Help translate SyncPOS to other languages

## 🛠️ Development Setup

### 1. <PERSON> and Clone

```bash
# Fork the repository on GitHub, then clone your fork
git clone https://github.com/YOUR_USERNAME/syncpos.git
cd syncpos

# Add the original repository as upstream
git remote add upstream https://github.com/original-repo/syncpos.git
```

### 2. Install Dependencies

```bash
# Install PHP dependencies
composer install

# Install Node.js dependencies
npm install

# Copy environment file
cp .env.example .env

# Generate application key
php artisan key:generate
```

### 3. Database Setup

```bash
# Create database
mysql -u root -p -e "CREATE DATABASE syncpos_dev;"

# Update .env with database credentials
# DB_DATABASE=syncpos_dev

# Run migrations
php artisan migrate

# Seed database with sample data
php artisan db:seed
```

### 4. Build Assets

```bash
# Build frontend assets
npm run dev

# Or watch for changes during development
npm run watch
```

### 5. Start Development Server

```bash
# Start Laravel development server
php artisan serve

# In another terminal, start queue worker
php artisan queue:work
```

## 📝 Contributing Guidelines

### Before You Start

1. **Check existing issues**: Look for existing issues or discussions about your idea
2. **Create an issue**: If none exists, create an issue to discuss your proposal
3. **Get feedback**: Wait for maintainer feedback before starting work
4. **Assign yourself**: Comment on the issue to let others know you're working on it

### Branch Naming

Use descriptive branch names with prefixes:

- `feature/` - New features
- `bugfix/` - Bug fixes
- `hotfix/` - Critical fixes
- `docs/` - Documentation updates
- `test/` - Test improvements

Examples:
```bash
feature/customer-loyalty-points
bugfix/inventory-calculation-error
docs/api-documentation-update
```

### Commit Messages

Follow conventional commit format:

```
type(scope): description

[optional body]

[optional footer]
```

Types:
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes (formatting, etc.)
- `refactor`: Code refactoring
- `test`: Adding or updating tests
- `chore`: Maintenance tasks

Examples:
```bash
feat(pos): add barcode scanning functionality
fix(inventory): correct stock calculation for transfers
docs(api): update authentication examples
test(sales): add unit tests for refund process
```

## 🔄 Pull Request Process

### 1. Create Feature Branch

```bash
# Update your fork
git fetch upstream
git checkout main
git merge upstream/main

# Create feature branch
git checkout -b feature/your-feature-name
```

### 2. Make Changes

- Write clean, well-documented code
- Follow our coding standards
- Add tests for new functionality
- Update documentation as needed

### 3. Test Your Changes

```bash
# Run all tests
php artisan test

# Run specific test suites
php artisan test --testsuite=Feature
php artisan test --testsuite=Unit

# Check code style
./vendor/bin/pint --test

# Run static analysis
./vendor/bin/phpstan analyse
```

### 4. Commit and Push

```bash
# Stage your changes
git add .

# Commit with descriptive message
git commit -m "feat(pos): add barcode scanning functionality"

# Push to your fork
git push origin feature/your-feature-name
```

### 5. Create Pull Request

1. Go to your fork on GitHub
2. Click "New Pull Request"
3. Select your feature branch
4. Fill out the PR template
5. Submit the pull request

### Pull Request Template

```markdown
## Description
Brief description of changes made.

## Type of Change
- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] Documentation update

## Testing
- [ ] Tests pass locally
- [ ] New tests added for new functionality
- [ ] Manual testing completed

## Screenshots (if applicable)
Add screenshots to help explain your changes.

## Checklist
- [ ] Code follows project style guidelines
- [ ] Self-review completed
- [ ] Code is commented where necessary
- [ ] Documentation updated
- [ ] No new warnings introduced
```

## 🎨 Coding Standards

### PHP Standards

We follow PSR-12 coding standards with some additional rules:

```php
<?php

namespace App\Services;

use App\Models\Product;
use Illuminate\Support\Collection;

class ProductService
{
    /**
     * Calculate product profit margin.
     */
    public function calculateProfitMargin(Product $product): float
    {
        if ($product->price <= 0) {
            return 0.0;
        }

        return (($product->price - $product->cost_price) / $product->price) * 100;
    }
}
```

### Laravel Best Practices

- Use Eloquent relationships instead of manual joins
- Implement form request validation
- Use resource controllers for CRUD operations
- Follow repository pattern for complex queries
- Use service classes for business logic
- Implement proper error handling

### Frontend Standards

- Use Alpine.js for interactive components
- Follow Tailwind CSS utility-first approach
- Keep JavaScript minimal and focused
- Use semantic HTML elements
- Ensure accessibility compliance

## 🧪 Testing

### Writing Tests

- Write tests for all new functionality
- Aim for high test coverage
- Use descriptive test names
- Follow AAA pattern (Arrange, Act, Assert)

```php
public function test_user_can_create_product_with_valid_data()
{
    // Arrange
    $user = User::factory()->create();
    $category = Category::factory()->create();
    
    $productData = [
        'name' => 'Test Product',
        'sku' => 'TEST-001',
        'price' => 99.99,
        'cost_price' => 50.00,
        'category_id' => $category->id,
    ];

    // Act
    $response = $this->actingAs($user)
        ->post(route('products.store'), $productData);

    // Assert
    $response->assertRedirect();
    $this->assertDatabaseHas('products', [
        'name' => 'Test Product',
        'sku' => 'TEST-001',
    ]);
}
```

### Test Categories

- **Unit Tests**: Test individual methods and classes
- **Feature Tests**: Test complete user workflows
- **Integration Tests**: Test external service integrations
- **Browser Tests**: Test frontend functionality

## 📚 Documentation

### Code Documentation

- Add PHPDoc blocks for all public methods
- Document complex algorithms and business logic
- Include examples in documentation
- Keep comments up to date with code changes

### User Documentation

- Update user guides for new features
- Add screenshots for UI changes
- Update API documentation for new endpoints
- Include migration guides for breaking changes

## 🐛 Bug Reports

When reporting bugs, please include:

1. **Environment details**: PHP version, Laravel version, OS
2. **Steps to reproduce**: Clear, numbered steps
3. **Expected behavior**: What should happen
4. **Actual behavior**: What actually happens
5. **Screenshots**: If applicable
6. **Error messages**: Full error messages and stack traces

## ✨ Feature Requests

When requesting features, please include:

1. **Problem description**: What problem does this solve?
2. **Proposed solution**: How should it work?
3. **Alternatives considered**: Other solutions you've thought of
4. **Use cases**: Real-world scenarios where this would be useful

## 🏷️ Release Process

### Versioning

We follow [Semantic Versioning](https://semver.org/):

- **MAJOR**: Breaking changes
- **MINOR**: New features (backward compatible)
- **PATCH**: Bug fixes (backward compatible)

### Release Checklist

- [ ] All tests pass
- [ ] Documentation updated
- [ ] CHANGELOG.md updated
- [ ] Version bumped
- [ ] Release notes prepared
- [ ] Security review completed

## 🆘 Getting Help

If you need help:

1. **Check documentation**: Look at our docs first
2. **Search issues**: Someone might have asked already
3. **Ask in discussions**: Use GitHub Discussions for questions
4. **Contact maintainers**: Reach out to the core team

## 🙏 Recognition

Contributors will be recognized in:

- CONTRIBUTORS.md file
- Release notes
- Project README
- Annual contributor highlights

Thank you for contributing to SyncPOS! 🚀
