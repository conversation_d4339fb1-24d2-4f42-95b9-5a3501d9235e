version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: syncpos_app
    restart: unless-stopped
    working_dir: /var/www
    volumes:
      - ./:/var/www
      - ./docker/php/local.ini:/usr/local/etc/php/conf.d/local.ini
    networks:
      - syncpos
    depends_on:
      - db
      - redis

  nginx:
    image: nginx:alpine
    container_name: syncpos_nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./:/var/www
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/sites/:/etc/nginx/sites-available
      - ./docker/nginx/ssl/:/etc/ssl/certs
    networks:
      - syncpos
    depends_on:
      - app

  db:
    image: mysql:8.0
    container_name: syncpos_db
    restart: unless-stopped
    environment:
      MYSQL_DATABASE: ${DB_DATABASE}
      MYSQL_ROOT_PASSWORD: ${DB_PASSWORD}
      MYSQL_PASSWORD: ${DB_PASSWORD}
      MYSQL_USER: ${DB_USERNAME}
      SERVICE_TAGS: dev
      SERVICE_NAME: mysql
    volumes:
      - dbdata:/var/lib/mysql
      - ./docker/mysql/my.cnf:/etc/mysql/my.cnf
    networks:
      - syncpos
    ports:
      - "3306:3306"

  redis:
    image: redis:alpine
    container_name: syncpos_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    networks:
      - syncpos

  queue:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: syncpos_queue
    restart: unless-stopped
    command: php artisan queue:work --verbose --tries=3 --timeout=90
    working_dir: /var/www
    volumes:
      - ./:/var/www
    networks:
      - syncpos
    depends_on:
      - db
      - redis

  scheduler:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: syncpos_scheduler
    restart: unless-stopped
    command: php artisan schedule:work
    working_dir: /var/www
    volumes:
      - ./:/var/www
    networks:
      - syncpos
    depends_on:
      - db
      - redis

  horizon:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: syncpos_horizon
    restart: unless-stopped
    command: php artisan horizon
    working_dir: /var/www
    volumes:
      - ./:/var/www
    networks:
      - syncpos
    depends_on:
      - db
      - redis

networks:
  syncpos:
    driver: bridge

volumes:
  dbdata:
    driver: local
