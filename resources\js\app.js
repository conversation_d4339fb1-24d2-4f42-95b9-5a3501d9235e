import { createApp } from 'vue'
import axios from 'axios'

// Set up axios defaults
window.axios = axios
window.axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest'

// Get CSRF token
const token = document.head.querySelector('meta[name="csrf-token"]')
if (token) {
    window.axios.defaults.headers.common['X-CSRF-TOKEN'] = token.content
} else {
    console.error('CSRF token not found')
}

// Import components
import PosInterface from './components/PosInterface.vue'
import ProductSearch from './components/ProductSearch.vue'
import CartComponent from './components/CartComponent.vue'
import PaymentModal from './components/PaymentModal.vue'
import CustomerSelector from './components/CustomerSelector.vue'

// Create Vue app
const app = createApp({})

// Register components
app.component('pos-interface', PosInterface)
app.component('product-search', ProductSearch)
app.component('cart-component', CartComponent)
app.component('payment-modal', PaymentModal)
app.component('customer-selector', CustomerSelector)

// Mount the app
app.mount('#app')
