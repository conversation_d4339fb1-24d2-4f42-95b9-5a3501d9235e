# SyncPOS Web Installer

This web-based installer makes it super easy to install SyncPOS on any hosting environment.

## 🚀 Quick Installation

### For Localhost (XAMPP, WAMP, MAMP)

1. **Download SyncPOS** and extract to your web directory:
   - XAMPP: `C:\xampp\htdocs\syncpos`
   - WAMP: `C:\wamp64\www\syncpos`
   - MAMP: `/Applications/MAMP/htdocs/syncpos`

2. **Start your local server** (Apache + MySQL)

3. **Open your browser** and go to:
   ```
   http://localhost/syncpos
   ```

4. **Follow the installer** - it will guide you through:
   - System requirements check
   - Database configuration
   - Application settings
   - Admin account creation

### For cPanel/Shared Hosting

1. **Upload SyncPOS** files to your hosting account:
   - Extract the ZIP file
   - Upload all files to your domain's public_html folder
   - Or upload to a subdirectory like `public_html/syncpos`

2. **Create a MySQL database** in cPanel:
   - Go to MySQL Databases
   - Create a new database
   - Create a database user
   - Assign the user to the database with all privileges

3. **Open your website** in a browser:
   ```
   https://yourdomain.com
   ```
   or
   ```
   https://yourdomain.com/syncpos
   ```

4. **Follow the installer** and enter your database details

### For VPS/Dedicated Servers

1. **Upload files** to your web directory (usually `/var/www/html`)

2. **Set proper permissions**:
   ```bash
   chmod -R 755 /var/www/html/syncpos
   chmod -R 777 /var/www/html/syncpos/storage
   chmod -R 777 /var/www/html/syncpos/bootstrap/cache
   ```

3. **Open your browser** and follow the installer

## 📋 System Requirements

The installer will automatically check these requirements:

### Required PHP Extensions
- ✅ PHP 8.1 or higher
- ✅ PDO Extension
- ✅ PDO MySQL Extension
- ✅ OpenSSL Extension
- ✅ Mbstring Extension
- ✅ Tokenizer Extension
- ✅ XML Extension
- ✅ Ctype Extension
- ✅ JSON Extension
- ✅ BCMath Extension
- ✅ Fileinfo Extension

### Database
- MySQL 5.7+ or MariaDB 10.3+

### Web Server
- Apache 2.4+ (with mod_rewrite)
- Nginx 1.18+
- Or any PHP-compatible web server

## 🔧 Installation Steps

### Step 1: System Requirements
The installer checks if your server meets all requirements. If any are missing, contact your hosting provider.

### Step 2: Database Configuration
Enter your database details:
- **Database Host**: Usually `localhost`
- **Database Name**: Your MySQL database name
- **Database Username**: Your MySQL username
- **Database Password**: Your MySQL password
- **Database Port**: Usually `3306`

### Step 3: Application Settings
Configure your application:
- **Application Name**: Your business name
- **Application URL**: Your website URL
- **Environment**: Production (recommended) or Local Development
- **Admin Email**: Your email for the admin account
- **Admin Password**: Secure password (minimum 8 characters)

### Step 4: Installation Complete
The installer will:
- Create configuration files
- Set up the database
- Create your admin account
- Set up storage directories

## 🛡️ Security

### After Installation
1. **Delete the installer** for security:
   ```bash
   rm -rf /path/to/your/site/install
   ```

2. **Set proper file permissions**:
   ```bash
   chmod 644 .env
   chmod -R 755 storage
   chmod -R 755 bootstrap/cache
   ```

3. **Change default passwords** and update your admin profile

## 🔧 Troubleshooting

### Common Issues

#### "Database connection failed"
- Check your database credentials
- Ensure MySQL is running
- Verify the database exists
- Check if the database user has proper permissions

#### "Permission denied" errors
- Set proper file permissions:
  ```bash
  chmod -R 755 storage
  chmod -R 755 bootstrap/cache
  ```

#### "Requirements not met"
- Contact your hosting provider to enable missing PHP extensions
- For localhost, edit `php.ini` and uncomment required extensions

#### "Page not found" after installation
- Check if `.htaccess` file exists in the public directory
- Ensure mod_rewrite is enabled on Apache
- For Nginx, configure proper URL rewriting

### Getting Help

If you encounter issues:

1. **Check the requirements** - ensure all PHP extensions are enabled
2. **Verify database credentials** - test connection manually
3. **Check file permissions** - ensure web server can write to storage directories
4. **Review error logs** - check your hosting control panel for error logs

### Manual Installation

If the web installer doesn't work, you can install manually:

1. **Copy `.env.example` to `.env`**
2. **Edit `.env`** with your database details
3. **Run**: `php artisan key:generate`
4. **Run**: `php artisan migrate`
5. **Create admin user** via command line or database

## 📞 Support

For additional help:
- Check the main README.md file
- Review the documentation
- Contact your hosting provider for server-specific issues

## 🎉 What's Next?

After installation:
1. **Login** with your admin credentials
2. **Configure your business settings**
3. **Create your first store**
4. **Add products** to your inventory
5. **Start making sales!**

Welcome to SyncPOS! 🚀
