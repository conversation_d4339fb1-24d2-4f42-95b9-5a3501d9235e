<?php

namespace Database\Seeders;

use App\Models\Brand;
use Illuminate\Database\Seeder;

class BrandSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $brands = [
            // Electronics
            ['name' => 'Apple', 'slug' => 'apple', 'description' => 'Premium consumer electronics'],
            ['name' => 'Samsung', 'slug' => 'samsung', 'description' => 'South Korean electronics giant'],
            ['name' => 'Sony', 'slug' => 'sony', 'description' => 'Japanese electronics and entertainment'],
            ['name' => 'LG', 'slug' => 'lg', 'description' => 'Life\'s Good electronics'],
            ['name' => 'Dell', 'slug' => 'dell', 'description' => 'Computer technology company'],
            ['name' => 'HP', 'slug' => 'hp', 'description' => 'Hewlett-Packard technology'],
            ['name' => 'Lenovo', 'slug' => 'lenovo', 'description' => 'Chinese technology company'],
            ['name' => 'Microsoft', 'slug' => 'microsoft', 'description' => 'Software and hardware technology'],
            ['name' => 'Google', 'slug' => 'google', 'description' => 'Technology and internet services'],
            ['name' => 'Bose', 'slug' => 'bose', 'description' => 'Premium audio equipment'],

            // Clothing
            ['name' => 'Nike', 'slug' => 'nike', 'description' => 'Athletic footwear and apparel'],
            ['name' => 'Adidas', 'slug' => 'adidas', 'description' => 'German sportswear manufacturer'],
            ['name' => 'Levi\'s', 'slug' => 'levis', 'description' => 'American denim and clothing'],
            ['name' => 'H&M', 'slug' => 'hm', 'description' => 'Swedish fashion retailer'],
            ['name' => 'Zara', 'slug' => 'zara', 'description' => 'Spanish fast fashion'],
            ['name' => 'Uniqlo', 'slug' => 'uniqlo', 'description' => 'Japanese casual wear'],
            ['name' => 'Gap', 'slug' => 'gap', 'description' => 'American clothing and accessories'],
            ['name' => 'Calvin Klein', 'slug' => 'calvin-klein', 'description' => 'American fashion house'],

            // Food & Beverages
            ['name' => 'Coca-Cola', 'slug' => 'coca-cola', 'description' => 'The world\'s largest beverage company'],
            ['name' => 'PepsiCo', 'slug' => 'pepsico', 'description' => 'American food and beverage corporation'],
            ['name' => 'Nestlé', 'slug' => 'nestle', 'description' => 'Swiss multinational food and drink company'],
            ['name' => 'Kraft Heinz', 'slug' => 'kraft-heinz', 'description' => 'American food company'],
            ['name' => 'Unilever', 'slug' => 'unilever', 'description' => 'British-Dutch consumer goods company'],
            ['name' => 'General Mills', 'slug' => 'general-mills', 'description' => 'American multinational manufacturer'],

            // Books
            ['name' => 'Penguin Random House', 'slug' => 'penguin-random-house', 'description' => 'World\'s largest trade book publisher'],
            ['name' => 'HarperCollins', 'slug' => 'harpercollins', 'description' => 'American publishing company'],
            ['name' => 'Macmillan', 'slug' => 'macmillan', 'description' => 'British publishing company'],
            ['name' => 'Scholastic', 'slug' => 'scholastic', 'description' => 'American publishing and education company'],

            // Home & Garden
            ['name' => 'IKEA', 'slug' => 'ikea', 'description' => 'Swedish furniture retailer'],
            ['name' => 'Home Depot', 'slug' => 'home-depot', 'description' => 'American home improvement retailer'],
            ['name' => 'Whirlpool', 'slug' => 'whirlpool', 'description' => 'American home appliance manufacturer'],
            ['name' => 'KitchenAid', 'slug' => 'kitchenaid', 'description' => 'American home appliance brand'],

            // Sports & Outdoors
            ['name' => 'Under Armour', 'slug' => 'under-armour', 'description' => 'American sports equipment company'],
            ['name' => 'Puma', 'slug' => 'puma', 'description' => 'German multinational corporation'],
            ['name' => 'The North Face', 'slug' => 'the-north-face', 'description' => 'American outdoor recreation products'],
            ['name' => 'Patagonia', 'slug' => 'patagonia', 'description' => 'American clothing company'],
            ['name' => 'Coleman', 'slug' => 'coleman', 'description' => 'American outdoor recreation products'],

            // Generic/Store Brands
            ['name' => 'Generic', 'slug' => 'generic', 'description' => 'Generic or unbranded products'],
            ['name' => 'Store Brand', 'slug' => 'store-brand', 'description' => 'Private label store brand'],
        ];

        foreach ($brands as $brand) {
            Brand::create(array_merge($brand, ['is_active' => true]));
        }
    }
}
