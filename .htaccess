# SyncPOS Root .htaccess
# Redirect all requests to public directory

<IfModule mod_rewrite.c>
    RewriteEngine On

    # Redirect everything to public directory
    RewriteCond %{REQUEST_URI} !^/public/
    RewriteRule ^(.*)$ public/$1 [L]
</IfModule>

# Security: Deny access to sensitive files
<Files ".env">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

<Files "composer.json">
    Order allow,deny
    <PERSON>y from all
</Files>

<Files "composer.lock">
    Order allow,deny
    <PERSON>y from all
</Files>
