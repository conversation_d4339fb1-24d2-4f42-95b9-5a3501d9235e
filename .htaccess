# SyncPOS Root .htaccess
# Redirect all requests to public directory

<IfModule mod_rewrite.c>
    RewriteEngine On

    # Handle installer - allow direct access
    RewriteCond %{REQUEST_URI} ^/install/
    RewriteRule ^(.*)$ $1 [L]

    # Handle public directory access
    RewriteCond %{REQUEST_URI} ^/public/
    RewriteRule ^public/(.*)$ public/$1 [L]

    # Redirect everything else to public directory
    RewriteCond %{REQUEST_URI} !^/public/
    RewriteCond %{REQUEST_URI} !^/install/
    RewriteRule ^(.*)$ public/$1 [L]
</IfModule>

# Security headers
<IfModule mod_headers.c>
    Header always set X-Frame-Options "SAMEORIGIN"
    Header always set X-XSS-Protection "1; mode=block"
    Header always set X-Content-Type-Options "nosniff"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# Deny access to sensitive files
<FilesMatch "\.(env|log|sql|md|json|lock|yml|yaml)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# Deny access to directories
<DirectoryMatch "(storage|bootstrap|database|config|app|resources|routes|tests|vendor)">
    Order allow,deny
    Deny from all
</DirectoryMatch>
