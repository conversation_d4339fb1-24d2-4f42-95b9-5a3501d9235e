<?php

namespace App\Console\Commands;

use App\Models\Tenant;
use App\Models\Domain;
use Illuminate\Console\Command;
use Illuminate\Support\Str;

class CreateTenant extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'tenant:create 
                            {domain : The domain for the tenant}
                            {company_name : The company name}
                            {email : The company email}
                            {--plan=basic : Subscription plan (basic, professional, enterprise)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create a new tenant with domain';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $domain = $this->argument('domain');
        $companyName = $this->argument('company_name');
        $email = $this->argument('email');
        $plan = $this->option('plan');

        // Validate inputs
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $this->error('Invalid email address');
            return 1;
        }

        if (!in_array($plan, ['basic', 'professional', 'enterprise'])) {
            $this->error('Invalid plan. Must be: basic, professional, or enterprise');
            return 1;
        }

        // Check if domain already exists
        if (Domain::where('domain', $domain)->exists()) {
            $this->error("Domain '{$domain}' already exists");
            return 1;
        }

        // Check if email already exists
        if (Tenant::where('company_email', $email)->exists()) {
            $this->error("Email '{$email}' already exists");
            return 1;
        }

        $this->info('Creating tenant...');

        try {
            // Create tenant
            $tenant = Tenant::create([
                'id' => Str::uuid(),
                'company_name' => $companyName,
                'company_email' => $email,
                'subscription_plan' => $plan,
                'trial_ends_at' => now()->addDays(14), // 14-day trial
                'is_active' => true,
            ]);

            // Create domain
            $tenant->domains()->create([
                'domain' => $domain,
                'is_primary' => true,
            ]);

            $this->info("✅ Tenant created successfully!");
            $this->table(['Field', 'Value'], [
                ['Tenant ID', $tenant->id],
                ['Company Name', $tenant->company_name],
                ['Email', $tenant->company_email],
                ['Domain', $domain],
                ['Plan', $plan],
                ['Trial Ends', $tenant->trial_ends_at->format('Y-m-d H:i:s')],
            ]);

            $this->info("\nNext steps:");
            $this->line("1. Add to hosts file (for local development):");
            $this->line("   127.0.0.1 {$domain}");
            $this->line("2. Run tenant migrations:");
            $this->line("   php artisan tenants:migrate");
            $this->line("3. Access tenant at: http://{$domain}/syncpos/public");

            return 0;

        } catch (\Exception $e) {
            $this->error('Failed to create tenant: ' . $e->getMessage());
            return 1;
        }
    }
}
