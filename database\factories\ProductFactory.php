<?php

namespace Database\Factories;

use App\Models\Product;
use App\Models\Category;
use App\Models\Brand;
use App\Models\Supplier;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Product>
 */
class ProductFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Product::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $name = $this->faker->words(3, true);
        $price = $this->faker->randomFloat(2, 10, 1000);
        $costPrice = $price * $this->faker->randomFloat(2, 0.4, 0.8); // 40-80% of price

        return [
            'name' => ucwords($name),
            'slug' => Str::slug($name),
            'sku' => 'SKU-' . strtoupper($this->faker->unique()->bothify('???###')),
            'barcode' => $this->faker->optional(0.7)->ean13(),
            'description' => $this->faker->optional(0.8)->paragraph(),
            'price' => $price,
            'cost_price' => $costPrice,
            'category_id' => Category::factory(),
            'brand_id' => $this->faker->optional(0.8)->randomElement([Brand::factory(), null]),
            'supplier_id' => $this->faker->optional(0.6)->randomElement([Supplier::factory(), null]),
            'weight' => $this->faker->optional(0.7)->randomFloat(2, 0.1, 50),
            'dimensions' => $this->faker->optional(0.5)->randomElements([
                'length' => $this->faker->randomFloat(2, 1, 100),
                'width' => $this->faker->randomFloat(2, 1, 100),
                'height' => $this->faker->randomFloat(2, 1, 100),
            ]),
            'image' => $this->faker->optional(0.6)->imageUrl(400, 400, 'products'),
            'gallery' => $this->faker->optional(0.3)->randomElements([
                $this->faker->imageUrl(400, 400, 'products'),
                $this->faker->imageUrl(400, 400, 'products'),
                $this->faker->imageUrl(400, 400, 'products'),
            ], $this->faker->numberBetween(1, 3)),
            'is_active' => $this->faker->boolean(90),
            'track_quantity' => $this->faker->boolean(85),
            'allow_backorder' => $this->faker->boolean(30),
            'meta_title' => $this->faker->optional(0.4)->sentence(),
            'meta_description' => $this->faker->optional(0.4)->text(160),
            'tags' => $this->faker->optional(0.5)->words($this->faker->numberBetween(1, 5)),
            'woocommerce_id' => $this->faker->optional(0.2)->randomNumber(5),
            'shopify_id' => $this->faker->optional(0.2)->randomNumber(8),
            'last_synced_at' => $this->faker->optional(0.3)->dateTimeBetween('-1 month', 'now'),
        ];
    }

    /**
     * Indicate that the product is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
        ]);
    }

    /**
     * Indicate that the product is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Indicate that the product tracks quantity.
     */
    public function trackQuantity(): static
    {
        return $this->state(fn (array $attributes) => [
            'track_quantity' => true,
        ]);
    }

    /**
     * Indicate that the product doesn't track quantity.
     */
    public function noTrackQuantity(): static
    {
        return $this->state(fn (array $attributes) => [
            'track_quantity' => false,
        ]);
    }

    /**
     * Indicate that the product allows backorders.
     */
    public function allowBackorder(): static
    {
        return $this->state(fn (array $attributes) => [
            'allow_backorder' => true,
        ]);
    }

    /**
     * Create a product with specific price range.
     */
    public function priceRange(float $min, float $max): static
    {
        return $this->state(function (array $attributes) use ($min, $max) {
            $price = $this->faker->randomFloat(2, $min, $max);
            return [
                'price' => $price,
                'cost_price' => $price * $this->faker->randomFloat(2, 0.4, 0.8),
            ];
        });
    }

    /**
     * Create an electronics product.
     */
    public function electronics(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => $this->faker->randomElement([
                'iPhone 15 Pro',
                'Samsung Galaxy S24',
                'MacBook Air M2',
                'Dell XPS 13',
                'iPad Pro',
                'Sony WH-1000XM5',
                'AirPods Pro',
                'Nintendo Switch',
            ]),
            'category_id' => Category::factory()->state(['name' => 'Electronics', 'slug' => 'electronics']),
            'brand_id' => Brand::factory()->state(['name' => $this->faker->randomElement(['Apple', 'Samsung', 'Sony', 'Dell'])]),
            'price' => $this->faker->randomFloat(2, 100, 2000),
        ]);
    }

    /**
     * Create a clothing product.
     */
    public function clothing(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => $this->faker->randomElement([
                'Nike Air Max 270',
                'Levi\'s 501 Jeans',
                'Adidas Ultraboost',
                'H&M Cotton T-Shirt',
                'Zara Summer Dress',
                'Calvin Klein Polo',
            ]),
            'category_id' => Category::factory()->state(['name' => 'Clothing', 'slug' => 'clothing']),
            'brand_id' => Brand::factory()->state(['name' => $this->faker->randomElement(['Nike', 'Adidas', 'Levi\'s', 'H&M', 'Zara'])]),
            'price' => $this->faker->randomFloat(2, 20, 300),
        ]);
    }

    /**
     * Create a food product.
     */
    public function food(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => $this->faker->randomElement([
                'Coca-Cola 12-Pack',
                'Lay\'s Classic Chips',
                'Organic Whole Milk',
                'Frozen Pizza Margherita',
                'Premium Coffee Beans',
                'Dark Chocolate Bar',
            ]),
            'category_id' => Category::factory()->state(['name' => 'Food & Beverages', 'slug' => 'food-beverages']),
            'brand_id' => Brand::factory()->state(['name' => $this->faker->randomElement(['Coca-Cola', 'PepsiCo', 'Nestlé', 'Generic'])]),
            'price' => $this->faker->randomFloat(2, 2, 50),
            'weight' => $this->faker->randomFloat(2, 0.1, 5),
        ]);
    }

    /**
     * Create a product with low stock scenario.
     */
    public function lowStock(): static
    {
        return $this->state(fn (array $attributes) => [
            'track_quantity' => true,
        ]);
    }

    /**
     * Create a product with barcode.
     */
    public function withBarcode(): static
    {
        return $this->state(fn (array $attributes) => [
            'barcode' => $this->faker->ean13(),
        ]);
    }

    /**
     * Create a product with image.
     */
    public function withImage(): static
    {
        return $this->state(fn (array $attributes) => [
            'image' => 'products/' . $this->faker->uuid() . '.jpg',
        ]);
    }

    /**
     * Create a product with gallery.
     */
    public function withGallery(): static
    {
        return $this->state(fn (array $attributes) => [
            'gallery' => [
                'products/' . $this->faker->uuid() . '.jpg',
                'products/' . $this->faker->uuid() . '.jpg',
                'products/' . $this->faker->uuid() . '.jpg',
            ],
        ]);
    }
}
