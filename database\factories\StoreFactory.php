<?php

namespace Database\Factories;

use App\Models\Store;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Store>
 */
class StoreFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Store::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->company() . ' Store',
            'code' => strtoupper($this->faker->unique()->lexify('???')),
            'address' => $this->faker->streetAddress(),
            'city' => $this->faker->city(),
            'state' => $this->faker->state(),
            'country' => $this->faker->country(),
            'postal_code' => $this->faker->postcode(),
            'phone' => $this->faker->phoneNumber(),
            'email' => $this->faker->unique()->safeEmail(),
            'is_active' => $this->faker->boolean(90),
            'settings' => [
                'currency' => 'USD',
                'timezone' => 'America/New_York',
                'tax' => [
                    'enabled' => $this->faker->boolean(70),
                    'name' => 'Sales Tax',
                    'rate' => $this->faker->randomFloat(2, 5, 15),
                    'inclusive' => $this->faker->boolean(30),
                ],
                'receipt' => [
                    'header' => $this->faker->optional()->sentence(),
                    'footer' => $this->faker->optional()->sentence(),
                    'show_logo' => $this->faker->boolean(60),
                    'show_barcode' => $this->faker->boolean(80),
                    'paper_size' => $this->faker->randomElement(['80mm', '58mm', 'A4']),
                    'auto_print' => $this->faker->boolean(40),
                ],
                'integrations' => [
                    'woocommerce' => [
                        'enabled' => false,
                        'url' => '',
                        'consumer_key' => '',
                        'consumer_secret' => '',
                    ],
                    'shopify' => [
                        'enabled' => false,
                        'store_url' => '',
                        'access_token' => '',
                    ],
                ],
            ],
        ];
    }

    /**
     * Indicate that the store is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
        ]);
    }

    /**
     * Indicate that the store is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Create a store with tax enabled.
     */
    public function withTax(): static
    {
        return $this->state(function (array $attributes) {
            $settings = $attributes['settings'] ?? [];
            $settings['tax'] = [
                'enabled' => true,
                'name' => 'Sales Tax',
                'rate' => $this->faker->randomFloat(2, 5, 15),
                'inclusive' => $this->faker->boolean(30),
            ];
            
            return ['settings' => $settings];
        });
    }

    /**
     * Create a store with WooCommerce integration.
     */
    public function withWooCommerce(): static
    {
        return $this->state(function (array $attributes) {
            $settings = $attributes['settings'] ?? [];
            $settings['integrations']['woocommerce'] = [
                'enabled' => true,
                'url' => $this->faker->url(),
                'consumer_key' => 'ck_' . $this->faker->uuid(),
                'consumer_secret' => 'cs_' . $this->faker->uuid(),
            ];
            
            return ['settings' => $settings];
        });
    }

    /**
     * Create a store with Shopify integration.
     */
    public function withShopify(): static
    {
        return $this->state(function (array $attributes) {
            $settings = $attributes['settings'] ?? [];
            $settings['integrations']['shopify'] = [
                'enabled' => true,
                'store_url' => 'https://' . $this->faker->domainWord() . '.myshopify.com',
                'access_token' => $this->faker->uuid(),
            ];
            
            return ['settings' => $settings];
        });
    }
}
