@echo off
echo ========================================
echo SyncPOS XAMPP Installation Script
echo ========================================
echo.

REM Check if we're in the right directory
if not exist "composer.json" (
    echo Error: composer.json not found. Please run this script from the SyncPOS root directory.
    pause
    exit /b 1
)

echo Step 1: Installing PHP dependencies...
composer install
if errorlevel 1 (
    echo Error: Composer install failed
    pause
    exit /b 1
)

echo.
echo Step 2: Installing Node.js dependencies...
npm install
if errorlevel 1 (
    echo Error: NPM install failed
    pause
    exit /b 1
)

echo.
echo Step 3: Setting up environment file...
if not exist ".env" (
    copy ".env.example" ".env"
    echo Environment file created from .env.example
) else (
    echo Environment file already exists
)

echo.
echo Step 4: Generating application key...
php artisan key:generate
if errorlevel 1 (
    echo Error: Failed to generate application key
    pause
    exit /b 1
)

echo.
echo Step 5: Building frontend assets...
npm run build
if errorlevel 1 (
    echo Error: Failed to build assets
    pause
    exit /b 1
)

echo.
echo Step 6: Setting up storage directories...
if not exist "storage\app\public" mkdir "storage\app\public"
if not exist "storage\framework\cache" mkdir "storage\framework\cache"
if not exist "storage\framework\sessions" mkdir "storage\framework\sessions"
if not exist "storage\framework\views" mkdir "storage\framework\views"
if not exist "storage\logs" mkdir "storage\logs"

echo.
echo ========================================
echo Installation completed successfully!
echo ========================================
echo.
echo Next steps:
echo 1. Configure your database in .env file
echo 2. Run: php artisan migrate
echo 3. Run: php artisan tenants:migrate
echo 4. Access your application at: http://localhost/syncpos/public
echo.
pause
