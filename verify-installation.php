<?php
/**
 * SyncPOS Installation Verification
 * Check if all dependencies and requirements are met
 */

echo "<h1>✅ SyncPOS Installation Verification</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .info { color: blue; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 12px; text-align: left; }
    th { background-color: #f2f2f2; }
    .button { background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px 5px; font-weight: bold; }
    .success-button { background: #28a745; }
    .card { background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; padding: 20px; margin: 15px 0; }
</style>";

echo "<div class='card'>";
echo "<h2>🔍 Checking Installation Status</h2>";

// Core Requirements Check
$checks = [
    'PHP Version >= 8.2' => [
        'status' => version_compare(PHP_VERSION, '8.2.0', '>='),
        'value' => PHP_VERSION,
        'required' => '8.2.0+'
    ],
    'Composer Dependencies' => [
        'status' => file_exists('vendor/autoload.php'),
        'value' => file_exists('vendor/autoload.php') ? 'Installed' : 'Missing',
        'required' => 'Required'
    ],
    'Laravel Framework' => [
        'status' => file_exists('vendor/laravel/framework'),
        'value' => file_exists('vendor/laravel/framework') ? 'Installed' : 'Missing',
        'required' => 'Required'
    ],
    'Environment File' => [
        'status' => file_exists('.env'),
        'value' => file_exists('.env') ? 'Exists' : 'Missing',
        'required' => 'Required'
    ],
    'Storage Directory Writable' => [
        'status' => is_writable('storage'),
        'value' => is_writable('storage') ? 'Writable' : 'Not Writable',
        'required' => 'Required'
    ],
    'Bootstrap Cache Writable' => [
        'status' => is_writable('bootstrap/cache'),
        'value' => is_writable('bootstrap/cache') ? 'Writable' : 'Not Writable',
        'required' => 'Required'
    ]
];

echo "<table>";
echo "<tr><th>Requirement</th><th>Status</th><th>Current Value</th><th>Required</th></tr>";

$allPassed = true;
foreach ($checks as $check => $data) {
    $statusIcon = $data['status'] ? "✅" : "❌";
    $statusClass = $data['status'] ? "success" : "error";
    
    echo "<tr>";
    echo "<td><strong>$check</strong></td>";
    echo "<td><span class='$statusClass'>$statusIcon " . ($data['status'] ? 'PASS' : 'FAIL') . "</span></td>";
    echo "<td>{$data['value']}</td>";
    echo "<td>{$data['required']}</td>";
    echo "</tr>";
    
    if (!$data['status']) {
        $allPassed = false;
    }
}
echo "</table>";
echo "</div>";

// Laravel-specific checks
echo "<div class='card'>";
echo "<h2>🚀 Laravel Framework Check</h2>";

if (file_exists('vendor/autoload.php')) {
    require_once 'vendor/autoload.php';
    
    $laravelChecks = [
        'Autoloader' => class_exists('Illuminate\Foundation\Application'),
        'Laravel Sanctum' => class_exists('Laravel\Sanctum\Sanctum'),
        'Spatie Permissions' => class_exists('Spatie\Permission\Models\Role'),
        'Stancl Tenancy' => class_exists('Stancl\Tenancy\Tenancy'),
        'Intervention Image' => class_exists('Intervention\Image\ImageManager'),
        'Maatwebsite Excel' => class_exists('Maatwebsite\Excel\Excel'),
        'DomPDF' => class_exists('Barryvdh\DomPDF\ServiceProvider'),
    ];
    
    echo "<table>";
    echo "<tr><th>Package</th><th>Status</th></tr>";
    
    foreach ($laravelChecks as $package => $exists) {
        $statusIcon = $exists ? "✅" : "❌";
        $statusClass = $exists ? "success" : "error";
        echo "<tr>";
        echo "<td><strong>$package</strong></td>";
        echo "<td><span class='$statusClass'>$statusIcon " . ($exists ? 'Available' : 'Missing') . "</span></td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p class='error'>❌ Composer autoloader not found. Dependencies not installed.</p>";
}
echo "</div>";

// Environment Check
echo "<div class='card'>";
echo "<h2>⚙️ Environment Configuration</h2>";

if (file_exists('.env')) {
    $envContent = file_get_contents('.env');
    $envChecks = [
        'APP_KEY Set' => strpos($envContent, 'APP_KEY=base64:') !== false,
        'APP_NAME Set' => strpos($envContent, 'APP_NAME=') !== false,
        'Database Config' => strpos($envContent, 'DB_DATABASE=') !== false,
    ];
    
    echo "<table>";
    echo "<tr><th>Configuration</th><th>Status</th></tr>";
    
    foreach ($envChecks as $config => $isSet) {
        $statusIcon = $isSet ? "✅" : "❌";
        $statusClass = $isSet ? "success" : "warning";
        echo "<tr>";
        echo "<td><strong>$config</strong></td>";
        echo "<td><span class='$statusClass'>$statusIcon " . ($isSet ? 'Configured' : 'Needs Setup') . "</span></td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p class='warning'>⚠️ .env file not found. Will be created during installation.</p>";
}
echo "</div>";

// Database Check
echo "<div class='card'>";
echo "<h2>🗄️ Database Connection Test</h2>";

if (file_exists('.env')) {
    $envContent = file_get_contents('.env');
    preg_match('/DB_HOST=(.*)/', $envContent, $hostMatch);
    preg_match('/DB_DATABASE=(.*)/', $envContent, $dbMatch);
    preg_match('/DB_USERNAME=(.*)/', $envContent, $userMatch);
    
    $host = $hostMatch[1] ?? '127.0.0.1';
    $database = $dbMatch[1] ?? 'syncpos';
    $username = $userMatch[1] ?? 'root';
    
    echo "<p><strong>Database Configuration:</strong></p>";
    echo "<ul>";
    echo "<li>Host: $host</li>";
    echo "<li>Database: $database</li>";
    echo "<li>Username: $username</li>";
    echo "</ul>";
    
    // Try to connect to MySQL (without selecting database)
    try {
        $pdo = new PDO("mysql:host=$host", $username, '');
        echo "<p class='success'>✅ MySQL connection successful</p>";
        
        // Check if database exists
        $stmt = $pdo->query("SHOW DATABASES LIKE '$database'");
        if ($stmt->rowCount() > 0) {
            echo "<p class='success'>✅ Database '$database' exists</p>";
        } else {
            echo "<p class='warning'>⚠️ Database '$database' does not exist (will be created during installation)</p>";
        }
    } catch (PDOException $e) {
        echo "<p class='error'>❌ Database connection failed: " . $e->getMessage() . "</p>";
        echo "<p class='info'>💡 Make sure MySQL is running in XAMPP</p>";
    }
} else {
    echo "<p class='info'>ℹ️ Database will be configured during installation</p>";
}
echo "</div>";

// Final Status and Next Steps
echo "<div class='card'>";
if ($allPassed && file_exists('vendor/autoload.php')) {
    echo "<h2 class='success'>🎉 Installation Ready!</h2>";
    echo "<p class='success'>All requirements are met. SyncPOS is ready for installation!</p>";
    
    echo "<div style='text-align: center; margin: 20px 0;'>";
    echo "<h3>Choose Installation Method:</h3>";
    echo "<a href='public/install' class='button success-button'>🚀 Web Installation (Recommended)</a>";
    echo "<a href='http://localhost:8000/install' class='button' onclick=\"alert('First run: php artisan serve')\">🖥️ Laravel Server</a>";
    echo "</div>";
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px;'>";
    echo "<h4>📋 Pre-Installation Checklist:</h4>";
    echo "<ul>";
    echo "<li>✅ PHP 8.2+ installed</li>";
    echo "<li>✅ Composer dependencies installed</li>";
    echo "<li>✅ File permissions set</li>";
    echo "<li>✅ Laravel framework loaded</li>";
    echo "<li>🔄 MySQL running (check XAMPP)</li>";
    echo "<li>🔄 Create 'syncpos' database (optional - can be done during installation)</li>";
    echo "</ul>";
    echo "</div>";
    
} else {
    echo "<h2 class='error'>❌ Installation Issues Found</h2>";
    echo "<p class='error'>Some requirements are not met. Please fix the issues above before proceeding.</p>";
    
    if (!file_exists('vendor/autoload.php')) {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px;'>";
        echo "<h4>🔧 Fix Dependencies:</h4>";
        echo "<p>Run this command in your SyncPOS directory:</p>";
        echo "<code style='background: #000; color: #0f0; padding: 10px; display: block;'>composer install --ignore-platform-reqs --optimize-autoloader</code>";
        echo "</div>";
    }
}
echo "</div>";

// System Information
echo "<div class='card'>";
echo "<h2>🖥️ System Information</h2>";
echo "<table>";
echo "<tr><th>Item</th><th>Value</th></tr>";
echo "<tr><td>PHP Version</td><td>" . PHP_VERSION . "</td></tr>";
echo "<tr><td>Operating System</td><td>" . PHP_OS . "</td></tr>";
echo "<tr><td>Server Software</td><td>" . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "</td></tr>";
echo "<tr><td>Document Root</td><td>" . ($_SERVER['DOCUMENT_ROOT'] ?? 'Unknown') . "</td></tr>";
echo "<tr><td>Current Directory</td><td>" . __DIR__ . "</td></tr>";
echo "<tr><td>Memory Limit</td><td>" . ini_get('memory_limit') . "</td></tr>";
echo "<tr><td>Max Execution Time</td><td>" . ini_get('max_execution_time') . "s</td></tr>";
echo "</table>";
echo "</div>";

echo "<hr>";
echo "<p style='text-align: center; color: #666;'>";
echo "<strong>SyncPOS Installation Verification</strong><br>";
echo "Comprehensive system check completed";
echo "</p>";
?>
