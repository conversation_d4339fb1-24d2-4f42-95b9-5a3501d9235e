<?php

namespace App\Services;

use App\Models\Product;
use App\Models\Store;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class WooCommerceService
{
    protected $baseUrl;
    protected $consumerKey;
    protected $consumerSecret;

    public function __construct()
    {
        $this->baseUrl = tenant('settings.woocommerce_store_url') ?? env('WOOCOMMERCE_STORE_URL');
        $this->consumerKey = tenant('settings.woocommerce_consumer_key') ?? env('WOOCOMMERCE_CONSUMER_KEY');
        $this->consumerSecret = tenant('settings.woocommerce_consumer_secret') ?? env('WOOCOMMERCE_CONSUMER_SECRET');
    }

    /**
     * Test connection to WooCommerce store
     */
    public function testConnection(): bool
    {
        try {
            $response = $this->makeRequest('GET', 'system_status');
            return $response->successful();
        } catch (\Exception $e) {
            Log::error('WooCommerce connection test failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Sync products from WooCommerce to local database
     */
    public function syncProductsFromWooCommerce(): array
    {
        $results = [
            'created' => 0,
            'updated' => 0,
            'errors' => []
        ];

        try {
            $page = 1;
            $perPage = 100;

            do {
                $response = $this->makeRequest('GET', 'products', [
                    'page' => $page,
                    'per_page' => $perPage,
                    'status' => 'publish'
                ]);

                if (!$response->successful()) {
                    throw new \Exception('Failed to fetch products from WooCommerce');
                }

                $products = $response->json();

                foreach ($products as $wcProduct) {
                    try {
                        $this->createOrUpdateProductFromWooCommerce($wcProduct, $results);
                    } catch (\Exception $e) {
                        $results['errors'][] = "Product {$wcProduct['name']}: " . $e->getMessage();
                    }
                }

                $page++;
            } while (count($products) === $perPage);

        } catch (\Exception $e) {
            $results['errors'][] = $e->getMessage();
            Log::error('WooCommerce sync error: ' . $e->getMessage());
        }

        return $results;
    }

    /**
     * Sync products from local database to WooCommerce
     */
    public function syncProductsToWooCommerce(): array
    {
        $results = [
            'created' => 0,
            'updated' => 0,
            'errors' => []
        ];

        $products = Product::where('sync_status', 'pending')
            ->orWhereNull('woocommerce_id')
            ->get();

        foreach ($products as $product) {
            try {
                if ($product->woocommerce_id) {
                    $this->updateProductInWooCommerce($product, $results);
                } else {
                    $this->createProductInWooCommerce($product, $results);
                }
            } catch (\Exception $e) {
                $results['errors'][] = "Product {$product->name}: " . $e->getMessage();
                $product->update(['sync_status' => 'failed']);
            }
        }

        return $results;
    }

    /**
     * Sync inventory levels to WooCommerce
     */
    public function syncInventoryToWooCommerce(Store $store = null): array
    {
        $results = [
            'updated' => 0,
            'errors' => []
        ];

        $query = Product::whereNotNull('woocommerce_id');
        
        if ($store) {
            $query->whereHas('stores', function ($q) use ($store) {
                $q->where('store_id', $store->id);
            });
        }

        $products = $query->get();

        foreach ($products as $product) {
            try {
                $totalStock = $store ? 
                    $product->getStockForStore($store) : 
                    $product->total_stock;

                $response = $this->makeRequest('PUT', "products/{$product->woocommerce_id}", [
                    'stock_quantity' => $totalStock,
                    'manage_stock' => true,
                    'in_stock' => $totalStock > 0
                ]);

                if ($response->successful()) {
                    $results['updated']++;
                } else {
                    throw new \Exception('Failed to update stock in WooCommerce');
                }

            } catch (\Exception $e) {
                $results['errors'][] = "Product {$product->name}: " . $e->getMessage();
            }
        }

        return $results;
    }

    /**
     * Create or update product from WooCommerce data
     */
    protected function createOrUpdateProductFromWooCommerce(array $wcProduct, array &$results): void
    {
        $product = Product::where('woocommerce_id', $wcProduct['id'])->first();

        $productData = [
            'name' => $wcProduct['name'],
            'description' => strip_tags($wcProduct['description']),
            'sku' => $wcProduct['sku'] ?: 'WC-' . $wcProduct['id'],
            'price' => $wcProduct['price'] ?: 0,
            'woocommerce_id' => $wcProduct['id'],
            'last_synced_at' => now(),
            'sync_status' => 'synced',
        ];

        // Handle images
        if (!empty($wcProduct['images'])) {
            $productData['image'] = $wcProduct['images'][0]['src'];
            $productData['images'] = array_column($wcProduct['images'], 'src');
        }

        if ($product) {
            $product->update($productData);
            $results['updated']++;
        } else {
            Product::create($productData);
            $results['created']++;
        }
    }

    /**
     * Create product in WooCommerce
     */
    protected function createProductInWooCommerce(Product $product, array &$results): void
    {
        $wcProductData = [
            'name' => $product->name,
            'description' => $product->description,
            'sku' => $product->sku,
            'regular_price' => (string) $product->price,
            'manage_stock' => true,
            'stock_quantity' => $product->total_stock,
            'in_stock' => $product->total_stock > 0,
            'status' => $product->is_active ? 'publish' : 'draft',
        ];

        // Add images
        if ($product->image) {
            $wcProductData['images'] = [
                ['src' => $product->image]
            ];
        }

        $response = $this->makeRequest('POST', 'products', $wcProductData);

        if ($response->successful()) {
            $wcProduct = $response->json();
            $product->update([
                'woocommerce_id' => $wcProduct['id'],
                'last_synced_at' => now(),
                'sync_status' => 'synced',
            ]);
            $results['created']++;
        } else {
            throw new \Exception('Failed to create product in WooCommerce: ' . $response->body());
        }
    }

    /**
     * Update product in WooCommerce
     */
    protected function updateProductInWooCommerce(Product $product, array &$results): void
    {
        $wcProductData = [
            'name' => $product->name,
            'description' => $product->description,
            'sku' => $product->sku,
            'regular_price' => (string) $product->price,
            'manage_stock' => true,
            'stock_quantity' => $product->total_stock,
            'in_stock' => $product->total_stock > 0,
            'status' => $product->is_active ? 'publish' : 'draft',
        ];

        $response = $this->makeRequest('PUT', "products/{$product->woocommerce_id}", $wcProductData);

        if ($response->successful()) {
            $product->update([
                'last_synced_at' => now(),
                'sync_status' => 'synced',
            ]);
            $results['updated']++;
        } else {
            throw new \Exception('Failed to update product in WooCommerce: ' . $response->body());
        }
    }

    /**
     * Make HTTP request to WooCommerce API
     */
    protected function makeRequest(string $method, string $endpoint, array $data = [])
    {
        $url = rtrim($this->baseUrl, '/') . '/wp-json/wc/v3/' . $endpoint;

        $request = Http::withBasicAuth($this->consumerKey, $this->consumerSecret)
            ->timeout(30);

        switch (strtoupper($method)) {
            case 'GET':
                return $request->get($url, $data);
            case 'POST':
                return $request->post($url, $data);
            case 'PUT':
                return $request->put($url, $data);
            case 'DELETE':
                return $request->delete($url);
            default:
                throw new \InvalidArgumentException("Unsupported HTTP method: {$method}");
        }
    }
}
