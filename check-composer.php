<?php
/**
 * Composer Installation Check
 */

echo "<h1>🎼 Composer Installation Check</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .info { color: blue; }
    .code { background: #f0f0f0; padding: 10px; border-radius: 5px; margin: 10px 0; }
    .terminal { background: #000; color: #0f0; padding: 10px; border-radius: 5px; margin: 10px 0; font-family: monospace; }
</style>";

echo "<h2>🔍 Checking Composer Installation...</h2>";

// Method 1: Check via command line
echo "<h3>Method 1: Command Line Check</h3>";
exec('composer --version 2>&1', $output, $return_code);

if ($return_code === 0) {
    echo "<p class='success'>✅ Composer is installed and accessible via command line!</p>";
    echo "<div class='terminal'>" . htmlspecialchars(implode("\n", $output)) . "</div>";
    
    // Check if we can run composer commands
    echo "<h3>Testing Composer Commands...</h3>";
    
    // Test composer help
    exec('composer help 2>&1', $helpOutput, $helpReturn);
    if ($helpReturn === 0) {
        echo "<p class='success'>✅ Composer commands are working</p>";
    } else {
        echo "<p class='error'>❌ Composer commands are not working properly</p>";
    }
    
} else {
    echo "<p class='error'>❌ Composer is not installed or not in PATH</p>";
    echo "<div class='terminal'>" . htmlspecialchars(implode("\n", $output)) . "</div>";
}

// Method 2: Check for composer.phar in current directory
echo "<h3>Method 2: Local Composer Check</h3>";
if (file_exists('composer.phar')) {
    echo "<p class='success'>✅ composer.phar found in current directory</p>";
    
    // Test local composer
    exec('php composer.phar --version 2>&1', $localOutput, $localReturn);
    if ($localReturn === 0) {
        echo "<p class='success'>✅ Local composer.phar is working</p>";
        echo "<div class='terminal'>" . htmlspecialchars(implode("\n", $localOutput)) . "</div>";
    } else {
        echo "<p class='error'>❌ Local composer.phar is not working</p>";
    }
} else {
    echo "<p class='warning'>⚠️ composer.phar not found in current directory</p>";
}

// Method 3: Check if dependencies are already installed
echo "<h3>Method 3: Dependencies Check</h3>";
if (file_exists('vendor/autoload.php')) {
    echo "<p class='success'>✅ Composer dependencies are already installed!</p>";
    echo "<p class='info'>The vendor/autoload.php file exists, which means Laravel dependencies are available.</p>";
    
    // Check some key Laravel files
    $laravelFiles = [
        'vendor/laravel/framework/src/Illuminate/Foundation/Application.php',
        'vendor/autoload.php',
        'vendor/composer/autoload_real.php'
    ];
    
    echo "<h4>Key Laravel Files:</h4>";
    foreach ($laravelFiles as $file) {
        if (file_exists($file)) {
            echo "<p class='success'>✅ " . htmlspecialchars($file) . "</p>";
        } else {
            echo "<p class='error'>❌ " . htmlspecialchars($file) . "</p>";
        }
    }
    
} else {
    echo "<p class='error'>❌ Composer dependencies are NOT installed</p>";
    echo "<p class='info'>The vendor directory is missing or incomplete.</p>";
}

// Summary and next steps
echo "<hr>";
echo "<h2>📋 Summary & Next Steps</h2>";

if (file_exists('vendor/autoload.php')) {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px;'>";
    echo "<h3 class='success'>🎉 Ready to Proceed!</h3>";
    echo "<p>Dependencies are installed. You can now run the SyncPOS installation.</p>";
    echo "<p><a href='public/install' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🚀 Start SyncPOS Installation</a></p>";
    echo "</div>";
    
} elseif ($return_code === 0) {
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px;'>";
    echo "<h3 class='warning'>⚠️ Composer Available - Dependencies Needed</h3>";
    echo "<p>Composer is installed but dependencies need to be installed.</p>";
    echo "<p><strong>Run this command:</strong></p>";
    echo "<div class='terminal'>cd " . __DIR__ . " && composer install</div>";
    echo "<p>Or click: <a href='install-dependencies.php'>Auto-install dependencies</a></p>";
    echo "</div>";
    
} else {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px;'>";
    echo "<h3 class='error'>❌ Composer Not Found</h3>";
    echo "<p>Composer needs to be installed first.</p>";
    echo "<h4>Installation Options:</h4>";
    echo "<ol>";
    echo "<li><strong>Windows Installer:</strong> <a href='https://getcomposer.org/Composer-Setup.exe' target='_blank'>Download Composer-Setup.exe</a></li>";
    echo "<li><strong>Manual Download:</strong> <a href='https://getcomposer.org/composer.phar' target='_blank'>Download composer.phar</a> and place it in this directory</li>";
    echo "</ol>";
    echo "</div>";
}

// Additional system info
echo "<h3>🖥️ System Information</h3>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th style='padding: 8px; background: #f0f0f0;'>Item</th><th style='padding: 8px; background: #f0f0f0;'>Value</th></tr>";
echo "<tr><td style='padding: 8px;'>PHP Version</td><td style='padding: 8px;'>" . PHP_VERSION . "</td></tr>";
echo "<tr><td style='padding: 8px;'>Operating System</td><td style='padding: 8px;'>" . PHP_OS . "</td></tr>";
echo "<tr><td style='padding: 8px;'>Current Directory</td><td style='padding: 8px;'>" . __DIR__ . "</td></tr>";
echo "<tr><td style='padding: 8px;'>PHP Executable</td><td style='padding: 8px;'>" . PHP_BINARY . "</td></tr>";
echo "</table>";

echo "<hr>";
echo "<p style='text-align: center; color: #666;'>";
echo "<strong>SyncPOS Composer Check</strong><br>";
echo "Refresh this page after installing Composer or dependencies";
echo "</p>";
?>
