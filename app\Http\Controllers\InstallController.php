<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Store;
use App\Models\Category;
use App\Models\CustomerGroup;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rules\Password;
use Spatie\Permission\Models\Role;

class InstallController extends Controller
{
    public function index()
    {
        // Check if already installed
        if ($this->isInstalled()) {
            return redirect('/')->with('error', 'Application is already installed.');
        }

        return view('install.index');
    }

    public function process(Request $request)
    {
        // Check if already installed
        if ($this->isInstalled()) {
            return redirect('/')->with('error', 'Application is already installed.');
        }

        $step = $request->input('step', 1);

        switch ($step) {
            case 1:
                return $this->processRequirements();
            case 2:
                return $this->processDatabase($request);
            case 3:
                return $this->processApplication($request);
            default:
                return redirect()->route('install.index');
        }
    }

    public function complete()
    {
        if (!$this->isInstalled()) {
            return redirect()->route('install.index');
        }

        return view('install.complete');
    }

    private function processRequirements()
    {
        $requirements = $this->checkRequirements();
        
        if (!$requirements['all_passed']) {
            return back()->with('error', 'Some system requirements are not met. Please fix them before continuing.');
        }

        return redirect()->route('install.index', ['step' => 2]);
    }

    private function processDatabase(Request $request)
    {
        $validated = $request->validate([
            'db_host' => 'required|string',
            'db_port' => 'required|integer|min:1|max:65535',
            'db_name' => 'required|string',
            'db_user' => 'required|string',
            'db_pass' => 'nullable|string',
        ]);

        // Handle test connection request
        if ($request->has('test_connection')) {
            try {
                $this->testDatabaseConnection($validated);
                return response()->json(['success' => true, 'message' => 'Database connection successful']);
            } catch (\Exception $e) {
                return response()->json(['success' => false, 'message' => $e->getMessage()]);
            }
        }

        try {
            // Test database connection
            $this->testDatabaseConnection($validated);

            // Store database config in session
            session(['db_config' => $validated]);

            return redirect()->route('install.index', ['step' => 3]);

        } catch (\Exception $e) {
            return back()->withInput()->with('error', 'Database connection failed: ' . $e->getMessage());
        }
    }

    private function processApplication(Request $request)
    {
        $validated = $request->validate([
            'app_name' => 'required|string|max:255',
            'app_url' => 'required|url',
            'app_env' => 'required|in:local,production',
            'admin_name' => 'required|string|max:255',
            'admin_email' => 'required|email|max:255',
            'admin_password' => ['required', 'confirmed', Password::min(8)],
            'store_name' => 'required|string|max:255',
            'store_email' => 'required|email|max:255',
            'store_phone' => 'nullable|string|max:20',
        ]);

        $dbConfig = session('db_config');
        if (!$dbConfig) {
            return redirect()->route('install.index', ['step' => 2])
                ->with('error', 'Database configuration not found. Please configure database first.');
        }

        try {
            DB::beginTransaction();

            // Create .env file
            $this->createEnvFile($validated, $dbConfig);

            // Clear config cache
            if (file_exists(base_path('bootstrap/cache/config.php'))) {
                unlink(base_path('bootstrap/cache/config.php'));
            }

            // Run migrations
            Artisan::call('migrate', ['--force' => true]);

            // Create roles and permissions
            $this->createRolesAndPermissions();

            // Create admin user
            $user = $this->createAdminUser($validated);

            // Create default store
            $store = $this->createDefaultStore($validated, $user);

            // Create sample data
            $this->createSampleData();

            // Mark as installed
            $this->markAsInstalled();

            DB::commit();

            // Clear session
            session()->forget('db_config');

            return redirect()->route('install.complete');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->withInput()->with('error', 'Installation failed: ' . $e->getMessage());
        }
    }

    private function checkRequirements(): array
    {
        $requirements = [
            'PHP Version >= 8.2' => version_compare(PHP_VERSION, '8.2.0', '>='),
            'PDO Extension' => extension_loaded('pdo'),
            'PDO MySQL Extension' => extension_loaded('pdo_mysql'),
            'OpenSSL Extension' => extension_loaded('openssl'),
            'Mbstring Extension' => extension_loaded('mbstring'),
            'Tokenizer Extension' => extension_loaded('tokenizer'),
            'XML Extension' => extension_loaded('xml'),
            'Ctype Extension' => extension_loaded('ctype'),
            'JSON Extension' => extension_loaded('json'),
            'BCMath Extension' => extension_loaded('bcmath'),
            'Fileinfo Extension' => extension_loaded('fileinfo'),
            'GD Extension' => extension_loaded('gd'),
            'Zip Extension' => extension_loaded('zip'),
        ];

        $permissions = [
            'storage/ directory' => is_writable(storage_path()),
            'bootstrap/cache/ directory' => is_writable(base_path('bootstrap/cache')),
            '.env file' => is_writable(base_path()) || is_writable(base_path('.env')),
        ];

        $allPassed = true;
        foreach (array_merge($requirements, $permissions) as $requirement => $passed) {
            $allPassed = $allPassed && $passed;
        }

        return [
            'requirements' => $requirements,
            'permissions' => $permissions,
            'all_passed' => $allPassed,
        ];
    }

    private function testDatabaseConnection(array $config): void
    {
        $dsn = "mysql:host={$config['db_host']};port={$config['db_port']};charset=utf8mb4";
        $pdo = new \PDO($dsn, $config['db_user'], $config['db_pass'], [
            \PDO::ATTR_ERRMODE => \PDO::ERRMODE_EXCEPTION,
        ]);

        // Check if database exists, create if not
        $stmt = $pdo->query("SHOW DATABASES LIKE '{$config['db_name']}'");
        if ($stmt->rowCount() == 0) {
            $pdo->exec("CREATE DATABASE `{$config['db_name']}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        }
    }

    private function createEnvFile(array $appConfig, array $dbConfig): void
    {
        $appKey = 'base64:' . base64_encode(random_bytes(32));
        
        $envContent = "APP_NAME=\"{$appConfig['app_name']}\"
APP_ENV={$appConfig['app_env']}
APP_KEY={$appKey}
APP_DEBUG=" . ($appConfig['app_env'] === 'local' ? 'true' : 'false') . "
APP_TIMEZONE=UTC
APP_URL={$appConfig['app_url']}

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=" . ($appConfig['app_env'] === 'local' ? 'debug' : 'error') . "

DB_CONNECTION=mysql
DB_HOST={$dbConfig['db_host']}
DB_PORT={$dbConfig['db_port']}
DB_DATABASE={$dbConfig['db_name']}
DB_USERNAME={$dbConfig['db_user']}
DB_PASSWORD={$dbConfig['db_pass']}

SESSION_DRIVER=file
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync

CACHE_STORE=file
CACHE_PREFIX=

MAIL_MAILER=log
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS=\"noreply@{$appConfig['app_name']}.com\"
MAIL_FROM_NAME=\"{$appConfig['app_name']}\"

VITE_APP_NAME=\"{$appConfig['app_name']}\"
";

        file_put_contents(base_path('.env'), $envContent);
    }

    private function createRolesAndPermissions(): void
    {
        if (class_exists(Role::class)) {
            Role::firstOrCreate(['name' => 'admin']);
            Role::firstOrCreate(['name' => 'manager']);
            Role::firstOrCreate(['name' => 'cashier']);
        }
    }

    private function createAdminUser(array $config): User
    {
        $user = User::create([
            'name' => $config['admin_name'],
            'email' => $config['admin_email'],
            'password' => Hash::make($config['admin_password']),
            'email_verified_at' => now(),
            'is_active' => true,
        ]);

        if (class_exists(Role::class)) {
            $user->assignRole('admin');
        }

        return $user;
    }

    private function createDefaultStore(array $config, User $user): Store
    {
        $store = Store::create([
            'name' => $config['store_name'],
            'code' => 'MAIN',
            'address' => '123 Main Street',
            'city' => 'Your City',
            'state' => 'Your State',
            'country' => 'Your Country',
            'postal_code' => '12345',
            'phone' => $config['store_phone'] ?? '(*************',
            'email' => $config['store_email'],
            'is_active' => true,
        ]);

        // Assign user to store
        $store->users()->attach($user->id, ['is_primary' => true]);

        return $store;
    }

    private function createSampleData(): void
    {
        // Create sample categories
        $categories = [
            ['name' => 'Electronics', 'slug' => 'electronics'],
            ['name' => 'Clothing', 'slug' => 'clothing'],
            ['name' => 'Food & Beverages', 'slug' => 'food-beverages'],
            ['name' => 'Books', 'slug' => 'books'],
        ];

        foreach ($categories as $categoryData) {
            Category::create($categoryData + ['is_active' => true]);
        }

        // Create default customer group
        CustomerGroup::create([
            'name' => 'Regular Customers',
            'description' => 'Default customer group',
            'discount_percentage' => 0,
            'is_active' => true,
        ]);
    }

    private function markAsInstalled(): void
    {
        Storage::put('installed', now()->toDateTimeString());
    }

    private function isInstalled(): bool
    {
        return Storage::exists('installed') && file_exists(base_path('.env'));
    }
}
