<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Store;
use App\Models\Product;
use App\Models\Category;
use App\Models\Brand;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class ProductManagementTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected User $user;
    protected Store $store;
    protected Category $category;
    protected Brand $brand;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->store = Store::factory()->create();
        $this->category = Category::factory()->create();
        $this->brand = Brand::factory()->create();

        // Assign user to store
        $this->store->users()->attach($this->user->id, ['is_primary' => true]);

        // Create roles if using Spatie Permission
        if (class_exists(\Spatie\Permission\Models\Role::class)) {
            $adminRole = \Spatie\Permission\Models\Role::create(['name' => 'admin']);
            $this->user->assignRole($adminRole);
        }
    }

    public function test_user_can_view_products_index()
    {
        $products = Product::factory(3)->create();

        // Attach products to store
        foreach ($products as $product) {
            $this->store->products()->attach($product->id, [
                'stock_quantity' => 50,
                'min_stock_level' => 10,
                'max_stock_level' => 100,
            ]);
        }

        $response = $this->actingAs($this->user)
            ->get(route('products.index'));

        $response->assertStatus(200);
        $response->assertViewIs('products.index');
        $response->assertViewHas('products');
    }

    public function test_user_can_create_product()
    {
        Storage::fake('public');

        $productData = [
            'name' => 'Test Product',
            'sku' => 'TEST-001',
            'barcode' => '1234567890123',
            'description' => 'This is a test product',
            'price' => 99.99,
            'cost_price' => 50.00,
            'category_id' => $this->category->id,
            'brand_id' => $this->brand->id,
            'weight' => 1.5,
            'is_active' => true,
            'track_quantity' => true,
            'allow_backorder' => false,
            'image' => UploadedFile::fake()->image('product.jpg'),
            'stores' => [
                [
                    'store_id' => $this->store->id,
                    'stock_quantity' => 100,
                    'min_stock_level' => 10,
                    'max_stock_level' => 200,
                ]
            ]
        ];

        $response = $this->actingAs($this->user)
            ->post(route('products.store'), $productData);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('products', [
            'name' => 'Test Product',
            'sku' => 'TEST-001',
            'price' => 99.99,
            'cost_price' => 50.00,
        ]);

        $product = Product::where('sku', 'TEST-001')->first();
        $this->assertNotNull($product);

        // Check store association
        $this->assertTrue($product->stores->contains($this->store));
        $pivot = $product->stores->where('id', $this->store->id)->first()->pivot;
        $this->assertEquals(100, $pivot->stock_quantity);
        $this->assertEquals(10, $pivot->min_stock_level);
        $this->assertEquals(200, $pivot->max_stock_level);

        // Check image upload
        Storage::disk('public')->assertExists($product->image);
    }

    public function test_user_can_update_product()
    {
        $product = Product::factory()->create([
            'category_id' => $this->category->id,
            'brand_id' => $this->brand->id,
        ]);

        $this->store->products()->attach($product->id, [
            'stock_quantity' => 50,
            'min_stock_level' => 10,
            'max_stock_level' => 100,
        ]);

        $updateData = [
            'name' => 'Updated Product Name',
            'sku' => $product->sku,
            'price' => 149.99,
            'cost_price' => 75.00,
            'category_id' => $this->category->id,
            'brand_id' => $this->brand->id,
            'is_active' => true,
            'track_quantity' => true,
            'allow_backorder' => false,
        ];

        $response = $this->actingAs($this->user)
            ->put(route('products.update', $product), $updateData);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('products', [
            'id' => $product->id,
            'name' => 'Updated Product Name',
            'price' => 149.99,
            'cost_price' => 75.00,
        ]);
    }

    public function test_user_can_delete_product()
    {
        $product = Product::factory()->create([
            'category_id' => $this->category->id,
            'brand_id' => $this->brand->id,
        ]);

        $response = $this->actingAs($this->user)
            ->delete(route('products.destroy', $product));

        $response->assertRedirect();
        $response->assertSessionHas('success');

        $this->assertSoftDeleted('products', ['id' => $product->id]);
    }

    public function test_product_validation_rules()
    {
        $response = $this->actingAs($this->user)
            ->post(route('products.store'), []);

        $response->assertSessionHasErrors([
            'name',
            'sku',
            'price',
            'cost_price',
            'category_id',
        ]);
    }

    public function test_duplicate_sku_validation()
    {
        $existingProduct = Product::factory()->create(['sku' => 'DUPLICATE-SKU']);

        $response = $this->actingAs($this->user)
            ->post(route('products.store'), [
                'name' => 'Test Product',
                'sku' => 'DUPLICATE-SKU',
                'price' => 99.99,
                'cost_price' => 50.00,
                'category_id' => $this->category->id,
            ]);

        $response->assertSessionHasErrors(['sku']);
    }

    public function test_user_can_search_products()
    {
        $product1 = Product::factory()->create(['name' => 'iPhone 15']);
        $product2 = Product::factory()->create(['name' => 'Samsung Galaxy']);
        $product3 = Product::factory()->create(['name' => 'MacBook Pro']);

        foreach ([$product1, $product2, $product3] as $product) {
            $this->store->products()->attach($product->id, [
                'stock_quantity' => 50,
                'min_stock_level' => 10,
                'max_stock_level' => 100,
            ]);
        }

        $response = $this->actingAs($this->user)
            ->get(route('products.index', ['search' => 'iPhone']));

        $response->assertStatus(200);
        $response->assertSee('iPhone 15');
        $response->assertDontSee('Samsung Galaxy');
        $response->assertDontSee('MacBook Pro');
    }

    public function test_user_can_filter_products_by_category()
    {
        $category2 = Category::factory()->create();
        
        $product1 = Product::factory()->create(['category_id' => $this->category->id]);
        $product2 = Product::factory()->create(['category_id' => $category2->id]);

        foreach ([$product1, $product2] as $product) {
            $this->store->products()->attach($product->id, [
                'stock_quantity' => 50,
                'min_stock_level' => 10,
                'max_stock_level' => 100,
            ]);
        }

        $response = $this->actingAs($this->user)
            ->get(route('products.index', ['category_id' => $this->category->id]));

        $response->assertStatus(200);
        $response->assertSee($product1->name);
        $response->assertDontSee($product2->name);
    }

    public function test_low_stock_products_are_identified()
    {
        $product = Product::factory()->create([
            'category_id' => $this->category->id,
            'brand_id' => $this->brand->id,
        ]);

        // Set stock below minimum level
        $this->store->products()->attach($product->id, [
            'stock_quantity' => 5,
            'min_stock_level' => 10,
            'max_stock_level' => 100,
        ]);

        $lowStockProducts = $this->store->getLowStockProducts();
        
        $this->assertTrue($lowStockProducts->contains($product));
    }
}
