/* SyncPOS Installer Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #374151;
    line-height: 1.6;
}

.container {
    max-width: 700px;
    margin: 2rem auto;
    padding: 0 1rem;
}

.installer-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    overflow: hidden;
    animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 3rem 2rem;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

.header h1 {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
    font-weight: 700;
    position: relative;
    z-index: 1;
}

.header p {
    opacity: 0.9;
    font-size: 1.1rem;
    position: relative;
    z-index: 1;
}

.content {
    padding: 2.5rem;
}

.step-indicator {
    display: flex;
    justify-content: center;
    margin-bottom: 2.5rem;
    position: relative;
}

.step-indicator::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 20%;
    right: 20%;
    height: 2px;
    background: #e5e7eb;
    z-index: 0;
}

.step {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 1rem;
    font-weight: bold;
    font-size: 1.1rem;
    position: relative;
    z-index: 1;
    transition: all 0.3s ease;
}

.step.active {
    background: #667eea;
    color: white;
    box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.2);
    transform: scale(1.1);
}

.step.completed {
    background: #10b981;
    color: white;
    transform: scale(1.05);
}

.step.pending {
    background: #f3f4f6;
    color: #9ca3af;
    border: 2px solid #e5e7eb;
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: #f3f4f6;
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 2.5rem;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    transition: width 0.5s ease;
    border-radius: 3px;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #374151;
    font-size: 0.95rem;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 0.875rem 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.2s ease;
    background: #fafafa;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group .small {
    font-size: 0.8rem;
    color: #6b7280;
    margin-top: 0.25rem;
    display: block;
}

.btn {
    display: inline-block;
    padding: 0.875rem 2rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-decoration: none;
    border-radius: 10px;
    border: none;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px -3px rgba(0, 0, 0, 0.1);
}

.btn:active {
    transform: translateY(0);
}

.btn-secondary {
    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
}

.alert {
    padding: 1rem 1.25rem;
    border-radius: 10px;
    margin-bottom: 1.5rem;
    border-left: 4px solid;
    font-weight: 500;
}

.alert-error {
    background: #fef2f2;
    color: #dc2626;
    border-left-color: #dc2626;
}

.alert-success {
    background: #f0fdf4;
    color: #16a34a;
    border-left-color: #16a34a;
}

.alert-info {
    background: #eff6ff;
    color: #2563eb;
    border-left-color: #2563eb;
}

.requirements {
    list-style: none;
    background: #f9fafb;
    border-radius: 10px;
    padding: 1.5rem;
    margin: 1.5rem 0;
}

.requirements li {
    padding: 0.75rem 0;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #e5e7eb;
}

.requirements li:last-child {
    border-bottom: none;
}

.requirements .check {
    margin-right: 1rem;
    font-weight: bold;
    font-size: 1.1rem;
    width: 20px;
    text-align: center;
}

.requirements .pass {
    color: #16a34a;
}

.requirements .fail {
    color: #dc2626;
}

.text-center {
    text-align: center;
}

.mt-2 {
    margin-top: 1.5rem;
}

.installation-complete {
    text-align: center;
    padding: 2rem 0;
}

.installation-complete h2 {
    color: #16a34a;
    margin-bottom: 1rem;
    font-size: 2rem;
}

.next-steps {
    background: #f9fafb;
    border-radius: 10px;
    padding: 1.5rem;
    margin: 1.5rem 0;
    text-align: left;
}

.next-steps h3 {
    color: #374151;
    margin-bottom: 1rem;
}

.next-steps ul {
    margin: 1rem 0;
    padding-left: 1.5rem;
}

.next-steps li {
    margin: 0.5rem 0;
    color: #6b7280;
}

.button-group {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

@media (max-width: 640px) {
    .container {
        margin: 1rem auto;
        padding: 0 0.5rem;
    }
    
    .content {
        padding: 1.5rem;
    }
    
    .header {
        padding: 2rem 1.5rem;
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    .step {
        width: 40px;
        height: 40px;
        margin: 0 0.5rem;
        font-size: 1rem;
    }
    
    .button-group {
        flex-direction: column;
    }
    
    .btn {
        width: 100%;
        text-align: center;
    }
}
