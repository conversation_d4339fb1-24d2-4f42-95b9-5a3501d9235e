<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ProductVariant extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'product_id',
        'name',
        'sku',
        'barcode',
        'price',
        'cost_price',
        'weight',
        'dimensions',
        'image',
        'is_active',
        'attributes',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'cost_price' => 'decimal:2',
        'weight' => 'decimal:2',
        'is_active' => 'boolean',
        'dimensions' => 'array',
        'attributes' => 'array',
    ];

    /**
     * Get the parent product.
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Get the stores that stock this variant.
     */
    public function stores()
    {
        return $this->belongsToMany(Store::class, 'store_product_variants')
            ->withPivot('stock_quantity', 'min_stock_level', 'max_stock_level')
            ->withTimestamps();
    }

    /**
     * Get the sale items for this variant.
     */
    public function saleItems()
    {
        return $this->hasMany(SaleItem::class, 'product_variant_id');
    }

    /**
     * Scope for active variants.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Get stock for a specific store.
     */
    public function getStockForStore(Store $store): int
    {
        $pivot = $this->stores()->where('store_id', $store->id)->first()?->pivot;
        return $pivot ? $pivot->stock_quantity : 0;
    }

    /**
     * Check if variant is in stock for a store.
     */
    public function isInStock(Store $store, int $quantity = 1): bool
    {
        return $this->getStockForStore($store) >= $quantity;
    }

    /**
     * Get the full name including parent product.
     */
    public function getFullNameAttribute(): string
    {
        return $this->product->name . ' - ' . $this->name;
    }
}
