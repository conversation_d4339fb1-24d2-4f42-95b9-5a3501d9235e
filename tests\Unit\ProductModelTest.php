<?php

namespace Tests\Unit;

use App\Models\Product;
use App\Models\Store;
use App\Models\Category;
use App\Models\Brand;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ProductModelTest extends TestCase
{
    use RefreshDatabase;

    protected Product $product;
    protected Store $store;

    protected function setUp(): void
    {
        parent::setUp();

        $this->product = Product::factory()->create([
            'category_id' => Category::factory()->create()->id,
            'brand_id' => Brand::factory()->create()->id,
            'price' => 100.00,
            'cost_price' => 60.00,
        ]);

        $this->store = Store::factory()->create();
    }

    public function test_product_has_category_relationship()
    {
        $this->assertInstanceOf(Category::class, $this->product->category);
    }

    public function test_product_has_brand_relationship()
    {
        $this->assertInstanceOf(Brand::class, $this->product->brand);
    }

    public function test_product_has_stores_relationship()
    {
        $this->store->products()->attach($this->product->id, [
            'stock_quantity' => 50,
            'min_stock_level' => 10,
            'max_stock_level' => 100,
        ]);

        $this->assertTrue($this->product->stores->contains($this->store));
    }

    public function test_product_profit_margin_calculation()
    {
        $expectedMargin = (($this->product->price - $this->product->cost_price) / $this->product->price) * 100;
        
        $this->assertEquals($expectedMargin, $this->product->profit_margin);
        $this->assertEquals(40.0, $this->product->profit_margin); // (100-60)/100 * 100 = 40%
    }

    public function test_product_profit_amount_calculation()
    {
        $expectedProfit = $this->product->price - $this->product->cost_price;
        
        $this->assertEquals($expectedProfit, $this->product->profit_amount);
        $this->assertEquals(40.0, $this->product->profit_amount); // 100 - 60 = 40
    }

    public function test_product_is_in_stock_check()
    {
        $this->store->products()->attach($this->product->id, [
            'stock_quantity' => 50,
            'min_stock_level' => 10,
            'max_stock_level' => 100,
        ]);

        $this->assertTrue($this->product->isInStock($this->store, 30));
        $this->assertTrue($this->product->isInStock($this->store, 50));
        $this->assertFalse($this->product->isInStock($this->store, 51));
    }

    public function test_product_is_low_stock_check()
    {
        $this->store->products()->attach($this->product->id, [
            'stock_quantity' => 5,
            'min_stock_level' => 10,
            'max_stock_level' => 100,
        ]);

        $this->assertTrue($this->product->isLowStock($this->store));

        // Update stock above minimum
        $this->store->products()->updateExistingPivot($this->product->id, [
            'stock_quantity' => 15,
        ]);

        $this->assertFalse($this->product->isLowStock($this->store));
    }

    public function test_product_scope_active()
    {
        $activeProduct = Product::factory()->create(['is_active' => true]);
        $inactiveProduct = Product::factory()->create(['is_active' => false]);

        $activeProducts = Product::active()->get();

        $this->assertTrue($activeProducts->contains($activeProduct));
        $this->assertFalse($activeProducts->contains($inactiveProduct));
    }

    public function test_product_scope_search()
    {
        $product1 = Product::factory()->create(['name' => 'iPhone 15 Pro']);
        $product2 = Product::factory()->create(['name' => 'Samsung Galaxy S24']);
        $product3 = Product::factory()->create(['sku' => 'IPHONE-15-PRO']);

        $searchResults = Product::search('iPhone')->get();

        $this->assertTrue($searchResults->contains($product1));
        $this->assertTrue($searchResults->contains($product3));
        $this->assertFalse($searchResults->contains($product2));
    }

    public function test_product_scope_by_category()
    {
        $category1 = Category::factory()->create();
        $category2 = Category::factory()->create();

        $product1 = Product::factory()->create(['category_id' => $category1->id]);
        $product2 = Product::factory()->create(['category_id' => $category2->id]);

        $categoryProducts = Product::byCategory($category1->id)->get();

        $this->assertTrue($categoryProducts->contains($product1));
        $this->assertFalse($categoryProducts->contains($product2));
    }

    public function test_product_scope_by_brand()
    {
        $brand1 = Brand::factory()->create();
        $brand2 = Brand::factory()->create();

        $product1 = Product::factory()->create(['brand_id' => $brand1->id]);
        $product2 = Product::factory()->create(['brand_id' => $brand2->id]);

        $brandProducts = Product::byBrand($brand1->id)->get();

        $this->assertTrue($brandProducts->contains($product1));
        $this->assertFalse($brandProducts->contains($product2));
    }

    public function test_product_scope_low_stock()
    {
        $this->store->products()->attach($this->product->id, [
            'stock_quantity' => 5,
            'min_stock_level' => 10,
            'max_stock_level' => 100,
        ]);

        $lowStockProducts = Product::lowStock($this->store)->get();

        $this->assertTrue($lowStockProducts->contains($this->product));
    }

    public function test_product_formatted_price_attribute()
    {
        $this->assertEquals('$100.00', $this->product->formatted_price);
    }

    public function test_product_formatted_cost_price_attribute()
    {
        $this->assertEquals('$60.00', $this->product->formatted_cost_price);
    }

    public function test_product_image_url_attribute()
    {
        // Test with no image
        $this->assertNull($this->product->image_url);

        // Test with image
        $this->product->update(['image' => 'products/test-image.jpg']);
        $this->assertStringContains('products/test-image.jpg', $this->product->image_url);
    }

    public function test_product_can_be_soft_deleted()
    {
        $productId = $this->product->id;
        
        $this->product->delete();
        
        $this->assertSoftDeleted('products', ['id' => $productId]);
        
        // Verify it's not in normal queries
        $this->assertNull(Product::find($productId));
        
        // Verify it's in trashed queries
        $this->assertNotNull(Product::withTrashed()->find($productId));
    }

    public function test_product_can_be_restored()
    {
        $this->product->delete();
        $this->product->restore();
        
        $this->assertDatabaseHas('products', [
            'id' => $this->product->id,
            'deleted_at' => null,
        ]);
    }

    public function test_product_validation_rules()
    {
        // Test that required fields are validated
        $this->expectException(\Illuminate\Database\QueryException::class);
        
        Product::create([
            'name' => null, // Required field
            'price' => 100,
        ]);
    }

    public function test_product_sku_must_be_unique()
    {
        $sku = 'UNIQUE-SKU-123';
        
        Product::factory()->create(['sku' => $sku]);
        
        $this->expectException(\Illuminate\Database\QueryException::class);
        
        Product::factory()->create(['sku' => $sku]);
    }

    public function test_product_barcode_must_be_unique_when_provided()
    {
        $barcode = '1234567890123';
        
        Product::factory()->create(['barcode' => $barcode]);
        
        $this->expectException(\Illuminate\Database\QueryException::class);
        
        Product::factory()->create(['barcode' => $barcode]);
    }
}
